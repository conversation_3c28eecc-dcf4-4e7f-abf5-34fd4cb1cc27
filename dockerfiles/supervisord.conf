[supervisord]
nodaemon=true

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:nginx]
command = /usr/sbin/nginx -g "daemon off;"
user = root
autostart = true

[program:php-fpm]
command = /usr/sbin/php-fpm8.3 -c /etc/php/8.3/fpm/php-fpm.conf
user = root
autostart = true

[program:cron]
command = /usr/sbin/cron -f
autostart = true

[program:skribble-learn-logging]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan log:process
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/laravel_logging.log

[program:skribble-learn-event-listeners]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work event-listeners --sleep=1 --tries=1 --queue=event-listeners --timeout=60 --max-jobs=500 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-push-notifications]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work push-notifications --sleep=1 --tries=1 --queue=push-notifications --timeout=15 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-results-posting]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work results-posting --sleep=1 --tries=1 --queue=results-posting --timeout=60 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=5
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-grading-framework]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work grading-framework --sleep=1 --tries=1 --queue=grading-framework --timeout=60 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=5
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-audit]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work audit --sleep=1 --tries=1 --queue=audit --timeout=15 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-materialized-views]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work materialized-views --sleep=1 --tries=1 --queue=materialized-views --timeout=15 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-attendance-posting]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work attendance-posting --sleep=1 --tries=1 --queue=attendance-posting --timeout=15 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-report-card]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work report-card --sleep=1 --tries=1 --queue=report-card --timeout=180 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=5
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-enrollment]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work enrollment --sleep=1 --tries=1 --queue=enrollment --timeout=15 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log

[program:skribble-learn-duplicate-semester-setting]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work duplicate-semester-setting --sleep=1 --tries=1 --queue=duplicate-semester-setting --timeout=30 --max-jobs=100 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel_worker.log
