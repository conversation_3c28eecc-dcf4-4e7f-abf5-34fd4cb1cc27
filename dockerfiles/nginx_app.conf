server {
    listen 80 reuseport;

    server_name localhost;
    aio threads;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    root /var/www/public;

    index index.html index.php;

    access_log on;
    error_log /var/log/nginx/error.log;

    charset utf-8;

    client_max_body_size 32M;
    client_body_buffer_size 12K;
    client_header_buffer_size 2k;
    client_body_timeout 90;
    client_header_timeout 60;

    reset_timedout_connection on;

    location / {
        try_files $uri $uri/ /index.html /index.php?$query_string;
    }

    location = /favicon.ico { log_not_found off; access_log off; }
    location = /robots.txt  { log_not_found off; access_log off; }

    error_page 404 /index.php;

    location ~ ^/(index|app|app_dev|config)\.php(/|$) {
        try_files $uri /index.php =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        include fastcgi.conf;
    }

    # Deny .htaccess file access
    location ~ /\.ht {
        deny all;
    }
}
