user  www-data;
worker_processes  auto;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

worker_rlimit_nofile 100000;

events {
    worker_connections  8048;

    # essential for linux, optmized to serve many clients with each thread
    use epoll;

    # Accept as many connections as possible, after nginx gets notification about a new connection.
    # May flood worker_connections, if that option is set too low.
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
    ssl_prefer_server_ciphers on;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log off;
    error_log /var/log/nginx/error.log;

    sendfile on;
    tcp_nopush on;
    keepalive_timeout 65;
    server_tokens off;
    # types_hash_max_size 2048;

    gzip on;
    gzip_disable "msie6";
    gzip_min_length 1000;
    gzip_proxied    expired no-cache no-store private auth;
    gzip_http_version 1.1;
    gzip_types      text/plain application/json application/xml application/javascript text/css;

    client_body_buffer_size 64k;

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;

    fastcgi_read_timeout 360;

}
