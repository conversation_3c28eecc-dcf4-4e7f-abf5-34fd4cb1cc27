@extends('reports.layout')

@section('content')
    @foreach($orders as $order)
        <p>{{__('general.receipt_no')}} : {{$order->items->first()->billingDocumentLineItem?->billingDocument->reference_no}}</p>
        <table>
            <thead>
            <tr>
                <td>{{__('general.student_name')}} : {{$order->recipientStudentClass?->student->getFormattedTranslations('name')}}</td>
                <td>{{__('general.payment_method')}} : {{$order->items->first()->billingDocumentLineItem?->billingDocument->payments[0]->paymentMethod->name}}</td>
            </tr>
            <tr>
                <td>{{__('general.student_no')}} : {{$order->recipientStudentClass?->student->student_number}}</td>
                <td>{{__('general.payment_date')}} : {{$order['created_at']->setTimezone(config('school.timezone'))->format('d M Y, h:i A')}}</td>
            </tr>
            <tr>
                <td>{{__('general.class')}} : {{$order->recipientStudentClass?->semesterClass?->classModel?->name}}</td>
                <td></td>
            </tr>
            </thead>
        </table>

        <div style="height:10px"></div>

        <table>
            <thead>
            <tr>
                <th>{{__('general.sn')}}</th>
                <th>{{__('general.item_name')}}</th>
                <th>{{__('general.unit_price')}} ({{$order->currency_code}})</th>
                <th>{{__('general.quantity')}}</th>
                <th>{{__('general.total')}} ({{$order->currency_code}})</th>
            </tr>
            </thead>

            <tbody>
            @foreach($order->items as $item)
                <tr>
                    <td>{{$loop->iteration}}</td>
                    <td>{{$item->product_name}}</td>
                    <td>{{number_format($item->product_unit_price,2)}}</td>
                    <td>{{$item->quantity}}</td>
                    <td>{{ bcadd($item->amount_before_tax, $item->tax_amount, 2)}}</td>
                </tr>
            @endforeach
            <tr>
                <td colspan="4">{{__('general.total')}}</td>
                <td>{{number_format($order->amount_after_tax, 2)}}</td>
            </tr>
            </tbody>
        </table>

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection
