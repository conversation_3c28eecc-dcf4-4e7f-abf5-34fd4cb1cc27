@extends('reports.layout')

@section('content')
    @php
        $currency = $orders['currency']->symbol;
    @endphp

    @foreach ($orders['orders'] as $merchant_name => $merchant_data)
        <h3 style="margin-top: 25px">{{ $merchant_name }}</h3>
        <table>
            <thead>
                <tr>
                    <th>{{__('ecommerce.meal_date')}}</th>
                    <th>{{__('general.day')}}</th>
                    <th>{{__('general.total_sales')}} ({{ $currency }})</th>
                    <th>{{__('general.total_item_sold')}}</th>
                    <th>{{__('general.total_receipt')}}</th>
                    <th>{{__('general.avg_per_item')}} ({{ $currency }})</th>
                    <th>{{__('general.avg_per_receipt')}} ({{ $currency }})</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($merchant_data['orders'] as $merchant_order)
                    <tr>
                        <td style="white-space: nowrap;">{{ \Carbon\Carbon::parse($merchant_order['meal_date'])->format('d F Y') }}</td>
                        <td>{{ \Carbon\Carbon::parse($merchant_order['meal_date'])->englishDayOfWeek }}</td>
                        <td>{{ number_format($merchant_order['total_sales'], 2) }}</td>
                        <td>{{ $merchant_order['total_item_sold'] }}</td>
                        <td>{{ $merchant_order['total_receipt'] }}</td>
                        <td>{{ number_format($merchant_order['avg_per_item'], 2) }}</td>
                        <td>{{ number_format($merchant_order['avg_per_receipt'], 2) }}</td>
                    </tr>
                @endforeach
                <tr>
                    <td colspan="2">{{__('general.total')}}</td>
                    <td>{{ number_format($merchant_data['merchant_total_sales'], 2) }}</td>
                    <td>{{ $merchant_data['merchant_total_item_sold'] }}</td>
                    <td>{{ $merchant_data['merchant_total_receipt'] }}</td>
                    <td>{{ number_format($merchant_data['merchant_avg_per_item'], 2) }}</td>
                    <td>{{ number_format($merchant_data['merchant_avg_per_receipt'], 2) }}</td>
                </tr>
            </tbody>
        </table>
    @endforeach
@endsection
