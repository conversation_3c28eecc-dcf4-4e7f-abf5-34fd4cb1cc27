@extends('reports.layout')

@section('content')
    @foreach($data as $date)
        <div style="text-align: center; margin-bottom: 10px;">
            <h2>{{__('ecommerce.canteen_report_by_class')}}</h2>
            <h4>{{__('general.order_for')}} : {{$date['date']->format('d M Y')}}</h4>
            <h4>{{__('general.print_date')}} : {{now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A')}}</h4>
        </div>

        @foreach($date['classes'] as $class)
            <p style="padding:15px; background-color:#CCCCCC; text-align: center;">{{__('general.class')}} : {{$class['class_name']}} ({{$class['quantity']}} {{__('general.unit')}})</p>

            @foreach($class['order_items'] as $item)
                <p style="font-weight: bold">{{$item['product_name']}} ({{$item['quantity']}} {{__('general.unit')}})</p>
                <table>
                    @foreach(array_chunk($item['students'],2) as $students)
                        <tr>
                            <td style="width:50%">{{$students[0]['name']}} ({{$students[0]['quantity']}} {{__('general.unit')}})</td>
                            @if(isset($students[1]))
                                <td style="width:50%">{{$students[1]['name']}} ({{$students[1]['quantity']}} {{__('general.unit')}})</td>
                            @else
                                <td style="width:50%"></td>
                            @endif

                        </tr>
                    @endforeach
                </table>
            @endforeach
        @endforeach

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection
