@extends('reports.layout')

@section('content')
    <table>
        <thead>
            @php
                $currency = $orders['currency']->symbol;
            @endphp
            <tr>
                <th>{{__('ecommerce.meal_date')}}</th>
                <th>{{__('general.day')}}</th>
                <th>{{__('general.total_sales')}} ({{ $currency }})</th>
                <th>{{__('general.total_item_sold')}}</th>
                <th>{{__('general.total_receipt')}}</th>
                <th>{{__('general.avg_per_item')}} ({{ $currency }})</th>
                <th>{{__('general.avg_per_receipt')}} ({{ $currency }})</th>
            </tr>
        </thead>

        <tbody>

            @foreach ($orders['orders'] as $order)
                <tr>
                    <td style="white-space: nowrap;">{{ \Carbon\Carbon::parse($order['meal_date'])->format('d F Y') }}</td>
                    <td>{{ \Carbon\Carbon::parse($order['meal_date'])->englishDayOfWeek }}</td>
                    <td>{{ number_format($order['total_sales'], 2) }}</td>
                    <td>{{ $order['total_item_sold'] }}</td>
                    <td>{{ $order['total_receipt'] }}</td>
                    <td>{{ number_format($order['avg_per_item'], 2) }}</td>
                    <td>{{ number_format($order['avg_per_receipt'], 2) }}</td>
                </tr>
            @endforeach

            <tr>
                <td colspan="2">{{__('general.total')}}</td>
                <td>{{ number_format($orders['merchant_total_sales'], 2) }}</td>
                <td>{{ $orders['merchant_total_item_sold'] }}</td>
                <td>{{ $orders['merchant_total_receipt'] }}</td>
                <td>{{ number_format($orders['merchant_avg_per_item'], 2) }}</td>
                <td>{{ number_format($orders['merchant_avg_per_receipt'], 2) }}</td>
            </tr>
        </tbody>
    </table>
@endsection
