@extends('reports.layout')

@section('content')
    @foreach($data['classes'] as $class)
        <div style="text-align: center; margin-bottom: 10px;">
            <h2>{{__('ecommerce.canteen_report_by_class')}} {{$class['class_name']}}</h2>
            <h4>{{__('general.order_for')}} : {{$start_date->format('d M Y')}} - {{$end_date->format('d M Y')}}</h4>
            <h4>{{__('general.print_date')}} : {{now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A')}}</h4>
        </div>
        <table>
            <thead>
            <tr>
                <td>{{__('general.sn')}}</td>
                <td>{{__('general.student_name')}}</td>
                @foreach($data['dates'] as $period)
                    <td>{{$period->format('D')}} ({{$period->format('d M Y')}})</td>
                @endforeach
            </tr>
            </thead>

            <tbody>
            @foreach($class['students'] as $student)
                <tr>
                    <td>{{$loop->iteration}}</td>
                    <td>{{$student['name']}}</td>
                    @foreach($student['dates'] as $date)
                        <td>
                            @foreach($date['order_items'] as $item)
                                {{$item['product_name']}} (x{{$item['quantity']}})<br>
                            @endforeach
                        </td>
                    @endforeach
                </tr>
            @endforeach
            </tbody>
        </table>

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection
