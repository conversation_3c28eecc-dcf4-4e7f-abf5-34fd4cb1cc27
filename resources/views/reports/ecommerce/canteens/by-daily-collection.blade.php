@extends('reports.layout')

@section('content')
    <table style="margin-bottom: 20px">
        <tr>
            <th>{{ __('general.date') }}:</th>
            <th>{{ $date }}</th>
        </tr>
        <tr>
            <th>{{__('general.total_collection')}} :</th>
            <th>{{ number_format($orders['total_collection'], 2) }}</th>
        </tr>
    </table>

    @php
        $currency = $orders['currency']->symbol;
    @endphp

    @foreach ($orders['orders'] as $merchant_name => $merchant_data)
        <h3 style="margin-top: 30px">{{ $merchant_name }}</h3>
        <table>
            <thead>
            <tr>
                <th>{{__('general.item_name')}}</th>
                <th>{{__('general.unit_price')}} ({{ $currency }})</th>
                <th>{{__('general.quantity')}}</th>
                <th>{{__('general.total')}} ({{ $currency }})</th>
            </tr>
            </thead>

            <tbody>
            @foreach ($merchant_data['orders'] as $merchant_order)
                <tr>
                    <td style="white-space: nowrap;">{{ $merchant_order['product_name'] }}</td>
                    <td>{{ number_format($merchant_order['unit_price'], 2) }}</td>
                    <td>{{ $merchant_order['quantity'] }}</td>
                    <td>{{ number_format($merchant_order['total_price'], 2) }}</td>
                </tr>
            @endforeach
            <tr>
                <td colspan="2">{{__('general.total')}}</td>
                <td>{{ $merchant_data['total_group_quantity'] }}</td>
                <td>{{ number_format($merchant_data['total_group_price'], 2) }}</td>
            </tr>
            </tbody>
        </table>
    @endforeach
@endsection
