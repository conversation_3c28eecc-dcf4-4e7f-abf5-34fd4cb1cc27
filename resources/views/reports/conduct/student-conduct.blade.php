@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h1 class="mb-4"> {{ __($semester) }} Student Conduct Report</h1>
        <p><strong> {{ __('general.print_date') }} : {{ now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A') }} </strong> </p>

    </div>

    <div>
        <p style="text-align: left; width:49%; display: inline-block;">  {{ __('general.class') }} : {{ __($class) }}  </p>
        <p style="text-align: right; width:50%;  display: inline-block;">  {{ __('general.grade') }} : {{ __($grade) }} </p>
    </div>



    <table>
        <thead>
            <th colspan="3"></th>
            <th style="text-align:center;"> {{ __('general.class_guardian') }} </th>
            <th style="text-align:center;" colspan="6">{{ __('general.teacher') }} </th>
            <th colspan="5"> </th>
        <tr style="font-size:10px">
            <th>{{ __('general.no') }}</th>
            <th style="width:10%">{{ __('general.student_no') }}</th>
            <th style="width:8%">{{ __('general.name') }} </th>
            <th style="width:8%; text-align:center;">{{ __( $homeroom_teacher['name']) }} </th>
            
            @foreach( $other_teachers as $teacher)
                <th style="width:8%; text-align:center;">{{ __( $teacher['name']) }} </th>
            @endforeach
            <th>{{ __( 'general.average') }} </th>    
            <th style="text-align:center;" >    班导师分数 33%  </th>
            <th style="text-align:center;">    教师分数 67%    </th>
            <th style="width:4%; text-align:center;">    功 Merit        </th>
            <th style="text-align:center;">    过 Demerit      </th>
            <th style="text-align:center;">    实得分数 100%   </th>
        </tr>
        </thead>

        <tbody>
            @foreach ($data as $item)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td style="text-align:center;">{{ $item['student_number'] }}</td>
                <td>{{ $item['name_zh'].$item['name_en']}}</td>
                <td style="text-align:center;">{{ $item['homeroom_teacher_marks'] }}</td>

                @foreach ( $other_teachers as $teacher)
                    <td style="text-align:center;">{{ $item['other_teacher_marks'][$teacher['id']]['marks'] }}</td>
                @endforeach
                <td style="text-align:center;">{{ $item['other_teacher_average'] }}</td>
                <td style="text-align:center;">{{ $item['homeroom_teacher_percentage'] }}</td>
                <td style="text-align:center;">{{ $item['other_teacher_percentage'] }}</td>
                <td style="text-align:center;">{{ $item['merit_score'] }}</td>
                <td style="text-align:center;">{{ $item['demerit_score'] }}</td>
                <td style="text-align:center;">{{ $item['conduct_grade'] }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
@endsection
