@php use App\Helpers\ConfigHelper;use Illuminate\Support\Arr; @endphp
@extends('reports.layout')
@section('content')

    @php
        $available_locales = ConfigHelper::getAvailableLocales();
    @endphp
    <table>
        <thead>
        <tr>
            <th>{{__('general.number')}}</th>
            @foreach($available_locales as $locale)
                <th>{{__('general.name')}} ({{$locale}})</th>
            @endforeach

            <th>{{__('general.trainer_no')}}</th>
            <th>{{__('general.phone_no')}}</th>
            <th>{{__('general.department')}}</th>
            <th>{{__('general.card_number')}} 1</th>
            <th>{{__('general.card_number')}} 2</th>
            <th>{{__('general.card_number')}} 3</th>
            <th>{{__('general.class')}}</th>
        </tr>
        </thead>

        <tbody>
        @foreach($data as $trainer)
            @php
                $class_names = $trainer->classSubjects
                        ->pluck('semesterClass.classModel.name')
                        ->filter()
                        ->toArray();
            @endphp

            <tr>
                <td>{{$loop->iteration}}</td>
                @foreach($available_locales as $locale)
                    <td>{{$trainer->getTranslation('name', $locale)}}</td>
                @endforeach
                <td>{{$trainer->contractor_number}}</td>
                <td>{{$trainer->phone_number}}</td>
                <td>{{$trainer->department->value}}</td>
                <td>{{$trainer->firstActiveCard?->card_number}}</td>
                <td>{{$trainer->firstActiveCard?->card_number2}}</td>
                <td>{{$trainer->firstActiveCard?->card_number3}}</td>
                <td>{{implode(', ',$class_names)}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection

