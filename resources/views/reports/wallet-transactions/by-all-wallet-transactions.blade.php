@extends('reports.layout')
@section('content')
    <table>
        <thead>
        <tr>
            <th>{{__('general.reference_no')}}</th>
            <th>{{__('general.user_type')}}</th>
            <th>{{__('general.user_name')}}</th>
            <th>{{__('general.user_no')}}</th>
            <th>{{__('general.card_number')}}</th>
            <th>{{__('general.type')}}</th>
            <th>{{__('general.status')}}</th>
            <th>{{__('general.transaction_amount')}}</th>
            <th>{{__('general.balance_before')}}</th>
            <th>{{__('general.balance_after')}}</th>
            <th>{{__('general.description')}}</th>
            <th>{{__('general.remarks')}}</th>
            <th>{{__('general.created_at')}}</th>
        </tr>
        </thead>
        <tbody>
        @foreach($data as $wallet_transaction)
            <tr>
                <td>{{$wallet_transaction['reference_no']}}</td>
                <th>{{$wallet_transaction['userable']?->getUserTypeDescription()}}</th>
                <th>{{$wallet_transaction['userable']?->getUserName()}}</th>
                <th>{{$wallet_transaction['userable']?->getUserNumber()}}</th>
                <td>{{$wallet_transaction['userable']?->firstActiveCard?->card_number}}</td>
                <td>{{$wallet_transaction['type']}}</td>
                <td>{{$wallet_transaction['status']}}</td>
                <td>{{$wallet_transaction['total_amount']}}</td>
                <td>{{$wallet_transaction['balance_before']}}</td>
                <td>{{$wallet_transaction['balance_after']}}</td>
                <td>{{$wallet_transaction['description']}}</td>
                <td>{{$wallet_transaction['remark'] ?: '-'}}</td>
                <td>{{\Carbon\Carbon::parse($wallet_transaction['created_at'])->setTimezone(config('school.timezone'))}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection



