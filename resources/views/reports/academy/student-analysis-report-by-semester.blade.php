@extends('reports.layout')
@section('content')
    <div style="text-align: center;">
        <h3>
            {{ __('general.print_date') }} : {{ App\Helpers\ReportHelper::getPrintDate() }}
        </h3>
    </div>
    <table class="summary-table">
        <tr>
            <th>{{ __('general.total_school') }}</th>
            <td>{{ isset($data['summary']['total_students']) ? $data['summary']['total_students'] : 0 }}</td>
        </tr>
        <tr>
            <th>{{ __('general.male') }}</th>
            <td>{{ isset($data['summary']['total_male_students']) ? $data['summary']['total_male_students'] : 0 }}</td>
        </tr>
        <tr>
            <th>{{ __('general.female') }}</th>
            <td>{{ isset($data['summary']['total_female_students']) ? $data['summary']['total_female_students'] : 0 }}</td>
        </tr>
        <tr>
            <th>{{ __('general.hostel_student') }}</th>
            <td>{{ isset($data['summary']['total_hostel_students']) ? $data['summary']['total_hostel_students'] : 0 }}</td>
        </tr>
        <tr>
            <th>{{ __('general.non_hostel_student') }}</th>
            <td>{{ isset($data['summary']['total_non_hostel_students']) ? $data['summary']['total_non_hostel_students'] : 0 }}</td>
        </tr>
    </table>

    <table class="summary-by-grade-table">
        @php
            $rows = [
                __('general.gender_or_grade') => 'grade_name',
                __('general.male') => 'total_male_students',
                __('general.female') => 'total_female_students',
                __('general.total_count') => 'total_students',
                __('general.hostel_student') => 'total_hostel_students',
                __('general.non_hostel_student') => 'total_non_hostel_students',
            ];
        @endphp

        @foreach ($rows as $label => $key)
            <tr>
                <th>{{ $label }}</th>
                @foreach ($data['summary_by_grade'] as $summary_by_grade)
                    <td>{{ isset($summary_by_grade[$key]) ? $summary_by_grade[$key] : ($key === 'grade_name' ? '-' : 0) }}</td>
                @endforeach
            </tr>
        @endforeach
    </table>

    @foreach ($data['summary_by_grade'] as $summary_by_grade)
        <b>{{ isset($summary_by_grade['grade_name']) ? $summary_by_grade['grade_name'] : '-' }}</b>
        <table class="summary-by-grade-table">
            <tr>
                <th rowspan="2">{{ __('general.class') }}</th>
                <th colspan="5">{{ __('general.student_analysis') }}</th>
            </tr>
            <tr>
                <th>{{ __('general.male') }}</th>
                <th>{{ __('general.female') }}</th>
                <th>{{  __('general.total_count') }}</th>
                <th>{{ __('general.hostel_student') }}</th>
                <th>{{ __('general.non_hostel_student') }}</th>
            </tr>
            @if(isset($summary_by_grade['classes_data']) && count($summary_by_grade['classes_data']) > 0)
            @foreach ($summary_by_grade['classes_data'] as $class)
                <tr>
                    <td>{{ $class['class_name'] }}</td>
                    <td>{{ $class['total_male_students'] }}</td>
                    <td>{{ $class['total_female_students'] }}</td>
                    <td>{{ $class['total_students'] }}</td>
                    <td>{{ $class['total_hostel_students'] }}</td>
                    <td>{{ $class['total_non_hostel_students'] }}</td>
                </tr>
            @endforeach
            @else
                <tr>
                    <td colspan="6">No class available for this grade</td>
                </tr>
            @endif
        </table>
    @endforeach
@endsection

@push('styles')
    <style>
        .summary-table {
            width: 20%; 
            border: 1px solid black;
        }

        .summary-table, .summary-by-grade-table {
            margin-bottom: 50px;
        }

        .summary-table th, .summary-table td{
            border: none;
            background-color: white;
        }

        .summary-by-grade-table th, .summary-by-grade-table td {
            text-align: center;
        }

        tbody tr:nth-child(even) {
            background-color: white;
        }
    </style>
@endpush