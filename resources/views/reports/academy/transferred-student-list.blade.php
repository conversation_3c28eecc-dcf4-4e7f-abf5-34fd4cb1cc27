@php use App\Helpers\ConfigHelper; @endphp
@extends('reports.layout')
@section('content')
    <div style="text-align: center;">
        <h3>
            {{ __('general.print_date') }} : {{ App\Helpers\ReportHelper::getPrintDate() }}
        </h3>
    </div>
    @php
        $available_locales = ConfigHelper::getAvailableLocales();
        $colspan = count($available_locales) + 3;
    @endphp
    <table>
        <thead>
        <tr>
            <th>{{ __('general.no') }}</th>
            @foreach($available_locales as $locale)
                <th>{{ __('general.student_name') }}</th>
            @endforeach
            <th>{{ __('general.student_number') }}</th>
            <th>{{ __('general.class') }}</th>
        </tr>
        </thead>
        <tbody>
        @if(isset($data))
            @foreach($data as $d)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    @foreach($available_locales as $locale)
                        <td>{{ $d['student_name'][$locale] }}</td>
                    @endforeach
                    <td>{{ $d['student_number'] }}</td>
                    <td>{{ $d['class_name'] }}</td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="{{$colspan}}">{{ __('general.no_data_available') }}</td>
            </tr>
        @endif
        </tbody>
    </table>
@endsection
