<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        @font-face {
            font-family: 'Noto Serif SC';
            font-style: normal;
            font-weight: 200 900;
            font-display: swap;
            src: url({{ asset('fonts/truetype/NotoSerifSC.ttf') }});
        }

        @font-face {
            font-family: 'Noto Sans HK';
            font-style: normal;
            font-weight: 200 900;
            font-display: swap;
            src: url({{ asset('fonts/truetype/NotoSerifHK.ttf') }});
        }

        html, body {
            font-family: 'Noto Serif SC', 'Noto Sans HK', sans-serif;
        }

        body {
            font-size: 10px;
        }

        table, th, td {
            border: 1px solid;
            padding: 7px;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        td.label {
            padding-right: 5px;
        }

        table.no-border,
        table.no-border th,
        table.no-border td {
            border: none;
        }
    </style>
</head>

<body>
@foreach($data_by_student as $data)

    <div style="display:block; width: 100%; height: 100px">&nbsp;</div>

    <div style="text-align: center; font-size:14px">
        <h2 style="margin: 0;"> {{ $data['exam'] }}</h2>
        <h2 style="margin: 0;">成绩报告表</h2>
        <br><br/>
    </div>

    <table class="no-border" style="margin-bottom: 10px;">
        <tbody>
        <tr>
            <td class="label">姓名:</td>
            <td style="width: 40%">  {{ $data['student_name_zh'] }} </td>
            <td class="label">班级 (Class):</td>
            <td style="width: 20%"> {{ $data['class_name'] }} </td>
        </tr>
        <tr>
            <td class="label">Name:</td>
            <td style="width: 40%"> {{ $data['student_name_en'] }} </td>
            <td class="label">学号(Student ID):</td>
            <td style="width: 20%"> {{ $data['student_no'] }} </td>
        </tr>
        </tbody>
    </table>

    <table style="margin-top:30px;">
        <thead>
        <tr style="border: 1px solid;">
            <th style="border: none; text-align: left;">科目</th>
            <th style="border: none; text-align: left;">Subject</th>
            <th style="border: none;">成绩 Result</th>
        </tr>
        </thead>
        <tbody>
        @if(isset($data['scores']))
            @foreach($data['scores'] as $scores)
                <tr>
                    <td style="border: none;width: 30%;">{{ $scores['subject_name_zh'] }}</td>
                    <td style="border: none;">{{ $scores['subject_name_en'] }}</td>
                    <td style="border-top: none; border-bottom: none; width: 20%; text-align: center;">{{ $scores['score'] }}</td>
                </tr>
            @endforeach
        @endif
        </tbody>
    </table>

    <div style="display:block; width: 100%; height: 100px">&nbsp;</div>

    <div>
        <table>
            <thead>
            <tr>
                <th style="width: 30%">校长 Principal</th>
                <th style="width: 30%">班导师 Form Teacher</th>
                <th style="width: 30%">家长/监护人 Parent / Guardian</th>
            </tr>
            </thead>

            <tbody>
            <tr style="height:150px">
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr style="height:30px">
                <td style="text-align: center; vertical-align:bottom"> {{ '('.$data['principal_name'].')' }}</td>
                <td style="text-align: center; vertical-align:bottom"> {{ '('.$data['homeroom_teacher'].')' }}</td>
                <td></td>
            </tr>
            </tbody>
        </table>

        <p style="font-size: 12px;">备注：</p>
        <p style="font-size: 12px;">(1) 各科考试分数满分为100分,及格分数为60分。</p>
        <p style="font-size: 12px;">(2) 期中英文科分数不按等级换算。</p>
        <p style="font-size: 12px;">(3) X: 缺席 </p>
        <p style="font-size: 12px;">(4) -: 免考 </p>
        <br/>
        <p style="font-size: 12px;">发出日期 (Date Issued): {{ now()->setTimezone(config('school.timezone'))->format('Y-m-d') }} </p>
    </div>

    @if(!$loop->last)
        <div style="page-break-after: always"></div>
    @endif
@endforeach
</body>
</html>
