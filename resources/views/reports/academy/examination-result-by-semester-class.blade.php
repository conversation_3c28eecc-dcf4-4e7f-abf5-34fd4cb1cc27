@extends('reports.layout')
@section('content')
    @if(isset($data['class']))
        <div>
            <p class="fs-8" style="font-weight: bold;">{{ __('general.class') . ': ' . $data['class'] }}</p>
        </div>
        <table style="margin-top:2px;">
            <thead>
            <tr>
                @if(isset($data['header']))
                    @foreach($data['header'] as $key => $header)
                        <th style="{{ str_starts_with($key, 'subject') ? ' max-width: 45px; word-wrap: break-word;' : '' }}">{{ $header }}</th>
                    @endforeach
                @endif
            </tr>
            </thead>
            <tbody>
            @if(isset($data['body']))
                @foreach($data['body'] as $body)
                    <tr>
                        @foreach($body as $d)
                            <td>{{ $d }}</td>
                        @endforeach
                    </tr>
                @endforeach
            @endif
            </tbody>
        </table>
    @endif
@endsection

@push('styles')
    <style>
        th, td {
            text-align: center;
            background-color: white;
            font-size: 8px;
            padding: 2px;
        }

        tbody tr:nth-child(even) {
            background-color: white;
        }

        h2 {
            font-size: 10px;
        }

        h4, .fs-8 {
            font-size: 8px;
        }
    </style>
@endpush
