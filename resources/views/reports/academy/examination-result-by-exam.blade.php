@extends('reports.layout')
@section('content')
    @if(isset($data['data_by_classes']))
        @foreach($data['data_by_classes'] as $data_by_classes)
            <div>
                <p style="font-weight: bold;">{{ __('general.class') . ': ' . (isset($data_by_classes['class_name']) ? $data_by_classes['class_name'] : '-') }}</p>
            </div>
            <table style="margin-top:20px;">
                <thead>
                    <tr>
                        @if(isset($data_by_classes['header']))
                            @foreach($data_by_classes['header'] as $header)
                                <th>{{ $header }}</th>
                            @endforeach
                        @endif
                    </tr>
                </thead>
                <tbody>
                    @if(isset($data_by_classes['data']))
                        @foreach($data_by_classes['data'] as $students_data)
                            <tr>
                                @foreach ($students_data as $student_data)
                                    <td>{{ $student_data }}</td>
                                @endforeach
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
            @if(!$loop->last)
                <div style="page-break-after: always"></div>
            @endif
        @endforeach
    @endif
@endsection

