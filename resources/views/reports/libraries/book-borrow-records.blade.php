@extends('reports.layout')
@section('content')
    <p>{{__('library.book_title')}} : {{$data['book_title']}}</p>
    <p>{{__('library.book_no')}} : {{$data['book_number']}}</p>
    <p>{{__('library.call_no')}} : {{$data['book_call_number']}}</p>

    <table>
        <thead>
        <tr>
            <th>{{__('general.number')}}</th>
            <th>{{__('general.member_no')}}</th>
            <th>{{__('general.name')}}</th>
            <th>{{__('general.class')}}</th>
            <th>{{__('library.loan_date')}}</th>
            <th>{{__('general.due_date')}}</th>
            <th>{{__('general.return_date')}}</th>
            <th>{{__('general.balance')}}</th>
        </tr>
        </thead>

        <tbody>
        @foreach($data['book_loans'] as $book_loan)
            <tr>
                <td>{{$loop->iteration}}</td>
                <td>{{$book_loan['member_number']}}</td>
                <td>{{$book_loan['member_name']}}</td>
                <td>{{$book_loan['class_name']}}</td>
                <td>{{$book_loan['loan_date']->format('Y-m-d')}}</td>
                <td>{{$book_loan['due_date']->format('Y-m-d')}}</td>
                <td>{{$book_loan['return_date']?->format('Y-m-d')}}</td>
                <td>{{$book_loan['balance']}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection

