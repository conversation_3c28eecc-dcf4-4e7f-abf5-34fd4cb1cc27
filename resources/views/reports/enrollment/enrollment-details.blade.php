@php
    use App\Enums\GuardianType;
    use App\Helpers\ConfigHelper;
    use App\Helpers\ReportHelper;

    $locales = ConfigHelper::getAvailableLocales();
@endphp

@extends('reports.layout')

@section('content')
    <div style="text-align: center; margin-bottom: 15px;">
        <h2 style="padding: 0px; margin: 0px;">
            {{ __('semester_class.student_details_report') }} ({{ $enrollment->enrollmentSession?->admission_year }})
        </h2>
        <h4 style="padding: 0px; margin: 0px;">{{ __('general.print_date') }} : {{ ReportHelper::getPrintDate() }}</h4>
    </div>

    <h2 style="width: 100%; border-bottom: 1px solid black;">{{ __('general.student_information') }}</h2>

    <table width="100%" cellspacing="0" cellpadding="2">
        <tr>
            {{-- Student Name --}}
            <td width="19%">{{ __('general.student_name') }}</td>
            <td width="1%" style="text-align: center;">:</td>
            <td width="30%">
                @foreach ($locales as $locale)
                    {{ $enrollment->getTranslation('name', $locale) }} {{ ' ' }}
                @endforeach
            </td>
            {{-- Primary School --}}
            <td width="19%">{{ __('general.primary_school') }}</td>
            <td width="1%" style="text-align: center;">:</td>
            <td width="30%">{{ $enrollment->primarySchool?->name }}</td>
        </tr>
        <tr>
            {{-- Admission Year --}}
            <td>{{ __('general.admission_year') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->enrollmentSession?->admission_year }}</td>
            {{-- Admission Grade --}}
            <td>{{ __('general.admission_grade') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->admissionGrade?->name }}</td>
        </tr>
        <tr>
            {{-- Admission Type --}}
            <td>{{ __('general.admission_type') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->admission_type }}</td>
            {{-- Join Date --}}
            <td>{{ __('general.join_date') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->registration_date }}</td>
        </tr>
        <tr>
            {{-- Enrollment Status --}}
            <td>{{ __('general.enrollment_status') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->enrollment_status }}</td>
            {{-- Birth Cert Number --}}
            <td>{{ __('general.birth_cert_number') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->birth_cert_number }}</td>
        </tr>
        <tr>
            {{-- NRIC --}}
            <td>{{ __('general.nric') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->nric }}</td>
            {{-- Passport Number --}}
            <td>{{ __('general.passport_number') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->passport_number }}</td>
        </tr>
        <tr>
            {{-- Gender --}}
            <td>{{ __('general.gender') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->gender->label() }}</td>
            {{-- Race --}}
            <td>{{ __('general.race') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->race?->name }}</td>
        </tr>
        <tr>
            {{-- Religion --}}
            <td>{{ __('general.religion') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->religion?->name }}</td>
            {{-- Date Of Birth --}}
            <td>{{ __('general.date_of_birth') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->date_of_birth }}</td>
        </tr>
        <tr>
            {{-- Email --}}
            <td>{{ __('general.email') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->email }}</td>
            {{-- Phone Number --}}
            <td>{{ __('general.phone_number') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->phone_number }}</td>
        </tr>
        <tr>
            {{-- Birthplace --}}
            <td>{{ __('general.birthplace') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->birthplace }}</td>
            {{-- Nationality --}}
            <td>{{ __('general.nationality') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->nationality?->name }}</td>
        </tr>
        <tr>
            {{-- Address --}}
            <td>{{ __('general.address') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->address }}</td>
            {{-- Postal Code --}}
            <td>{{ __('general.postal_code') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->postal_code }}</td>
        </tr>
        <tr>
            {{-- City --}}
            <td>{{ __('general.city') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->city }}</td>
            {{-- State --}}
            <td>{{ __('general.state') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->state?->name }}</td>
        </tr>
        <tr>
            {{-- Country --}}
            <td>{{ __('general.country') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->country?->name }}</td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            {{-- Staying In Hostel --}}
            <td>{{ __('general.staying_in_hostel') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->is_hostel ? __('general.yes_affirmation') : __('general.no_affirmation') }}</td>
            {{-- Have Siblings --}}
            <td>{{ __('general.have_siblings') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->have_siblings ? __('general.yes_affirmation') : __('general.no_affirmation') }}</td>
        </tr>
        <tr>
            {{-- Dietary Restriction --}}
            <td>{{ __('general.dietary_restriction') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->dietary_restriction }}</td>
            {{-- Health Concern --}}
            <td>{{ __('general.health_concern') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->healthConcern?->name }}</td>
        </tr>
        <tr>
            {{-- Remarks --}}
            <td>{{ __('general.remarks') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ $enrollment->remarks }}</td>
            {{-- Exam Slip Number --}}
            <td>{{ __('general.exam_slip_number') }}</td>
            <td style="text-align: center;">:</td>
            <td>{{ join(', ', $enrollment->enrollmentExams->pluck('exam_slip_number')->toArray()) }}</td>
        </tr>
    </table>

    @php
        $guardians = $enrollment->guardians
            ->filter(function ($guardian) {
                // exclude the guardian with type 'Guardian'
                return $guardian->guardian_type !== GuardianType::GUARDIAN->value;
            })
            ->sortBy('guardian_type')
            ->keyBy('guardian_type');
    @endphp

    @foreach ($guardians as $type => $guardian)
        <h3 style="padding:5px; background-color:#CCCCCC; margin-top: 15px; margin-bottom: 10px;">
            {{ __('general.student_' . strtolower($type) . '_information') }}
        </h3>

        <table width="100%" cellspacing="0" cellpadding="2">
            <tr>
                {{-- Guardian Name --}}
                <td width="19%">{{ __('general.name') }}</td>
                <td width="1%" style="text-align: center;">:</td>
                <td width="30%">
                    @foreach ($locales as $locale)
                        {{ $guardian->getTranslation('name', $locale) }} {{ ' ' }}
                    @endforeach
                </td>
                {{-- Guardian Live Status --}}
                <td>{{ __('general.live_status') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->live_status }}</td>
            </tr>
            <tr>
                {{-- Guardian Email --}}
                <td>{{ __('general.email') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->email }}</td>
                {{-- Guardian Phone Number --}}
                <td width="19%">{{ __('general.phone_number') }}</td>
                <td width="1%" style="text-align: center;">:</td>
                <td width="30%">{{ $guardian->phone_number }}</td>
            </tr>
            <tr>
                {{-- Guardian NRIC --}}
                <td>{{ __('general.nric') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->nric }}</td>
                {{-- Guardian Passport Number --}}
                <td>{{ __('general.passport_number') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->passport_number }}</td>

            </tr>
            <tr>
                {{-- Guardian Nationality --}}
                <td>{{ __('general.nationality') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->nationality?->name }}</td>
                {{-- Guardian Race --}}
                <td>{{ __('general.race') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->race?->name }}</td>
            </tr>
            <tr>
                {{-- Guardian Religion --}}
                <td>{{ __('general.religion') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->religion?->name }}</td>
                {{-- Guardian Education --}}
                <td>{{ __('general.education') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->education?->name }}</td>
            </tr>
            <tr>
                {{-- Guardian Marital Status --}}
                <td>{{ __('general.marital_status') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->married_status }}</td>
                {{-- Guardian Primary Guardian --}}
                <td>{{ __('general.primary_guardian') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->is_primary ? __('general.yes_affirmation') : __('general.no_affirmation') }}</td>
            </tr>
            <tr>
                {{-- Guardian Occupation --}}
                <td>{{ __('general.occupation') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->occupation }}</td>
                {{-- Guardian Occupation Description --}}
                <td>{{ __('general.occupation_description') }}</td>
                <td style="text-align: center;">:</td>
                <td>{{ $guardian->occupation_description }}</td>
            </tr>
        </table>
    @endforeach
@endsection

@push('styles')
    <style>
        table {
            width: 100%;
            font-size: 14px;
            border-collapse: collapse;
            page-break-inside: avoid;
        }

        tbody tr:nth-child(even) {
            background-color: white;
        }

        td {
            vertical-align: top;
            padding: 3px;
            border: none !important;
        }
    </style>
@endpush
