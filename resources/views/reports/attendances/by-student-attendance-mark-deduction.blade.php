@extends('reports.layout')

@php
    $locale = app()->getLocale();
@endphp
@section('content')
    <div style="text-align: center; margin-bottom: 2rem;">
        <h3 style="margin: 1px;">
            @php
                $date_from = Carbon\Carbon::parse($date_from);
                $date_to = Carbon\Carbon::parse($date_to);

                if ($locale === 'zh') {
                    $date_from = $date_from->locale('zh')->translatedFormat('Y年MdS');
                    $date_to = $date_to->locale('zh')->translatedFormat('Y年MdS');
                } else {
                    $date_from = $date_from->format('F j, Y');
                    $date_to = $date_to->format('F j, Y');
                }
            @endphp
            {{ __('attendance.mark_deduct_report', [
                'date_from' => $date_from,
                'date_to' => $date_to,
            ]) }}
        </h3>
        <h5 style="margin: 2px;">
            {{ __('general.print_date') }} : {{ App\Helpers\ReportHelper::getPrintDate() }}
        </h5>
    </div>
    @foreach ($data as $class_data)
        @foreach ($class_data['students'] as $student)
            <div style="margin-bottom: 4rem; page-break-inside: avoid;">

                <table style="width: 100%; margin-bottom: 10px; font-size: 14px; font-weight: bold;">
                    <tr>
                        <td style="width: 29%; border: none; padding: 0;">{{ __('general.class') }} : {{ $student['class'][$locale] }}</td>
                        <td style="width: 29%; border: none; padding: 0; text-align: center;">{{ __('general.student_no') }} : {{ $student['student_number'] }}</td>
                        <td style="width: 42%; border: none; padding: 0; text-align: right;">{{ __('general.student_name') }} : {{ $student['student_name'][$locale] }}</td>
                    </tr>
                </table>

                <table style="margin-bottom: 2rem;">
                    <thead>
                    <tr>
                        <th>{{ __('general.date') }}</th>
                        <th>{{ __('general.attendance') }}  {{ __('general.category') }}</th>
                        <th>{{ __('general.period') }}</th>
                        <th>{{ __('general.deduct_average_point') }}</th>
                        <th>{{ __('general.remarks') }}</th>
                    </tr>
                    </thead>
                    @php
                        $total_period = 0;
                        $total_deduct_average_point = 0;
                    @endphp
                    <tbody>
                    @foreach ($student['attendances'] as $attendance)
                        @php
                            $total_period += $attendance['periods'];
                            $total_deduct_average_point += round($attendance['total_deduct_average_point'], 2);
                        @endphp
                        <tr>
                            <td>{{ $attendance['date'] }}</td>
                            <td>{{ $attendance['type'] }}</td>
                            <td>{{ $attendance['periods'] }}</td>
                            <td>{{ number_format($attendance['total_deduct_average_point'], 2) }}</td>
                            <td>{{ $attendance['reason'] ?? '' }}</td>
                        </tr>
                    @endforeach
                    <tr>
                        <td colspan="2">Total</td>
                        <td>{{ $total_period }}</td>
                        <td>{{ number_format($total_deduct_average_point, 2) }}</td>
                        <td></td>
                    </tr>
                    </tbody>
                </table>

                <table style="width: 100%; margin-bottom: 1rem; font-size: 14px; font-weight: bold;">
                    <tr>
                        <td style="width: 49%; border: none; padding: 0; text-align: left;">{{ __('general.signature') }} : ___________________________________________</td>
                        <td style="width: 49%; border: none; padding: 0; text-align: right;">{{ __('general.date') }} : ___________________________________________</td>
                    </tr>
                </table>
            </div>
        @endforeach
    @endforeach
@endsection
