@php use App\Helpers\ConfigHelper; @endphp
@extends('reports.layout')
@section('content')
    <div style="text-align: center;">
        <h3>
            {{ __('general.Trainer Attendance (Cocurriculum)') }} - {{ $type === 'attend_only' ? __('general.attend_only') : __('general.all_trainers') }} ({{ $date }})
            <br />
            {{ __('general.print_date') }} : {{ App\Helpers\ReportHelper::getPrintDate() }}
        </h3>
    </div>
    @php
        $available_locales = ConfigHelper::getAvailableLocales();
    @endphp
    <table>
        <thead>
        <tr>
            <th>{{ __('general.no') }}</th>
            <th>{{ __('general.contractor_name') }}</th>
            <th>{{ __('general.society') }}</th>
            <th>{{ __('general.attendance') }}</th>
            <th>{{ __('general.check_in') }}</th>
            <th>{{ __('general.check_out') }}</th>
        </tr>
        </thead>
        <tbody>
        @if(isset($data) && count($data) > 0)
            @foreach($data as $index => $d)
                <tr>
                    <td>{{ ($index+1) }}</td>
                    <td>{{ strtoupper($d->contractor_name) }}</td>
                    <td>{{ $d->class_name }}</td>
                    <td>{{ strtoupper(__('general.' . strtolower($d->attendance_status))) }}</td>
                    <td>{{ $d->check_in_datetime !== null ? \Carbon\Carbon::parse($d->check_in_datetime, 'UTC')->tz(config('school.timezone'))->format('H:i:s') : '-' }}</td>
                    <td>{{ $d->check_out_datetime !== null ? \Carbon\Carbon::parse($d->check_out_datetime, 'UTC')->tz(config('school.timezone'))->format('H:i:s') : '-' }}</td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="6">{{ __('general.no_data_available') }}</td>
            </tr>
        @endif
        </tbody>
    </table>
@endsection
