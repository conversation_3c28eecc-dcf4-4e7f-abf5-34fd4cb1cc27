@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h3 style="margin:0px">{{ __('attendance.student_daily_arrival') }}</h3>
        <h4 style="margin:1px">{{ \Carbon\Carbon::parse($date_from)->format('F j, Y') }} - {{ \Carbon\Carbon::parse($date_to)->format('F j, Y') }}</h4>
        <h5>
            {{ __('general.print_date') }} : {{ App\Helpers\ReportHelper::getPrintDate() }}
        </h5>
    </div>

    @php
        $available_locales = App\Helpers\ConfigHelper::getAvailableLocales();
    @endphp

    <table class="narrow-rows">
        <thead>
            <tr>
                <th>{{ __('general.no') }}</th>
                <th>{{ __('general.class') }}</th>
                <th>{{ __('general.attend') }}</th>
                <th>{{ __('general.late') }}</th>
                <th>{{ __('general.absent') }}</th>
                <th>{{ __('general.total_student') }}</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($data as $current_date => $row)
                <tr>
                    <td colspan="6" style="font-weight: bold;">{{ \Carbon\Carbon::parse($current_date)->format('F j, Y') }} ({{ \Carbon\Carbon::parse($current_date)->locale(app()->getLocale())->getTranslatedDayName() }})</td>
                </tr>
                @php
                    $total_present = 0;
                    $total_late = 0;
                    $total_absent = 0;
                    $total_student_for_current_date = 0;
                @endphp
                @foreach ($row as $class)
                    @php
                        $current_class_total = $class['records']['PRESENT'] + $class['records']['LATE'] + $class['records']['ABSENT'];

                        $total_present += $class['records']['PRESENT'];
                        $total_late += $class['records']['LATE'];
                        $total_absent += $class['records']['ABSENT'];
                        $total_student_for_current_date += $current_class_total;
                    @endphp
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>
                            @foreach ($available_locales as $locale)
                                {{ $class['name'][$locale] ?? '' }} @if (!$loop->last) {{ '/' }} @endif
                            @endforeach
                        </td>
                        <td>{{ $class['records']['PRESENT'] }}</td>
                        <td>{{ $class['records']['LATE'] }}</td>
                        <td>{{ $class['records']['ABSENT'] }}</td>
                        <td>{{ $current_class_total }}</td>
                    </tr>
                @endforeach
                <tr class="bold">
                    <td colspan="2">{{ __('general.total') }}</td>
                    <td>{{ $total_present }}</td>
                    <td>{{ $total_late }}</td>
                    <td>{{ $total_absent }}</td>
                    <td>{{ $total_student_for_current_date }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endsection
