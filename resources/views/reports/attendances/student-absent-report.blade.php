@php use App\Enums\AttendanceStatus; @endphp
@extends('reports.layout')

@section('content')

    <table>
        <thead>
        <tr>
            <th>{{__('general.number')}}</th>
            <th>{{__('attendance.attendance_status')}}</th>
            <th>{{__('general.student_no')}}</th>
            <th>{{__('general.student_name')}} </th>
            <th>{{__('general.class')}}</th>
            <th>{{__('general.date')}}</th>
            <th>{{__('attendance.time_in')}}</th>
            <th>{{__('attendance.time_out')}}</th>
        </tr>
        </thead>

        <tbody>
        @foreach($data as $student)
            <tr>
                @if(!empty($student['present_dates']))
                    <td>{{ $loop->iteration }}</td>
                    <td>{{$student['present_dates'][0]['attendance_status']}}</td>
                    <td>{{$student['student_number']}}</td>
                    <td>{{$student['student_name']}}</td>
                    <td> {{$student['class']}} @if ($is_primary_class_required) ({{ $student['primary_class'] }}) @endif</td>
                    <td style="padding: 0">
                        @foreach($student['present_dates'] as $present_date)
                            <div style="height:18px; text-align:center; padding: 2px; {{ !$loop->last ? 'border-bottom: 1px solid black; ' : '' }}"> {{$present_date['date']}}</div>
                        @endforeach
                    </td>
                    <td style="padding: 0">
                        @foreach($student['present_dates'] as $present_date)
                            <div style="height:18px; text-align:center; padding: 2px; {{ !$loop->last ? 'border-bottom: 1px solid black; ' : '' }}"> {{ $present_date['attendance_time_in'] ?? '-' }}</div>
                        @endforeach
                    </td>
                    <td style="padding: 0">
                        @foreach($student['present_dates'] as $present_date)
                            <div style="height:18px; text-align:center; padding: 2px; {{ !$loop->last ? 'border-bottom: 1px solid black; ' : '' }}"> {{ $present_date['attendance_time_out'] ?? '-' }}</div>
                        @endforeach
                    </td>
                @elseif(!empty($student['absent_dates']))
                    <td>{{ $loop->iteration }}</td>
                    <td>{{$student['absent_dates'][0]['attendance_status']}}</td>
                    <td>{{$student['student_number']}}</td>
                    <td>{{$student['student_name']}}</td>
                    <td> {{$student['class']}} @if ($is_primary_class_required) ({{ $student['primary_class'] }}) @endif</td>
                    <td style="padding: 0">
                        @foreach($student['absent_dates'] as $absent_dates)
                            <div style="height:18px; text-align:center; padding: 2px; {{ !$loop->last ? 'border-bottom: 1px solid black; ' : '' }}"> {{$absent_dates['date']}}</div>
                        @endforeach
                    </td>
                    <td style="padding: 0">
                        @foreach($student['absent_dates'] as $absent_dates)
                            <div style="height:18px; text-align:center; padding: 2px; {{ !$loop->last ? 'border-bottom: 1px solid black; ' : '' }}"> {{ $absent_dates['attendance_time_in'] ?? '-' }}</div>
                        @endforeach
                    </td>
                    <td style="padding: 0">
                        @foreach($student['absent_dates'] as $absent_dates)
                            <div style="height:18px; text-align:center; padding: 2px; {{ !$loop->last ? 'border-bottom: 1px solid black; ' : '' }}"> {{ $absent_dates['attendance_time_out'] ?? '-' }}</div>
                        @endforeach
                    </td>

                @endif
            </tr>
        @endforeach
        </tbody>
    </table>

@endsection
