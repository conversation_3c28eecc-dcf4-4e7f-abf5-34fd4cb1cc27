@php
    use App\Helpers\ConfigHelper;
    use App\Helpers\ReportHelper;
    use Illuminate\Support\Arr;
@endphp

@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h3>{{ now()->year }} {{ __('card.smart_card_import_title') }}</h3>
        <h3>
            {{ __('general.print_date') }} : {{ ReportHelper::getPrintDate() }}
        </h3>
    </div>

    @php
        $available_locales = ConfigHelper::getAvailableLocales();
    @endphp
    <table>
        <thead>
        <tr>
            <th>{{ __('general.student_number') }}</th>
            @foreach ($available_locales as $locale)
                <th>{{ __('general.student_name') }} ({{ $locale }})</th>
            @endforeach
            <th>{{ __('general.class') }}</th>
            <th>{{ __('general.card_number') }}</th>
            <th>{{ __('general.card_number') }} 2</th>
            <th>{{ __('general.card_number') }} 3</th>
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $card)
            <tr>
                <td>{{ $card['student_number'] }}</td>
                @foreach ($available_locales as $locale)
                    <td>{{ optional($card['student_name'])[$locale] }}</td>
                @endforeach
                <td>{{ optional($card['class_name'])['en'] }}</td>
                <td>{{ $card['card_number'] }}</td>
                <td>{{ $card['card_number2'] }}</td>
                <td>{{ $card['card_number3'] }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection
