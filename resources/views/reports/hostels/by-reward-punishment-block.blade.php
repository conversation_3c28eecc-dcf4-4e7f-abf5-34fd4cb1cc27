@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h2 style="margin: 0;">{{ __('hostel.title.reward_punishment_by_block' ,  ['year' => $year]) }}</h2>
        <br>
    </div>

    <table>
        <thead>
        <tr>
            <th>{{ __('general.date') }}</th>
            <th>{{ __('general.block') }}</th>
            <th>{{ __('general.room_number') }}</th>
            <th> {{ __('general.student_no') }}</th>
            <th>{{ __('general.student_name') }} (en)</th>
            <th>{{ __('general.student_name') }} (zh)</th>
            <th>{{ __('general.merit_demerit') }}</th>
            <th>{{ __('general.item') }}</th>
            <th>{{ __('general.person_in_charge') }}</th>
            <th>{{ __('general.remarks') }}</th>
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $hostel_reward_punishment)
            <tr>
                <td>{{ $hostel_reward_punishment['date'] }}</td>
                <td>{{ $hostel_reward_punishment['block_code'] }}</td>
                <td>{{ $hostel_reward_punishment['room'] }}</td>
                <td>{{ $hostel_reward_punishment['student_number']}}</td>
                <td>{{ $hostel_reward_punishment['student_name']['en'] }}</td>
                <td>{{ $hostel_reward_punishment['student_name']['zh'] }}</td>
                <td>{{ $hostel_reward_punishment['merit_demerit_item'] }}</td>
                <td>{{ $hostel_reward_punishment['reward_punishment_item'] }}</td>
                <td>{{ $hostel_reward_punishment['person_in_charge']}}</td>
                <td>{{ $hostel_reward_punishment['remark'] }}</td>         
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection
