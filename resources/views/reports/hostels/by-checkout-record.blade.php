@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h2 style="margin: 0;">{{ __('hostel.title.checkout_record', ['year' => $year]) }}</h2>
        <div style="margin: 0; margin-bottom: 5px;">{{ __('general.print_date') }}
            : {{ now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A') }}</div>
    </div>

    <table>
        <thead>
        <tr>
            <th>{{ __('general.number') }}</th>
            <th>{{ __('general.room') }}</th>
            <th>{{ __('general.bed_number') }}</th>
            <th>{{ __('general.student_no') }}</th>
            <th>{{ __('general.name') }}</th>
            <th>{{ __('general.ic_no') }}</th>
            <th>{{ __('general.phone_no') }}</th>
            <th>{{ __('general.guardian') }}</th>
            <th>{{ __('general.class') }}</th>
            <th>{{ __('general.check_in_date') }}</th>
            <th>{{ __('general.check_out_date') }}</th>
            <th>{{ __('general.remarks') }}</th>
        </tr>
        </thead>

        <tbody>
        @foreach($data as $record)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $record->room_name }}</td>
                <td>{{ $record->bed_name }}</td>
                <td>{{ $record->student_number }}</td>
                <td>{{ optional($record->student_name)['zh'] }} {{ optional($record->student_name)['en'] }}</td>
                <td>{{ $record->nric }}</td>
                <td>{{ $record->phone_number }}</td>
                <td>{{ optional($record->guardian)['zh'] }} {{ optional($record->guardian)['en'] }}</td>
                <td>{{ optional($record->class_name)['en'] }}</td>
                <td>{{ \Carbon\Carbon::parse($record->start_date)->toDateString() }}</td>
                <td>{{ \Carbon\Carbon::parse($record->end_date)->toDateString() }}</td>
                <td>{{ $record->remarks }}</td>
            </tr>
        @endforeach
        </tbody>

    </table>
@endsection
