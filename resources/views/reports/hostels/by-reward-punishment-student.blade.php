@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h2 style="margin: 0;">{{ __('hostel.title.reward_punishment_by_student', ['year' => $year]) }}</h2>
        <div style="margin: 0; margin-bottom: 5px;">
            {{ __('general.print_date') }}: {{ now()->format('d M Y, h:i A') }}
        </div>
        <br>
    </div>

    <table>
        <tr>
            <td style="text-align: left ; border: none">{{ __('general.student_number') }}: {{ $student_number }}</div>
            <td style="text-align: left ; border: none">{{ __('general.name') }}: {{ $name_en }} {{ $name_zh }}</div>
            <td style="text-align: right ; border: none">{{ __('general.room') }}: {{ $room_bed }}</div>
        </tr>
    </table>
    <img src={{$photo}} style="height: 200px;">

    <table>
        <thead>
        <tr>
            <th>{{ __('general.date') }}</th>
            <th>{{ __('general.room_number') }}</th>
            <th>{{ __('general.merit_demerit') }}</th>
            <th>{{ __('general.item') }}</th>
            <th>{{ __('general.person_in_charge') }}</th>
            <th>{{ __('general.deduction_points') }}</th>
            <th>{{ __('general.balance_points') }}</th>
            <th>{{ __('general.remarks') }}</th>
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $hostel_reward_punishment)
            <tr>
                <td>{{ $hostel_reward_punishment['date'] }}</td>
                <td>{{ isset($hostel_reward_punishment['room']) && isset($hostel_reward_punishment['bed']) ? $hostel_reward_punishment['room'] ."/". $hostel_reward_punishment['bed'] : null }}</td>
                <td>{{ $hostel_reward_punishment['merit_demerit_item'] }}</td>
                <td>{{ $hostel_reward_punishment['reward_punishment_item'] }}</td>
                <td>{{ $hostel_reward_punishment['person_in_charge']}}</td>
                <td>{{ $hostel_reward_punishment['deduction_points']}}</td>
                <td>{{ $hostel_reward_punishment['balance']}}</td>
                <td>{{ $hostel_reward_punishment['remark'] }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>

    <p style="font-size: 12px"> {{ __('general.deducted_points') }}: {{ $total_deducted_points }}</p>
    <p style="font-size: 12px"> {{ __('general.balance_points') }}: {{ $total_balance_points }}</p>

@endsection
