@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h2 style="margin: 0;">{{ __('hostel.title.boarders_contact', ['semester_setting' => $semester_setting->name]) }}</h2>
        <div style="margin: 0; margin-bottom: 5px;">{{ __('general.print_date') }} : {{ now()->format('d M Y') }}</div>
    </div>

    <table>
        <thead>
        <tr>
            <th>{{ __('general.room') }}</th>
            <th>{{ __('general.bed') }}</th>
            <th>{{ __('general.student_no') }}</th>
            <th>{{ __('general.student_name') }}</th>
            <th>{{ __('general.class') }}</th>
            <th>{{ __('general.guardian_type') }}</th>
            <th>{{ __('general.name') }}</th>
            <th>{{ __('general.nric') }}</th>
            <th>{{ __('general.phone_number') }}</th>
        </tr>
        </thead>
        <tbody>
        @foreach($data as $student)
            @php
                $guardian_count = count($student['guardians']);
            @endphp

            <tr>
                <td rowspan="{{ max($guardian_count, 1) }}">{{ $student['room_name'] ?? ' ' }}</td>
                <td rowspan="{{ max($guardian_count, 1) }}">{{ $student['bed_name'] ?? ' ' }}</td>
                <td rowspan="{{ max($guardian_count, 1) }}">{{ $student['student_number'] }}</td>
                <td rowspan="{{ max($guardian_count, 1) }}">{{ $student['student_name']['zh'] ?? '' }} {{ $student['student_name']['en'] ?? '' }}</td>
                <td rowspan="{{ max($guardian_count, 1) }}">{{ $student['class_name']['en'] ?? '' }}</td>

                @if($guardian_count > 0)
                    <td>{{ $student['guardians'][0]['pivot']['type'] ?? '' }}</td>
                    <td>{{ $student['guardians'][0]['name']['zh'] ?? '' }} {{ $student['guardians'][0]['name']['en'] ?? '' }}</td>
                    <td>{{ $student['guardians'][0]['nric'] }}</td>
                    <td>{{ $student['guardians'][0]['phone_number'] }}</td>
                @else
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                @endif
            </tr>

            @for($i = 1; $i < $guardian_count; $i++)
                <tr>
                    <td>{{ $student['guardians'][$i]['pivot']['type'] ?? '' }}</td>
                    <td>{{ $student['guardians'][$i]['name']['zh'] ?? '' }} {{ $student['guardians'][$i]['name']['en'] ?? '' }}</td>
                    <td>{{ $student['guardians'][$i]['nric'] }}</td>
                    <td>{{ $student['guardians'][$i]['phone_number'] }}</td>
                </tr>
            @endfor
        @endforeach
        </tbody>
    </table>
@endsection
