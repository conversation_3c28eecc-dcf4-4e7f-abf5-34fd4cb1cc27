@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h2 style="margin: 0;">{{ __('hostel.title.reward_punishment_by_room', ['year' => $year]) }}</h2>
        <div style="margin: 0; margin-bottom: 5px;">
            {{ __('general.print_date') }}: {{ now()->format('d M Y, h:i A') }}
        </div>
        <br>
    </div>

    @foreach ($data as $hostel_reward_punishment_group)
        <table>
            <tr>
                <td style="text-align: left ; border: none">
                    {{ __('general.student_number') }}: {{ $hostel_reward_punishment_group['student_number'] }}
                </td>
                <td style="text-align: left ; border: none">{{ __('general.name') }}: 
                    {{ $hostel_reward_punishment_group['name_en']}} {{ $hostel_reward_punishment_group['name_zh']}} 
                </td>
                <td style="text-align: right ; border: none">{{ __('general.room') }}: {{ $hostel_reward_punishment_group['room_bed']}}
                </td>
            </tr>
        </table>

        <table> 
            <thead>
            <tr>
                <th>{{ __('general.date') }}</th>
                <th>{{ __('general.room_number') }}</th>
                <th>{{ __('general.merit_demerit') }}</th>
                <th>{{ __('general.item') }}</th>
                <th>{{ __('general.person_in_charge') }}</th>
                <th>{{ __('general.deduction_points') }}</th>
                <th>{{ __('general.balance_points') }}</th>
                <th>{{ __('general.remarks') }}</th>
            </tr>
            </thead>

   
            <tbody>
            @foreach ($hostel_reward_punishment_group['table_data'] as $table_data)
                <tr>
                    <td>{{ $table_data['date'] }}</td>
                    <td>{{ isset($table_data['room']) && isset($table_data['bed']) ? $table_data['room'] ."/". $table_data['bed'] : null }}</td>
                    <td>{{ $table_data['merit_demerit_item'] }}</td>
                    <td>{{ $table_data['reward_punishment_item'] }}</td>
                    <td>{{ $table_data['person_in_charge']}}</td>
                    <td>{{ $table_data['deduction_points']}}</td>
                    <td>{{ $table_data['balance']}}</td>
                    <td>{{ $table_data['remark'] }}</td>  
                </tr>
            @endforeach
            </tbody>
        </table>

        <p style="font-size: 12px"> {{ __('general.deducted_points') }}: {{ $hostel_reward_punishment_group['total_deduction_points'] }} </p>
        <p style="font-size: 12px"> {{ __('general.balance_points') }}: {{ $hostel_reward_punishment_group['total_balance_points'] }}  </p>
    @endforeach

@endsection
