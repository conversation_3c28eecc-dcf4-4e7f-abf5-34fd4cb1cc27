@extends('reports.layout')

@section('content')
    <div style="text-align: center;">
        <h2 style="margin: 0;">{{ __('hostel.title.boarders_list') }}</h2>
        <div style="margin: 0; margin-bottom: 5px;">{{ __('general.print_date') }} : {{ now()->format('d M Y') }}</div>
    </div>

    <table>
        <thead>
        <tr>
            <th>{{ __('general.number') }}</th>
            <th>{{ __('general.room') }}</th>
            <th>{{ __('general.bed_number') }}</th>
            <th>{{ __('general.student_number') }}</th>
            @foreach($locales as $locale)
                <th>{{ __('general.name') }} ({{ $locale }})</th>
            @endforeach
            <th>{{ __('general.grade') }}</th>
            <th>{{ __('general.class') }}</th>
        </tr>
        </thead>

        <tbody>
        @foreach($data as $student)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ optional($student->activeHostelBedAssignments)[0]?->bed?->hostelRoom?->name }}</td>
                <td>{{ optional($student->activeHostelBedAssignments)[0]?->bed?->name }}</td>
                <td>{{ $student->student_number }}</td>
                @foreach($locales as $locale)
                    <td>{{ $student->getTranslation('name', $locale) }}</td>
                @endforeach
                <td>{{ $student->latestPrimaryClass?->semesterClass?->classModel->grade->name }}</td>
                <td>{{ $student->latestPrimaryClass?->semesterClass?->classModel->name }}</td>
            </tr>
        @endforeach
        </tbody>

    </table>
@endsection
