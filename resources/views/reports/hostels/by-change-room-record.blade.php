@extends('reports.layout')

@section('content')
    <table>
        <thead>
        <tr>
            <th>Old Room</th>
            <th>Student No.</th>
            <th>Name</th>
            <th>New Room</th>
            <th>Class</th>
            <th>Check-In Date</th>
            <th>Check-Out Date</th>
        </tr>
        </thead>

        <tbody>
        @foreach($data as $record)
            <tr>
                <td>{{ $record['previous_room_name'] }} / {{ $record['previous_bed_name'] }}</td>
                <td>{{ $record['student_number'] }}</td>
                {{-- TODO: Kimi locale must be dynamic --}}
                <td>{{ $record['student_name']['en'] ?? '' }}</td>
                <td>{{ $record['room_name'] }} / {{ $record['bed_name'] }}</td>
                {{-- TODO: Kimi locale must be dynamic --}}
                <td>{{ $record['class_name']['en'] ?? '' }}</td>
                <td>{{ $record['check_in_date'] }}</td>
                <td>{{ $record['check_out_date'] }}</td>
            </tr>
        @endforeach
        </tbody>

    </table>
@endsection
