@php
    use App\Enums\Gender;
    use App\Helpers\ConfigHelper;use App\Helpers\ReportHelper;
@endphp

@extends('reports.layout')

@push('styles')
    <style>
        table {
            font-size: 16px;
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            border: 1px solid black;
            padding: 2px;
            text-align: center;
        }

        .empty-spaces {
            width: 2.5rem;
        }

        .extra-info {
            display: flex;
            justify-content: space-between;
        }

        body {
            font-weight: normal;
        }

        .m-0 {
            margin: 0;
        }
    </style>
@endpush

@section('content')
    @foreach ( $data as $item)
        @php
            $available_locales = ConfigHelper::getAvailableLocales();
            $students = $item['students'];
            $teachers = $item['teachers'];
            $trainers = $item['trainers'];
            $class_name = $item['class_name'];
            $semester_name = $item['semester_setting']->name;
        @endphp


        <div style="margin-bottom: 10px;">
            <h3 style="text-align: center;">{{ $semester_name }}</h3>
            <h3 style="text-align: center;">{{ $class_name }}</h3>
            <div class="extra-info">
                <p class="m-0">{{ __('semester_class.teacher') }} : {{ $teachers}}</p>
                <p class="m-0">{{ __('semester_class.trainer') }} : {{ $trainers}}</p>
                <p class="m-0">{{ __('general.total_count') }} : {{ count($students) }}</p>
                <p class="m-0">{{ __('general.print_date') }} : {{ ReportHelper::getPrintDate() }}</p>
            </div>
        </div>

        <table>
            <thead>
            <tr>
                <th style="width: 40px">{{ __('general.number') }}</th>
                <th>{{ __('general.student_number') }}</th>
                @foreach($available_locales as $locale)
                    <th>{{__('general.name')}} ({{$locale}})</th>
                @endforeach
                <th>{{ __('general.class') }}</th>
                <th>{{ __('general.gender') }}</th>
                <th class="empty-spaces"></th>
                <th class="empty-spaces"></th>
                <th class="empty-spaces"></th>
                <th class="empty-spaces"></th>
                <th class="empty-spaces"></th>
                <th class="empty-spaces"></th>
            </tr>
            </thead>

            <tbody>
            @foreach ($students as $student)
                <tr style="{{ !$loop->last ? 'border-bottom: 1px solid black; ' : '' }}">
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $student['student_number'] }}</td>
                    <td>{{ $student['student_name_en'] }}</td>
                    <td>{{ $student['student_name_zh'] }}</td>
                    <td>{{ $student['student_primary_class'] }}</td>
                    <td>{{ Gender::getGenderInitial($student['student_gender']) }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            @endforeach
            </tbody>
        </table>

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection
