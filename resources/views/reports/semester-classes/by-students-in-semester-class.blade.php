@php
    use App\Enums\Gender;
    use App\Helpers\ConfigHelper;use App\Helpers\ReportHelper;
@endphp

@extends('reports.layout')

@push('styles')
    <style>
        table {
            font-size: 16px;
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            border: 1px solid black;
            padding: 2px;
            text-align: center;
        }

        .empty-spaces {
            width: 2.5rem;
        }

        .title {
            font-size: 1.25rem;
            text-align: center;
            margin: 0;
            padding: 0;
            margin-bottom: 5px;
        }

        .extra-info {
            display: flex;
            justify-content: space-between;
        }
    </style>
@endpush

@section('content')
    @php
        $available_locales = ConfigHelper::getAvailableLocales();
        $students = $data['students'];
        $homeroom_teacher = $data['homeroom_teacher'];

        $semester_name = $data['semester_setting']->name;
    @endphp

    <div style="margin-bottom: 10px;">
        <div class="title">{{ $semester_name }}<br>{{ $data['class_model']->getTranslation('name', 'zh') .' ('. $data['class_model']->getTranslation('name', 'en') .')' }}</div>
        <div class="extra-info">
            <div>{{ __('general.homeroom_teacher') }} : {{ $homeroom_teacher?->getFormattedTranslations('name') }}</div>
            <div>{{ __('general.total_count') }} : {{ count($students) }}</div>
            <div>{{ __('general.print_date') }} : {{ ReportHelper::getPrintDate() }}</div>
        </div>
    </div>

    <table>
        <thead>
        <tr>
            <th>{{ __('general.number') }}</th>
            <th>{{ __('general.student_number') }}</th>
            @foreach($available_locales as $locale)
                <th>{{__('general.name')}} ({{$locale}})</th>
            @endforeach
            <th>{{ __('general.gender') }}</th>
            <th class="empty-spaces"></th>
            <th class="empty-spaces"></th>
            <th class="empty-spaces"></th>
            <th class="empty-spaces"></th>
            <th class="empty-spaces"></th>
            <th class="empty-spaces"></th>
            <th class="empty-spaces"></th>
        </tr>
        </thead>

        <tbody>
        @foreach ($students as $student)
            <tr>
                <td>{{ $student->latestPrimaryClassBySemesterSettings->first()->seat_no }}</td>
                <td>{{ $student->student_number }}</td>
                @foreach($available_locales as $locale)
                    <td>{{ $student->getTranslations('name')[$locale] }}</td>
                @endforeach
                <td>{{ Gender::getGenderInitial($student->gender) }}</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection
