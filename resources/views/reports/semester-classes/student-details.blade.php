@php use App\Enums\GuardianType; @endphp
@extends('reports.layout')
@section('content')
    @foreach($data as $student)
        <div style="text-align: center; margin-bottom: 10px;">
            <h2>{{__('semester_class.student_details_report')}} ({{$student['semester_name']}})</h2>
            <h4>{{__('general.print_date')}} : {{now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A')}}</h4>
        </div>

        <h2 style="width: 100%; border-bottom: 1px solid black;">{{__('general.student_information')}}</h2>

        <table>
            <tr>
                <td class="td1">{{__('general.student_name')}}</td>
                <td class="td2">: {{$student['name']}}</td>
                <td class="td3">{{__('general.semester')}}/{{__('general.class')}}</td>
                <td class="td4">: {{$student['semester_name']}}/{{$student['class_name']}}</td>
                <td class="td5" style="padding:0;" rowspan="3">
                    @if($student['photo'])
                        <img src="{{$student['photo']}}" width="100%">
                    @endif
                </td>
            </tr>
            <tr>
                <td class="td1">{{__('general.student_no')}}</td>
                <td class="td2">: {{$student['student_number']}}</td>
                <td class="td3">{{__('general.student_school_from')}}</td>
                <td class="td4">: {{$student['primary_school_name']}}</td>
                <td class="td5"></td>
            </tr>
            <tr>
                <td class="td1" style="width: 15%;">{{__('general.student_address')}}</td>
                <td class="td2" style="width: 25%;">: {{$student['address']}}</td>
                <td class="td3" style="padding:0">
                    <table>
                        <tr>
                            <td>{{__('general.student_dob')}}</td>
                        </tr>
                        <tr>
                            <td>{{__('general.nativeplace')}}</td>
                        </tr>
                        <tr>
                            <td>{{__('general.religion')}}</td>
                        </tr>
                    </table>
                </td>
                <td class="td4" style="padding:0">
                    <table>
                        <tr>
                            <td>: {{$student['date_of_birth']}}</td>
                        </tr>
                        <tr>
                            <td>: {{$student['birthplace']}}</td>
                        </tr>
                        <tr>
                            <td>: {{$student['religion']}}</td>
                        </tr>
                    </table>
                </td>
                <td class="td5"></td>
            </tr>
        </table>

        @foreach($student['guardians'] as $type => $guardian)
            @if($type == GuardianType::GUARDIAN->value)
                @foreach($guardian as $data)
                    <h3 style="padding:5px; background-color:#CCCCCC;">
                        {{__('general.student_'.strtolower($type).'_information')}}
                    </h3>

                    <table style="width: 100%;">
                        <tr>
                            <td class="td1">{{__('general.'.strtolower($type).'_name')}}</td>
                            <td class="td2">: {{$data['name']}}</td>
                            <td class="td3">{{__('general.'.strtolower($type).'_religion')}}</td>
                            <td class="td4">: {{$data['religion']}}</td>
                            <td class="td5"></td>
                        </tr>
                        <tr>
                            <td class="td1">{{__('general.'.strtolower($type).'_nationality')}}</td>
                            <td class="td2">: {{$data['nationality']}}</td>
                            <td class="td3">{{__('general.'.strtolower($type).'_phone_no')}}</td>
                            <td class="td4">: {{$data['phone_number']}}</td>
                            <td class="td5"></td>
                        </tr>
                        <tr>
                            <td class="td1">{{__('general.'.strtolower($type).'_email')}}</td>
                            <td class="td2">: {{$data['email']}}</td>
                            <td class="td3">{{__('general.'.strtolower($type).'_work')}}</td>
                            <td class="td4">: {{$data['occupation']}}</td>
                            <td class="td5"></td>
                        </tr>
                        <tr>
                            <td class="td1">{{__('general.'.strtolower($type).'_education')}}</td>
                            <td class="td2">: {{$data['education']}}</td>
                            <td class="td3"></td>
                            <td class="td4"></td>
                            <td class="td5"></td>
                        </tr>
                    </table>
                @endforeach
            @else
                <h3 style="padding:5px; background-color:#CCCCCC;">
                    {{__('general.student_'.strtolower($type).'_information')}}
                </h3>

                <table style="width: 100%;">
                    <tr>
                        <td class="td1">{{__('general.'.strtolower($type).'_name')}}</td>
                        <td class="td2">: {{$guardian['name']}}</td>
                        <td class="td3">{{__('general.'.strtolower($type).'_religion')}}</td>
                        <td class="td4">: {{$guardian['religion']}}</td>
                        <td class="td5"></td>
                    </tr>
                    <tr>
                        <td class="td1">{{__('general.'.strtolower($type).'_nationality')}}</td>
                        <td class="td2">: {{$guardian['nationality']}}</td>
                        <td class="td3">{{__('general.'.strtolower($type).'_phone_no')}}</td>
                        <td class="td4">: {{$guardian['phone_number']}}</td>
                        <td class="td5"></td>
                    </tr>
                    <tr>
                        <td class="td1">{{__('general.'.strtolower($type).'_email')}}</td>
                        <td class="td2">: {{$guardian['email']}}</td>
                        <td class="td3">{{__('general.'.strtolower($type).'_work')}}</td>
                        <td class="td4">: {{$guardian['occupation']}}</td>
                        <td class="td5"></td>
                    </tr>
                    <tr>
                        <td class="td1">{{__('general.'.strtolower($type).'_education')}}</td>
                        <td class="td2">: {{$guardian['education']}}</td>
                        <td class="td3"></td>
                        <td class="td4"></td>
                        <td class="td5"></td>
                    </tr>
                </table>
            @endif
        @endforeach

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection

@push('styles')
    <style>
        th, td {
            border: 0;
        }

        tbody tr:nth-child(even) {
            background-color: white;
        }

        table {
            font-size: 16px;
            table-layout: fixed;
        }

        .td1 {
            width: 17%;
        }

        .td2 {
            width: 25%;
        }

        .td3 {
            width: 20%;
        }

        .td4 {
            width: 25%;
        }

        .td5 {
            width: 13%;
        }

        td {
            vertical-align: top;
        }
    </style>
@endpush
