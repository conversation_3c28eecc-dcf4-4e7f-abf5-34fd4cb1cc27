@extends('reports.layout')

@section('content')
    @foreach($data['classes'] as $class)
        <table>
            <thead>
            <tr>
                <td colspan="4">{{__('general.class')}} : {{$class['class_name']}}</td>
                <td colspan="6">{{__('general.homeroom_teacher')}} : {{$class['homeroom_teacher_name']}}</td>
            </tr>

            <tr>
                <th>{{__('general.photo')}}</th>
                <th>{{__('general.seat_number')}}</th>
                <th>{{__('general.student_no')}}</th>
                <th>{{__('general.name')}}</th>
                <th>{{__('general.gender')}}</th>
                <th>{{__('general.ic_no')}}</th>
                <th>{{__('general.address')}}</th>
                <th>{{__('general.guardian_type')}}</th>
                <th>{{__('general.guardian_name')}}</th>
                <th>{{__('general.guardian_phone_no')}}</th>
            </tr>
            </thead>

            <tbody>

            @foreach($class['students'] as $student)
                @php
                    $guardian_count = count($student['guardians']);
                    $rowspan = max($guardian_count, 1);
                @endphp
                <tr class="bg-white">
                    <td rowspan="{{ $rowspan }}">
                        @if($student['photo'])
                            <img src="{{$student['photo']}}" width="100px">
                        @endif
                    </td>
                    <td rowspan="{{ $rowspan }}">
                        {{$student['seat_number']}}
                    </td>
                    <td rowspan="{{ $rowspan }}">
                        {{$student['student_number']}}
                    </td>
                    <td rowspan="{{ $rowspan }}">
                        {{$student['name']}}
                    </td>
                    <td rowspan="{{ $rowspan }}">
                        {{$student['gender']}}
                    </td>
                    <td rowspan="{{ $rowspan }}">
                        {{$student['nric']}}
                    </td>
                    <td rowspan="{{ $rowspan }}">
                        {{$student['address']}}
                    </td>

                    @if($guardian_count > 0)
                        <td>{{$student['guardians'][0]['type']}}</td>
                        <td>{{$student['guardians'][0]['name']}}</td>
                        <td>{{$student['guardians'][0]['phone_number']}}</td>
                    @else
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    @endif
                </tr>

                @for($i = 1; $i < $guardian_count; $i++)
                    <tr class="bg-white">
                        <td>{{$student['guardians'][$i]['type']}}</td>
                        <td>{{$student['guardians'][$i]['name']}}</td>
                        <td>{{$student['guardians'][$i]['phone_number']}}</td>
                    </tr>
                @endfor
            @endforeach
            </tbody>
        </table>

        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection

@push('styles')
    <style>
        thead {
            display: table-row-group;
        }
    </style>
@endpush
