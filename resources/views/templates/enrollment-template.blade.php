@extends('reports.layout')

@section('content')
    <table>
        <thead>
        <tr>
            <th>No</th>
            <th>考生编号 / Exam Slip</th>
            <th>Status (APPROVED, REJECTED, SHORLISTED)</th>
            <th>过期日期 / Expiry Date</th>
            <th>注册日期 / Register Date</th>
            <th>学生姓名</th>
            <th>Student Name</th>
            <th>性别 / Gender (MALE, FEMALE)</th>
            <th>身份证号码 / IC Number</th>
            <th>Passport Number</th>
            <th>外藉生/Foreigner (TRUE, FALSE)</th>
            <th>国籍/Nationality</th>
            <th>Religion</th>
            <th>地址 / Address</th>
            <th>宿舍 / Hostel (TRUE, FALSE)</th>
            <th>原因/Reason</th>
            <th>膳食 / Dietary Restrictions (NONE, VEGETARIAN)</th>
            <th>健康问题 / Health Concern</th>
            <th>兄弟姐妹 / Have Siblings (TRUE, FALSE)</th>
            <th>就读小学 / Primary School</th>
            <th>总平均 / Total Average</th>
            @foreach ($subject_lists as $code => $s)
                <th>{{ $s->getTranslation('name', 'zh') }} / {{ $s->getTranslation('name', 'en') }}</th>
            @endforeach
            <th>操行 / Conduct</th>
            <th>监护人类别 / Guardian Type (GUARDIAN, FATHER, MOTHER)</th>
            <th>监护人姓名 / Guardian Name</th>
            <th>监护人电话号码 / Guardian Phone Number</th>
            <th>Guardian Email</th>
            <th>备注 /Remarks</th>
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $row)
            <tr>
                <td>{{ $row['number'] }}</td>
                <td>{{ $row['exam_slip_number'] }}</td>
                <td>{{ $row['status'] }}</td>
                <td>{{ $row['expiry_date'] }}</td>
                <td>{{ $row['register_date'] }}</td>
                <td>{{ $row['student_name_zh'] }}</td>
                <td>{{ $row['student_name_en'] }}</td>
                <td>{{ $row['gender'] }}</td>
                <td>{{ $row['nric'] }}</td>
                <td>{{ $row['passport_number'] }}</td>
                <td>{{ $row['foreigner'] }}</td>
                <td>{{ $row['nationality'] }}</td>
                <td>{{ $row['religion'] }}</td>
                <td>{{ $row['address'] }}</td>
                <td>{{ $row['hostel'] }}</td>
                <td>{{ $row['hostel_reason'] }}</td>
                <td>{{ $row['dietary_restriction'] }}</td>
                <td>{{ $row['health_concern'] }}</td>
                <td>{{ $row['have_siblings'] }}</td>
                <td>{{ $row['primary_school'] }}</td>
                <td style="text-align: left;">{{ number_format($row['total_average'], 2) }}</td>
                @foreach ($subject_lists as $code => $subject)
                    <td style="text-align: left;">{{ number_format($row[$code], 2) }}</td>
                @endforeach
                <td>{{ $row['conduct'] }}</td>
                <td>{{ $row['guardian_type'] }}</td>
                <td>{{ $row['guardian_name'] }}</td>
                <td>{{ $row['guardian_phone_number'] }}</td>
                <td>{{ $row['guardian_email'] }}</td>
                <td>{{ $row['remarks'] }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection
