@php
    use App\Helpers\ConfigHelper;
@endphp

@extends('reports.layout')

@push('styles')
    <style>
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
            margin-top: 20px;
        }

        .extra-info {
            width: 400px;
        }

        .extra-info tr {
            background-color: #fff !important;
        }
    </style>
@endpush

@section('content')
    <div style="margin-bottom: 10px;">
        @php
            $available_locales = ConfigHelper::getAvailableLocales();
            $bill_to_name = '';
            $bill_to_number = '';
            $bill_to_nric = '';
            $bill_to_current_class = '';
            $bill_to_current_semester = '';
            $bill_to_is_hostel = false;

            if (get_class($bill_to) == App\Models\Student::class) {
                $bill_to_name = $bill_to->getTranslations('name');
                $bill_to_number = $bill_to->student_number;
                $bill_to_nric = $bill_to->nric;
                $bill_to_is_hostel = $bill_to->is_hostel;
                $bill_to_current_class = $bill_to?->currentSemesterPrimaryClass?->semesterClass?->classModel?->name;
                $bill_to_current_semester = $bill_to?->currentSemesterPrimaryClass?->semesterSetting?->name;
                $bill_to_is_hostel = $bill_to->is_hostel;
            }
        @endphp

        <h2 style="text-align: center;">{{ __('unpaid_item.list_of_unpaid_item') }}</h2>

        <div class="container">
            <table class="extra-info">
                <tr>
                    <td>{{ __('general.name') }}</td>
                    <td>
                        @foreach ($available_locales as $locale)
                            <span>{{ $bill_to_name[$locale] ?? '' }}</span>
                        @endforeach
                    </td>
                </tr>
                <tr>
                    <td>{{ __('general.nric') }}</td>
                    <td>{{ $bill_to_nric }}</td>
                </tr>
                @if (get_class($bill_to) == App\Models\Student::class)
                    <tr>
                        <td>{{ __('general.student_number') }}</td>
                        <td>{{ $bill_to_number }}</td>
                    </tr>
                    <tr>
                        <td>{{ __('general.class') }}</td>
                        <td>
                            {{ $bill_to_current_class }}
                            @if ($bill_to_current_semester != '')
                                ({{ $bill_to_current_semester }})
                            @endif
                        </td>
                    </tr>
                @endif
                <tr>
                    <td>{{ __('general.hostel') }}</td>
                    <td>{{ $bill_to_is_hostel ? 'Yes' : 'No' }}</td>
                </tr>
            </table>
        </div>
    </div>

    <table>
        <thead>
        <tr>
            <th>{{ __('general.no') }}</th>
            <th>{{ __('general.name') }}</th>
            <th>{{ __('general.month') }}</th>
            <th>{{ __('general.amount') }} ({{ config('school.currency_code') }})</th>
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $unpaid_item)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $unpaid_item->description }}</td>
                <td>{{ \Carbon\Carbon::parse($unpaid_item->period)->format('F Y') }}</td>
                <td>{{ $unpaid_item->amount_before_tax_after_discount }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection
