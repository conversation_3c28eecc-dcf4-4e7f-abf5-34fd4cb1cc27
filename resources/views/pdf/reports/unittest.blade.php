@php use Illuminate\Support\Collection; @endphp
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        table, th, td {
            border: 1px solid;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
    </style>
</head>
</html>
<body>

<div>
    <p>Generated By: {{ $generated_by }}</p>
    <h1>List of students</h1>
    <table>
        <thead>
            <tr>
                <th>Student No.</th>
                <th>Student Name (EN)</th>
                <th>Student Name (ZH)</th>
                <th>Gender</th>
            </tr>
        </thead>
        <tbody>
        @if ( isset($query) )
            @php
                $query->lazy()->each(function($item) {
            @endphp
            <tr>
                <td>{{ $item->student_number }}</td>
                <td>{{ $item->getTranslation('name', 'EN') }}</td>
                <td>{{ $item->getTranslation('name', 'ZH') }}</td>
                <td>{{ $item->gender }}</td>
            </tr>
            @php
                });
            @endphp
        @elseif ( isset($data) )
            @php
                foreach ( $data as $item ) {
            @endphp
            <tr>
                <td>{{ $item->student_number }}</td>
                <td>{{ $item->getTranslation('name', 'EN') }}</td>
                <td>{{ $item->getTranslation('name', 'ZH') }}</td>
                <td>{{ $item->gender }}</td>
            </tr>
            @php
                }
            @endphp
        @endif
        </tbody>
    </table>
</div>

</body>
