<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <style>
        * {
            font-family: "Cal<PERSON>ri", sans-serif;
        }

        .container {
            display: block;
            width: 90%;
            margin: 0 auto;
        }

        .header {
            display: block;
            text-align: center;
            font-size: 14px;
            margin-bottom: 24px;
        }

        h1 {
            margin-bottom: 0;
            margin-top: 0;
        }

        h1.title {
            text-align: center;
            text-decoration: underline;
            font-weight: normal;
            margin-bottom: 20px;
        }

        .top-container {
            margin-bottom: 30px;
            min-height: 50px;
            display: block;
        }

        .top {
            width: 50%;
            float: left;
        }

        .top .label {
            width: 150px;
            font-weight: bold;
        }

        .bottom .label {
            width: 200px;
            font-weight: bold;
        }

        .line-items {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .line-items td, th {
            border: 1px solid #CCC;
            padding: 8px;
            text-align: left;
        }

        .payments {
            width: 100%;
            border-collapse: collapse;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .payments td, th {
            border: 1px solid #CCC;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }

        .total-row td {
            font-weight: bold;
        }

        .total-row .label {
            text-align: right;
        }

        .footer {
            margin-top: 50px;
        }

        .footer span {
            display: block;
            text-align: center;
            margin-bottom: 10px;
        }
    </style>
</head>
</html>
<body>

<div class="container">
    <div class="header">
        <h1>滨华中学</h1>   <!-- todo: change this to school setting actual name -->
        <h1>Pin Hwa High School</h1>    <!-- todo: change this to school setting actual name -->
    </div>

    <h1 class="title">
        Receipt
    </h1>

    <div class="top-container">

        @php
            $invoice->loadMissing(['payments.paymentMethod']);
            $payments = $invoice->payments->load('bank');
            $currency = strtoupper($invoice->currency_code);
        @endphp

        <table class="top">
            <tr>
                <td class="label">Name:</td>
                <td>{{ $bill_to_name }}</td>
            </tr>
            <tr>
                <td class="label">{{ $bill_to_type}} No:</td>
                <td>{{ $bill_to_reference_no }}</td>
            </tr>
        </table>

        <table class="top">
            <tr>
                <td class="label">Date:</td>
                <td>{{ $invoice->document_date }}</td>
            </tr>
            <tr>
                <td class="label">Receipt No:</td>
                <td>{{ $invoice->reference_no }}</td>
            </tr>
        </table>
    </div>

    <table class="line-items">
        <thead>
        <tr>
            <th>No.</th>
            <th>Item Description</th>
            <th>Period</th>
            <th>Quantity</th>
            <th>Amount ({{ $currency }})</th>
        </tr>
        </thead>
        <tbody>
        @php
            $item_no = 1;
            $total = 0;
            $total_quantity = 0;
        @endphp
        @foreach ( $line_items as $line_item )
            <tr>
                <td style="width: 50px;">{{ $item_no++ }}</td>
                <td>{!! nl2br($line_item->description) !!}</td>
                <td style="width: 150px;">{{ isset($line_item->billableItem) ? \Carbon\Carbon::parse($line_item->billableItem->period)->format('M Y') : '-' }}</td>
                <td>{{ number_format($line_item->quantity, 2) }}</td>
                <td style="width: 150px;">{{ number_format($line_item->amount_before_tax, 2) }}</td>
            </tr>

            @php
                $total += $line_item->amount_before_tax;
                $total_quantity += $line_item->quantity;
            @endphp
        @endforeach
        <tr class="total-row">
            <td colspan="3" class="label">Total ({{ $currency }})</td>
            <td>{{ number_format($total_quantity, 2) }}</td>
            <td>{{ number_format($total, 2) }}</td>
        </tr>
        <tr class="total-row">
            <td colspan="3" class="label">Total Received ({{ $currency }})</td>
            <td>{{ number_format($total_quantity, 2) }}</td>
            <td>{{ number_format($total, 2) }}</td>
        </tr>
        </tbody>
    </table>

    <div class="bottom-container">
        <table class="bottom">
            <tr>
                <td class="label">Payment Due Date:</td>
                <td>{{ $invoice->payment_due_date }}</td>
            </tr>
            <tr>
                <td class="label">Payment Status:</td>
                <td>{{ strtoupper($invoice->payment_status) }}</td>
            </tr>
        </table>

        @if ( $payments->count() > 0 )
            <table class="payments">
                <thead>
                <tr>
                    <th style="width: 100px;">Payment Date</th>
                    <th style="width: 130px;">Payment Method</th>
                    <th>Bank</th>
                    <th>Payment Ref</th>
                    <th style="width: 100px;">Amount ({{ $currency }})</th>
                </tr>
                </thead>
                <tbody>
                @foreach ( $payments as $payment )
                    <tr>
                        <td>{{ $payment->paid_at !== null ? $payment->getPaidAtWithSchoolTimezone()->toDateString() : '-' }}</td>
                        <td>{{ $payment->paymentMethod !== null ? $payment->paymentMethod->name : '-' }}</td>
                        <td>{{ $payment->bank !== null ? $payment->bank->name : '-' }}</td>
                        <td>{{ $payment->payment_reference_no ?? '-' }}</td>
                        <td>{{ number_format($payment->amount_received, 2) }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        @endif
    </div>
    <div class="footer">
        <span>This is a computer generated Receipt. No signature is required.</span>
        <span>All paid amount are non-refundable.</span>
    </div>
</div>

</body>
