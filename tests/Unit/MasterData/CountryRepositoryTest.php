<?php

use App\Models\Country;
use App\Repositories\CountryRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->countryRepository = app(CountryRepository::class);

    app()->setLocale('en');

    $this->table = resolve(Country::class)->getTable();
    $this->testLocale = app()->getLocale();

    // Need to truncate for this test case, because creating a
    // user will create a student, and student will create a country
    Country::truncate();
});

test('getModelClass()', function () {
    $response = $this->countryRepository->getModelClass();

    expect($response)->toEqual(Country::class);
});

test('getAll()', function () {
    $countries = Country::factory(3)->state(new Sequence(
        [
            'name->en' => 'United States',
            'name->zh' => 'some char',
        ],
        [
            'name' => 'United Kingdom'
        ],
        [
            'name' => 'Malaysia'
        ]
    ))->create();

    $response = $this->countryRepository->getAll(['order_by' => 'id'])->toArray();

    expect($response)->toEqual($countries->toArray());
});

test('getAllPaginated()', function () {
    $countries = Country::factory(3)->state(new Sequence(
        [
            'name->en' => 'United States',
            'name->zh' => 'some char',
        ],
        [
            'name' => 'United Kingdom'
        ],
        [
            'name' => 'Malaysia'
        ]
    ))->create();

    //Filter by name = United States
    $payload = [
        'name' => ['en' => 'United States']
    ];
    $response = $this->countryRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'United States')
        );

    //Filter by partial name = United
    $payload = [
        'name' => ['en' => 'United']
    ];
    $response = $this->countryRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'United States'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'United Kingdom')
        );

    //Filter non-existing name = United What
    $payload = [
        'name' => ['en' => 'United What']
    ];
    $response = $this->countryRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by name asc
    $payload = [
        'order_by' => ['name' => 'asc'],
    ];

    $response = $this->countryRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Malaysia'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'United Kingdom'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'United States'),
        );

    //sort by name desc
    $payload = [
        'order_by' => ['name' => 'desc'],
    ];

    $response = $this->countryRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'United States'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'United Kingdom'),
            fn($data) => $data->toHaveKey("name.$this->testLocale", 'Malaysia'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->countryRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $countries[0]->id),
            fn($data) => $data->toHaveKey('id', $countries[1]->id),
            fn($data) => $data->toHaveKey('id', $countries[2]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->countryRepository->getAllPaginated($payload)->toArray();

    expect($response['data'])->toHaveCount(3)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $countries[2]->id),
            fn($data) => $data->toHaveKey('id', $countries[1]->id),
            fn($data) => $data->toHaveKey('id', $countries[0]->id),
        );
});

test('getByName()', function () {
    $countries = Country::factory(3)->state(new Sequence(
        [
            'name->en' => 'United States',
            'name->zh' => 'zh United States'
        ],
        [
            'name' => 'India'
        ]
    ))->create();

    // get by name = United States
    $response = $this->countryRepository->getByName('United States');

    expect($response)->toBeInstanceOf(Country::class)
        ->and($response->id)->toEqual($countries[0]->id)
        ->and($response->getTranslation('name', 'en'))->toEqual('United States')
        ->and($response->getTranslation('name', 'zh'))->toEqual('zh United States');

    // get by name = India
    $response = $this->countryRepository->getByName('India');

    expect($response)->toBeInstanceOf(Country::class)
        ->and($response->id)->toEqual($countries[1]->id);

    // get by name = Non existing
    $response = $this->countryRepository->getByName('Non existing');

    expect($response)->toBeNull();
});

test('getByNames()', function () {
    $this->seed(InternationalizationSeeder::class);

    $malaysia = Country::factory()->create([
        'name' => [
            'en' => 'Malaysia',
            'zh' => '马来西亚'
        ]
    ]);

    $singapore = Country::factory()->create([
        'name' => [
            'en' => 'Singapore',
            'zh' => '新加坡'
        ]
    ]);

    $thailand = Country::factory()->create([
        'name' => [
            'en' => 'Thailand',
            'zh' => '泰国'
        ]
    ]);

    // multiple names in different languages
    $result = $this->countryRepository->getByNames(['Malaysia', 'Singapore', '泰国']);

    expect($result)->toHaveCount(3);
    expect($result->pluck('id'))->toContain($malaysia->id, $singapore->id, $thailand->id);


    // non-existing names
    $result = $this->countryRepository->getByNames(['NonExistentCountry', 'FakeCountry']);

    expect($result)->toHaveCount(0);
    expect($result)->toBeEmpty();
});
