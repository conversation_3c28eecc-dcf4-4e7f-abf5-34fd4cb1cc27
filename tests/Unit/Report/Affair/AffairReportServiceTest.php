<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Enums\LeaveApplicationStatus;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Models\Attendance;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Services\DocumentPrintService;
use App\Services\Report\AttendanceReportService;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Support\Facades\DB;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->attendanceReportService = app(AttendanceReportService::class);
    $this->reportPrintService = app(ReportPrintService::class);

    $this->student_late = Student::factory()->create([
        'name->en' => 'Student Late',
        'student_number' => '1',
    ]);
    $this->student_attendance_late = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_late->id,
        'date' => '2024-12-01',
        'check_in_datetime' => '2024-12-01 00:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->student_on_time = Student::factory()->create([
        'name->en' => 'Student On time',
        'student_number' => '2',
    ]);
    $this->student_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_on_time->id,
        'date' => '2024-12-02',
        'check_in_datetime' => '2024-11-30 23:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->student_absent = Student::factory()->create([
        'name->en' => 'Student Absent',
        'student_number' => '3',
    ]);
    $this->student_attendance_absent = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    $this->student_on_time2 = Student::factory()->create([ // same class as student_late
        'name->en' => 'Student On time2',
        'student_number' => '4',
    ]);

    $this->teacher_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Employee::class,
        'attendance_recordable_id' => Employee::factory(),
        'date' => '2024-12-03',
        'check_in_datetime' => '2024-11-30 23:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => '2024-12-01 07:00:00', // UTC + 0 Time
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $this->semester_setting1 = SemesterSetting::factory()->create();
    $this->semester_setting1_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
    ]);
    $this->semester_setting1_semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
    ]);

    $this->semester_setting2 = SemesterSetting::factory()->create(['is_current_semester' => true]);
    $this->semester_setting2_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class->id,
        'student_id' => $this->student_late->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class->id,
        'student_id' => $this->student_on_time2->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'student_id' => $this->student_on_time->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $this->student_absent->id,
        'is_active' => true,
    ]);

    $this->primary_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'type' => ClassType::PRIMARY,
    ]);

    $this->sem2_primaryclass = SemesterClass::factory()->create([
        'class_id' => $this->primary_class->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    SnappyPdf::fake();
});

test('getStudentAttendance() filter by date_from and date_to', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-02',
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 1, 2024 (Sunday)',
                        'students' => [
                            [
                                'student_number' => $this->student_late->student_number,
                                'student_primary_class' => $this->semester_setting1_semester_class->classModel->name,
                                'student_name' => $this->student_late->getTranslations('name'),
                                'attendance_time_in' => '08:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceCheckInStatus::LATE->value,
                                'attendance_reason' => null,
                                'attendance_remarks' => null,
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 0,
                        'total_absent' => 0,
                        'total_late' => 1,
                    ]
                ]
            ]),
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_primary_class' => $this->semester_setting1_semester_class2->classModel->name,
                                'student_name' => $this->student_on_time->getTranslations('name'),
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null,
                                'attendance_remarks' => null,
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() filter by semester_setting_id', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 1, 2024 (Sunday)',
                        'students' => [
                            [
                                'student_number' => $this->student_late->student_number,
                                'student_primary_class' => $this->semester_setting1_semester_class->classModel->name,
                                'student_name' => $this->student_late->getTranslations('name'),
                                'attendance_time_in' => '08:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceCheckInStatus::LATE->value,
                                'attendance_reason' => null,
                                'attendance_remarks' => null,
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 0,
                        'total_absent' => 0,
                        'total_late' => 1,
                    ]
                ]
            ]),
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_primary_class' => $this->semester_setting1_semester_class2->classModel->name,
                                'student_name' => $this->student_on_time->getTranslations('name'),
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null,
                                'attendance_remarks' => null,
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() filter by status', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'status' => AttendanceStatus::PRESENT->value,
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_primary_class' => $this->semester_setting1_semester_class2->classModel->name,
                                'student_name' => $this->student_on_time->getTranslations('name'),
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null,
                                'attendance_remarks' => null,
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() filter by semester_setting_id and semester_class_id', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_primary_class' => $this->semester_setting1_semester_class2->classModel->name,
                                'student_name' => $this->student_on_time->getTranslations('name'),
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null,
                                'attendance_remarks' => null,
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAttendance() generate pdf', function () {
    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-02',
        'semester_class_id' => $this->semester_setting1_semester_class2->id,
        'class_type' => 'PRIMARY'
    ];

    $late_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Late',
        'name->zh' => '迟到',
    ]);
    // student_on_time leave (2024-12-02) - 1 leave
    $student_on_time_leave = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $this->student_on_time->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $late_leave->id,
        'reason' => 'late to school 2024-12-02',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $student_on_time_leave->id,
        'date' => '2024-12-02',
        'period' => 1,
    ]);

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    $report_data = [
        'data' => $response,
        'title' => 'Report Student Daily Arrival',
        'subtitle' => Carbon::parse($filters['date_from'])->translatedFormat('F j, Y') . ' - ' . Carbon::parse($filters['date_to'])->translatedFormat('F j, Y'),
    ];
    $report_view_name = 'reports.attendances.student-attendance-report';
    $file_name = 'student-attendance-report';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($report_data['title']);
    SnappyPdf::assertSee($report_data['subtitle']);

    foreach ($response as $class) {
        $expected_headers = ['Student No.', 'Student Class', 'Student Name', 'Student Name', 'Time In', 'Time Out', 'Attendance Status', 'Reason', 'Remarks'];

        foreach ($expected_headers as $header) {
            SnappyPdf::assertSee($header);

            foreach ($class['dates'] as $date) {
                SnappyPdf::assertSee($date['date']);

                foreach ($date['students'] as $student) {
                    SnappyPdf::assertSee($student['student_number']);
                    SnappyPdf::assertSee(e($class['class_name']));
                    SnappyPdf::assertSee($student['student_name']['en']);
                    SnappyPdf::assertSee($student['student_name']['zh']);

                    if ($student['attendance_time_in']) {
                        SnappyPdf::assertSee($student['attendance_time_in']);
                    }

                    if ($student['attendance_time_out']) {
                        SnappyPdf::assertSee($student['attendance_time_out']);
                    }

                    SnappyPdf::assertSee($student['attendance_status']->value);

                    if ($student['attendance_reason']) {
                        SnappyPdf::assertSee($student['attendance_reason']);
                    }

                    if ($student['attendance_remarks']) {
                        SnappyPdf::assertSee($student['attendance_remarks']);
                    }
                }

                SnappyPdf::assertSee('Total : ' . $date['total']);
                SnappyPdf::assertSee('Attend : ' . $date['total_present']);
                SnappyPdf::assertSee('Absent : ' . $date['total_absent']);
                SnappyPdf::assertSee('Late : ' . $date['total_late']);
            }
        }
    }
});

test('getStudentAttendance() -  filter by class_type', function () {
    $student_present = Student::factory()->create([
        'name' => 'Teachers pet'
    ]);
    $student_absent = Student::factory()->create([
        'name' => 'Mana you? Always absent'
    ]);

    $cocuclass1 = ClassModel::factory()->create([
        'name' => 'Badminton',
        'type' => ClassType::SOCIETY,
    ]);
    $cocuclass2 = ClassModel::factory()->create([
        'name' => 'Football',
        'type' => ClassType::SOCIETY,
    ]);

    $engclass1 = ClassModel::factory()->create([
        'name' => 'A1A',
        'type' => ClassType::ENGLISH
    ]);
    $engclass2 = ClassModel::factory()->create([
        'name' => 'A2A',
        'type' => ClassType::ENGLISH
    ]);

    $sem2_cocuclass1 = SemesterClass::factory()->create([
        'class_id' => $cocuclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_cocuclass2 = SemesterClass::factory()->create([
        'class_id' => $cocuclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);

    $sem2_engclass1 = SemesterClass::factory()->create([
        'class_id' => $engclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_engclass2 = SemesterClass::factory()->create([
        'class_id' => $engclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);


    // Student Present has 1 cocuclass and 1 English Class
    // Student Absent has 2 cocuclass and 2 English class (one of them is inactive, and the other has a later class enter date)
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);


    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    $filters = [
        'semester_setting_id' => $this->semester_setting2->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);
    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            'class_name' => $cocuclass1->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_present->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::PRESENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 1,
                    'total_absent' => 0,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_present->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ])
        ->and($response[1])->toMatchArray([
            'class_name' => $cocuclass2->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_absent->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_absent->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ]);

    $filters = [
        'semester_setting_id' => $this->semester_setting2->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);
    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            'class_name' => $engclass1->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_present->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::PRESENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 1,
                    'total_absent' => 0,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_present->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_present->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ])
        ->and($response[1])->toMatchArray([
            'class_name' => $engclass2->name,
            'dates' => [
                [
                    'date' => 'December 1, 2024 (Sunday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_absent->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ],
                [
                    'date' => 'December 2, 2024 (Monday)',
                    'students' => [
                        [
                            'student_number' => $student_absent->student_number,
                            'student_primary_class' => null,
                            'student_name' => $student_absent->getTranslations('name'),
                            'attendance_time_in' => null,
                            'attendance_time_out' => null,
                            'attendance_status' => AttendanceStatus::ABSENT,
                            'attendance_reason' => null,
                            'attendance_remarks' => null,
                        ]
                    ],
                    'total' => 1,
                    'total_present' => 0,
                    'total_absent' => 1,
                    'total_late' => 0,
                ]
            ]
        ]);
});

test('getStudentAttendance() with leave application', function () {

    // student_late leave (2024-12-01) - 2 leaves
    $late_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Late',
        'name->zh' => '迟到',
    ]);
    $student_late_leave1 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $this->student_late->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $late_leave->id,
        'reason' => 'late to school',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $student_late_leave1->id,
        'date' => '2024-12-01',
        'period' => 1,
    ]);
    $sick_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Sick Leave',
        'name->zh' => '病假',
    ]);
    $student_late_leave2 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $this->student_late->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $sick_leave->id,
        'reason' => 'mc left early',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $student_late_leave2->id,
        'date' => '2024-12-01',
        'period' => 2,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $student_late_leave2->id,
        'date' => '2024-12-01',
        'period' => 3,
    ]);

    // student_on_time leave (2024-12-02) - 1 leave
    $student_on_time_leave = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $this->student_on_time->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $late_leave->id,
        'reason' => 'late to school 2024-12-02',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $student_on_time_leave->id,
        'date' => '2024-12-02',
        'period' => 1,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $this->student_on_time2->id,
        'date' => '2024-12-01',
        'check_in_datetime' => '2024-11-30 23:00:00', // UTC + 0 Time
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $filters = [
        'semester_setting_id' => $this->semester_setting1->id,
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-02',
        'class_type' => 'PRIMARY'
    ];

    $response = $this->attendanceReportService->getStudentAttendance($filters);

    expect($response)
        ->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 1, 2024 (Sunday)',
                        'students' => [
                            [
                                'student_number' => $this->student_late->student_number,
                                'student_name' => $this->student_late->getTranslations('name'),
                                'attendance_time_in' => '08:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceCheckInStatus::LATE->value,
                                'attendance_reason' => 'late to school, mc left early',
                                'attendance_remarks' => 'Late, Sick Leave',
                                'student_primary_class' => $this->semester_setting1_semester_class->classModel->name,
                            ],
                            [
                                'student_number' => $this->student_on_time2->student_number,
                                'student_name' => $this->student_on_time2->getTranslations('name'),
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => null,
                                'attendance_remarks' => null,
                                'student_primary_class' => $this->semester_setting1_semester_class->classModel->name,
                            ],
                        ],
                        'total' => 2,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 1,
                    ]
                ]
            ]),
            fn($data) => $data->toMatchArray([
                'class_name' => $this->semester_setting1_semester_class2->classModel->name,
                'dates' => [
                    [
                        'date' => 'December 2, 2024 (Monday)',
                        'students' => [
                            [
                                'student_number' => $this->student_on_time->student_number,
                                'student_name' => $this->student_on_time->getTranslations('name'),
                                'attendance_time_in' => '07:00:00',
                                'attendance_time_out' => null,
                                'attendance_status' => AttendanceStatus::PRESENT,
                                'attendance_reason' => 'late to school 2024-12-02',
                                'attendance_remarks' => 'Late',
                                'student_primary_class' => $this->semester_setting1_semester_class2->classModel->name,
                            ]
                        ],
                        'total' => 1,
                        'total_present' => 1,
                        'total_absent' => 0,
                        'total_late' => 0,
                    ]
                ]
            ])
        );
});

test('getStudentAbsent() filter by absent count', function () {
    $this->student_absent->delete();

    $student_present2 = Student::factory()->create([
        'name' => 'A teachers pet'
    ]);
    $student_absent2 = Student::factory()->create([
        'name' => 'Mana you? Always absent'
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $student_present2->id,
        'is_active' => true,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->semester_setting2_semester_class->id,
        'student_id' => $student_absent2->id,
        'is_active' => true,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present2,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-03',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);
    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'PRIMARY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 0
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_present2->name,
            "student_number" => $student_present2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "absent_dates" => [],
        ])
        ->and($response[0]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_absent2->name,
            "student_number" => $student_absent2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
        ])
        ->and($response[1]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'PRIMARY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 1,
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);

    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent2->name,
            "student_number" => $student_absent2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "present_dates" => [],
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'PRIMARY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 2
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent2->name,
            "student_number" => $student_absent2->student_number,
            "class" => $this->semester_setting2_semester_class->classModel->name,
            "present_dates" => [],
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 3
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(0)
        ->toBe([]);
});


test('getStudentAbsent() filter by absent count - cocurricular and english', function () {

    $student_present = Student::factory()->create([
        'name' => 'Teachers pet'
    ]);
    $student_absent = Student::factory()->create([
        'name' => 'Mana you? Always absent'
    ]);

    $cocuclass1 = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
    ]);
    $cocuclass2 = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
    ]);

    $engclass1 = ClassModel::factory()->create([
        'type' => ClassType::ENGLISH
    ]);
    $engclass2 = ClassModel::factory()->create([
        'type' => ClassType::ENGLISH
    ]);

    $sem2_cocuclass1 = SemesterClass::factory()->create([
        'class_id' => $cocuclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_cocuclass2 = SemesterClass::factory()->create([
        'class_id' => $cocuclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);

    $sem2_engclass1 = SemesterClass::factory()->create([
        'class_id' => $engclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);
    $sem2_engclass2 = SemesterClass::factory()->create([
        'class_id' => $engclass2->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);

    ////////////////////////////////////////////////////////////////////////////
    // PRIMARY CLASS

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->sem2_primaryclass->id,
        'student_id' => $student_present->id,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->sem2_primaryclass->id,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'is_latest_class_in_semester' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    ////////////////////////////////////////////////////////////////////////////


    // Student Present has 1 cocuclass and 1 English Class
    // Student Absent has 2 cocuclass and 2 English class (one of them is inactive, and the other has a later class enter date)
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_cocuclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_present->id,
        'is_active' => true,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => null,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1,
        'student_id' => $student_absent->id,
        'is_active' => false,
        'is_latest_class_in_semester' => false,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-03-01',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass2,
        'student_id' => $student_absent->id,
        'is_active' => true,
        'class_enter_date' => '2024-04-01',
        'class_leave_date' => null,
    ]);


    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_present,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-01',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent,
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    // Class Type = Society Test
    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 0
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);

    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $cocuclass2->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $cocuclass1->name,
            "primary_class" => $this->primary_class->name,
        ])
        ->and($response[1]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $cocuclass2->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $cocuclass1->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 2
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $cocuclass2->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'SOCIETY',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 3
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(0)
        ->toBe([]);

    // Class Type = English Test
    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 0
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $engclass2->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $engclass1->name,
            "primary_class" => $this->primary_class->name,
        ])
        ->and($response[1]['present_dates'][0])->toMatchArray([
            "attendance_status" => "PRESENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(2)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $engclass2->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[1])->toMatchArray([
            "student_name" => $student_present->name,
            "student_number" => $student_present->student_number,
            "class" => $engclass1->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[1]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);


    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 2
    ];

    $response = $this->attendanceReportService->getStudentAbsent($filters);
    usort($response, function ($a, $b) {
        return $a['student_name'] <=> $b['student_name'];
    });

    expect($response)->toHaveCount(1)
        ->and($response[0])->toMatchArray([
            "student_name" => $student_absent->name,
            "student_number" => $student_absent->student_number,
            "class" => $engclass2->name,
            "primary_class" => $this->primary_class->name,
            "present_dates" => []
        ])
        ->and($response[0]['absent_dates'][0])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-01",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ])
        ->and($response[0]['absent_dates'][1])->toMatchArray([
            "attendance_status" => "ABSENT",
            "date" => "2024-12-02",
            "attendance_time_in" => null,
            "attendance_time_out" => null
        ]);

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'class_type' => 'ENGLISH',
        'semester_setting_id' => $this->semester_setting2->id,
        'absent_count' => 3
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);
    expect($response)->toHaveCount(0)
        ->toBe([]);


});


test('getStudentAbsentReport() generate pdf for PRIMARY and non-PRIMARY', function () {

    $student_absent2 = Student::factory()->create([
        'name' => 'Always Absent'
    ]);

    $engclass1 = ClassModel::factory()->create([
        'name' => 'A1A',
        'type' => ClassType::ENGLISH
    ]);

    $sem2_engclass1 = SemesterClass::factory()->create([
        'class_id' => $engclass1->id,
        'semester_setting_id' => $this->semester_setting2->id
    ]);

    // ENGLISH CLASS
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $sem2_engclass1->id,
        'student_id' => $student_absent2->id,
        'is_active' => true,
    ]);

    // PRIMARY CLASS
    StudentClass::factory()->create([
        'semester_setting_id' => $this->semester_setting2->id,
        'semester_class_id' => $this->sem2_primaryclass->id,
        'student_id' => $student_absent2->id,
        'is_active' => true,
    ]);

    // ATTENDANCES
    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-02',
        'check_in_datetime' => now()->toDateTimeString(),
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student_absent2,
        'date' => '2024-12-03',
        'check_in_datetime' => now()->toDateTimeString(),
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);
    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'semester_setting_id' => $this->semester_setting2->id,
        'class_type' => 'PRIMARY',
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);

    $report_data = [
        'data' => $response,
        'title' => 'Student Absent Report',
        'subtitle' => Carbon::parse($filters['date_from'])->translatedFormat('F j, Y') . ' - ' . Carbon::parse($filters['date_to'])->translatedFormat('F j, Y'),
        'is_primary_class_required' => $filters['class_type'] === ClassType::SOCIETY->value || $filters['class_type'] === ClassType::ENGLISH->value ? true : false,
    ];
    $report_view_name = 'reports.attendances.student-absent-report';
    $file_name = 'student-absent-report';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee($report_data['title']);
    SnappyPdf::assertSee($report_data['subtitle']);

    $expected_headers = ['No.', 'Attendance Status', 'Student No.', 'Student Name', 'Date', 'Time In', 'Time Out', 'Class'];

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($response as $data) {
        SnappyPdf::assertSee(e($data['student_name']));
        SnappyPdf::assertSee($data['student_number']);
        SnappyPdf::assertSee(e($data['class']));

        foreach ($data['absent_dates'] as $date) {
            SnappyPdf::assertSee($date['attendance_status']);
            SnappyPdf::assertSee($date['date']);

            if ($date['attendance_time_in'] == null) {
                SnappyPdf::assertSee('-');
            } else {
                SnappyPdf::assertSee($date['attendance_time_in']);
            }

            if ($date['attendance_time_out'] == null) {
                SnappyPdf::assertSee('-');
            } else {
                SnappyPdf::assertSee($date['attendance_time_out']);
            }
        }
    }


    // Non-PRIMARY Class ensure to show the primary class
    $filters = [
        'date_from' => '2024-12-01',
        'date_to' => '2024-12-03',
        'semester_setting_id' => $this->semester_setting2->id,
        'class_type' => 'ENGLISH',
        'absent_count' => 1
    ];
    $response = $this->attendanceReportService->getStudentAbsent($filters);

    $report_data = [
        'data' => $response,
        'title' => 'Student Absent Report',
        'subtitle' => Carbon::parse($filters['date_from'])->translatedFormat('F j, Y') . ' - ' . Carbon::parse($filters['date_to'])->translatedFormat('F j, Y'),
        'is_primary_class_required' => $filters['class_type'] === ClassType::SOCIETY->value || $filters['class_type'] === ClassType::ENGLISH->value ? true : false,
    ];

    $report_view_name = 'reports.attendances.student-absent-report';
    $file_name = 'student-absent-report';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    $expected_headers = ['No.', 'Attendance Status', 'Student No.', 'Student Name', 'Date', 'Time In', 'Time Out', 'Class'];

    foreach ($response as $data) {
        expect($data)->toHaveKey('primary_class');

        SnappyPdf::assertSee(e($data['student_name']));
        SnappyPdf::assertSee($data['student_number']);
        SnappyPdf::assertSee(e($data['class']));
        SnappyPdf::assertSee("({$data['primary_class']})");
    }
});
