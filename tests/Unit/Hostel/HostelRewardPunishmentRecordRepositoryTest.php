<?php

use App\Enums\HostelBlockType;
use App\Enums\HostelMeritDemeritType;
use App\Enums\HostelRoomBedStatus;
use App\Enums\HostelRoomGender;
use App\Models\Employee;
use App\Models\HostelBedAssignment;
use App\Models\HostelBedByYearView;
use App\Models\HostelBlock;
use App\Models\HostelMeritDemeritSetting;
use App\Models\HostelRewardPunishmentRecord;
use App\Models\HostelRewardPunishmentSetting;
use App\Models\HostelRoom;
use App\Models\HostelRoomBed;
use App\Models\Student;
use App\Models\User;
use App\Repositories\HostelRewardPunishmentRecordRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->hostelRewardPunishmentRecordRepository = resolve(HostelRewardPunishmentRecordRepository::class);
});

test('getModelClass()', function () {
    $response = $this->hostelRewardPunishmentRecordRepository->getModelClass();

    expect($response)->toEqual(HostelRewardPunishmentRecord::class);
});

test('getAll()', function () {
    $first_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create();
    $second_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create();
    $third_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create();

    $response = $this->hostelRewardPunishmentRecordRepository->getAll(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->sequence(
        fn($response) => $response->toMatchArray($first_hostel_reward_punishment_record->toArray()),
        fn($response) => $response->toMatchArray($second_hostel_reward_punishment_record->toArray()),
        fn($response) => $response->toMatchArray($third_hostel_reward_punishment_record->toArray()),
    );
});

test('getAllPaginated()', function () {
    $person_in_charge = User::factory()->create();
    $student = Student::factory()->create();
    $date = '2024-12-25';
    $hostel_reward_punishment_setting = HostelRewardPunishmentSetting::factory()->create();
    $created_by = User::factory()->create();

    $first_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create([
        'person_in_charge_id' => $person_in_charge->id,
    ]);

    $second_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create([
        'student_id' => $student->id,
    ]);

    $third_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create([
        'date' => $date,
    ]);

    $fourth_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create([
        'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_setting->id,
    ]);

    $fifth_hostel_reward_punishment_record = HostelRewardPunishmentRecord::factory()->create([
        'created_by' => $created_by->id,
    ]);

    // Filter by person_in_charge_id = $student->id
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['person_in_charge_id' => $person_in_charge->id])->toArray();

    expect($response['data'])
        ->toHaveCount(1)
        ->sequence(
            fn($item) => $item->toEqual($first_hostel_reward_punishment_record->toArray()),
        );

    // Filter by non existing person_in_charge_id = 9999
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['person_in_charge_id' => 9999])->toArray();

    expect($response['data'])->toBeEmpty();

    // Filter by student_id = $student->id
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['student_id' => $student->id])->toArray();

    expect($response['data'])
        ->toHaveCount(1)
        ->sequence(
            fn($item) => $item->toEqual($second_hostel_reward_punishment_record->toArray()),
        );

    // Filter by non existing student_id = 9999
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['student_id' => 9999])->toArray();

    expect($response['data'])->toBeEmpty();

    // Filter by date = $date
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['date' => $date])->toArray();

    expect($response['data'])
        ->toHaveCount(1)
        ->sequence(
            fn($item) => $item->toEqual($third_hostel_reward_punishment_record->toArray()),
        );

    // Filter by non existing date = '2022-01-01'
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['date' => '2022-01-01'])->toArray();

    expect($response['data'])->toBeEmpty();

    // Filter by hostel_reward_punishment_setting_id = $hostel_reward_punishment_setting->id
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['hostel_reward_punishment_setting_id' => $hostel_reward_punishment_setting->id])->toArray();

    expect($response['data'])
        ->toHaveCount(1)
        ->sequence(
            fn($item) => $item->toEqual($fourth_hostel_reward_punishment_record->toArray()),
        );

    // Filter by non existing hostel_reward_punishment_setting_id = 999
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['hostel_reward_punishment_setting_id' => 999])->toArray();

    expect($response['data'])->toBeEmpty();

    // Filter by created_by = $created_by->id
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated(['created_by' => $created_by->id])->toArray();

    expect($response['data'])
        ->toHaveCount(1)
        ->sequence(
            fn($item) => $item->toEqual($fifth_hostel_reward_punishment_record->toArray()),
        );

    /***
     * SORTING
     */

    // Sort by id asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('id', $first_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $second_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $third_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $fourth_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $fifth_hostel_reward_punishment_record->id),
    );

    // Sort by id desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('id', $fifth_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $fourth_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $third_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $second_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $first_hostel_reward_punishment_record->id),
    );

    // Sort by hostel_reward_punishment_setting_id asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated([
        'order_by' => ['hostel_reward_punishment_setting_id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('id', $fourth_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $first_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $second_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $third_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $fifth_hostel_reward_punishment_record->id),
    );

    // Sort by hostel_reward_punishment_setting_id desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAllPaginated([
        'order_by' => ['hostel_reward_punishment_setting_id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($response) => $response->toHaveKey('id', $fifth_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $third_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $second_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $first_hostel_reward_punishment_record->id),
        fn($response) => $response->toHaveKey('id', $fourth_hostel_reward_punishment_record->id),
    );
});

test('getHostelRewardPunishmentReportData()', function () {

    $other_employee = Employee::factory(4)->create();
    $person_in_charge_A = Employee::factory()->create();
    $person_in_charge_B = Employee::factory()->create();

    $other_students = Student::factory(4)->create();
    $student_A = Student::factory()->create();
    $student_B = Student::factory()->create();
    $student_C = Student::factory()->create();
    $student_D = Student::factory()->create();

    // hostel block sample data
    $hostel_block_A = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-A',
        'type' => HostelBlockType::STUDENT
    ]);
    $hostel_block_B = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-B',
        'type' => HostelBlockType::STUDENT
    ]);

    // hostel room sample data
    $hostel_room_A = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_B = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_B->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_C = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_B->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_D = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_B->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    // hostel room bed sample data
    $hostel_room_bed_A = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_B = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_B->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_C = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_C->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_D = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_D->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    // hostel bed assignment sample data
    $hostel_bed_assignment_student_A_2024_04_30 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A['id'],
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);
    $hostel_bed_assignment_student_B_2023_04_30 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B['id'],
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-04-30"),
        'end_date' => strtotime("2023-10-30"),
        'remarks' => fake()->text(100),
    ]);
    $hostel_bed_assignment_student_B_2024_01_01 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B['id'],
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-01-01"),
        'end_date' => strtotime("2024-04-30"),
        'remarks' => fake()->text(100),
    ]);
    $hostel_bed_assignment_student_B_2024_04_30 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B['id'],
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);
    $hostel_bed_assignment_student_C_2023_04_30 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_C['id'],
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_C->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);
    $hostel_bed_assignment_student_D_2023_04_30 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_D['id'],
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_D->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-04-30"),
        'end_date' => strtotime("2024-01-01"),
        'remarks' => fake()->text(100),
    ]);

    // merit demerit setting data
    $hostel_merit_demerit_setting_A = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Illegal Item -5 marks'
    ]);
    $hostel_merit_demerit_setting_B = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Other -5 marks'
    ]);
    $hostel_merit_demerit_setting_C = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Charity +2 marks'
    ]);

    // reward punishment setting data
    $hostel_reward_punishment_settings_A = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
        'code' => fake()->uuid(),
        'name' => 'Bring Electronic item to school',
        'points' => -5
    ]);
    $hostel_reward_punishment_settings_B = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
        'code' => fake()->uuid(),
        'name' => 'Did not clean toilet/room',
        'points' => -5
    ]);
    $hostel_reward_punishment_settings_C = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
        'code' => fake()->uuid(),
        'name' => 'Volunteer at old folks home',
        'points' => 2

    ]);

    // hostel reward punishment record data
    $hostel_reward_punishment_records = HostelRewardPunishmentRecord::factory(6)->create(new Sequence(
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad student',
            'date' => '2024-01-01'
        ],
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_B->id,
            'remark' => 'Bad student',
            'date' => '2024-12-31'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_C->id,
            'remark' => 'Good Student',
            'date' => '2024-06-15'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad Student',
            'date' => '2023-10-20'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_C->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad Student',
            'date' => '2024-10-20'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_D->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad Student',
            'date' => '2024-10-20'
        ],
    ));

    // student A
    // $hostel_bed_assignment_student_A_2024_04_30, start_date = 2024-04-30, end_date = null
    // student B
    // $hostel_bed_assignment_student_B_2023_04_30, start_date = 2023-04-30, end_date = 2023-10-30
    // $hostel_bed_assignment_student_B_2024_01_01, start_date = 2024-01-01, end_date = 2024-04-30
    // $hostel_bed_assignment_student_B_2024_04_30, start_date = 2024-04-30, end_date = null
    // student C
    // $hostel_bed_assignment_student_C_2023_04_30, start_date = 2023-04-30, end_date = null
    // student D
    // $hostel_bed_assignment_student_D_2023_04_30, start_date = 2023-04-30, end_date = 2024-01-01

    // filter by date test
    $payload = [
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];
    $response = $this->hostelRewardPunishmentRecordRepository->getHostelRewardPunishmentReportData($payload)->sortBy('id')->values()->toArray();

    expect($response)->toBe(
        [
            [
                'id' => $hostel_reward_punishment_records[0]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[0]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[0]->student_id,
                'date' => $hostel_reward_punishment_records[0]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[0]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[0]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_A->id,
                    'name' => $person_in_charge_A->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_A->id,
                    'name' => $student_A->getTranslations('name'),
                    'student_number' => $student_A->student_number,
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_A_2024_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_A_2024_04_30->start_date->toISOString(), // 2024-04-30
                            'end_date' => null, // null
                            'assignable_id' => $student_A->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_A->id,
                            'bed' => [
                                'id' => $hostel_room_bed_A->id,
                                'hostel_room_id' => $hostel_room_A->id,
                                'name' => $hostel_room_bed_A->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_A->id,
                                    'name' => $hostel_room_A->name,
                                    'hostel_block_id' => $hostel_block_A->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_A->id,
                                        'code' => $hostel_block_A->code
                                    ]
                                ]
                            ]
                        ]
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_A->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
                    'name' => $hostel_reward_punishment_settings_A->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_A->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_A->id,
                        'name' => $hostel_merit_demerit_setting_A->name,
                    ]
                ]
            ],
            [
                'id' => $hostel_reward_punishment_records[1]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[1]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[1]->student_id,
                'date' => $hostel_reward_punishment_records[1]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[1]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[1]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_A->id,
                    'name' => $person_in_charge_A->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_A->id,
                    'name' => $student_A->getTranslations('name'),
                    'student_number' => $student_A->student_number,
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_A_2024_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_A_2024_04_30->start_date->toISOString(), // 2024-04-30
                            'end_date' => null, // null
                            'assignable_id' => $student_A->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_A->id,
                            'bed' => [
                                'id' => $hostel_room_bed_A->id,
                                'hostel_room_id' => $hostel_room_A->id,
                                'name' => $hostel_room_bed_A->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_A->id,
                                    'name' => $hostel_room_A->name,
                                    'hostel_block_id' => $hostel_block_A->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_A->id,
                                        'code' => $hostel_block_A->code
                                    ]
                                ]
                            ]
                        ]
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_B->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
                    'name' => $hostel_reward_punishment_settings_B->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_B->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_B->id,
                        'name' => $hostel_merit_demerit_setting_B->name,
                    ]
                ]
            ],
            [
                'id' => $hostel_reward_punishment_records[2]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[2]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[2]->student_id,
                'date' => $hostel_reward_punishment_records[2]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[2]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[2]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_B->id,
                    'name' => $person_in_charge_B->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_B->id,
                    'name' => $student_B->getTranslations('name'),
                    'student_number' => $student_B->student_number,
                    // only 2 beds, $hostel_bed_assignment_student_B_2023_04_30 end date is 2023-10-30 (before 2024-01-01)
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_B_2024_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_B_2024_04_30->start_date->toISOString(), // 2024-04-30
                            'end_date' => null, // null
                            'assignable_id' => $student_B->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_B->id,
                            'bed' => [
                                'id' => $hostel_room_bed_B->id,
                                'hostel_room_id' => $hostel_room_B->id,
                                'name' => $hostel_room_bed_B->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_B->id,
                                    'name' => $hostel_room_B->name,
                                    'hostel_block_id' => $hostel_block_B->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_B->id,
                                        'code' => $hostel_block_B->code
                                    ]
                                ]
                            ]
                        ],
                        [
                            'id' => $hostel_bed_assignment_student_B_2024_01_01->id,
                            'start_date' => $hostel_bed_assignment_student_B_2024_01_01->start_date->toISOString(), // 2024-01-01
                            'end_date' => $hostel_bed_assignment_student_B_2024_01_01->end_date->toISOString(), // 2024-04-30
                            'assignable_id' => $student_B->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_B->id,
                            'bed' => [
                                'id' => $hostel_room_bed_B->id,
                                'hostel_room_id' => $hostel_room_B->id,
                                'name' => $hostel_room_bed_B->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_B->id,
                                    'name' => $hostel_room_B->name,
                                    'hostel_block_id' => $hostel_block_B->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_B->id,
                                        'code' => $hostel_block_B->code
                                    ]
                                ]
                            ]
                        ],
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_C->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
                    'name' => $hostel_reward_punishment_settings_C->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_C->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_C->id,
                        'name' => $hostel_merit_demerit_setting_C->name,
                    ]
                ]
            ],
            [
                'id' => $hostel_reward_punishment_records[4]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[4]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[4]->student_id,
                'date' => $hostel_reward_punishment_records[4]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[4]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[4]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_B->id,
                    'name' => $person_in_charge_B->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_C->id,
                    'name' => $student_C->getTranslations('name'),
                    'student_number' => $student_C->student_number,
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_C_2023_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_C_2023_04_30->start_date->toISOString(), // 2023-04-30
                            'end_date' => null, // null
                            'assignable_id' => $student_C->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_C->id,
                            'bed' => [
                                'id' => $hostel_room_bed_C->id,
                                'hostel_room_id' => $hostel_room_C->id,
                                'name' => $hostel_room_bed_C->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_C->id,
                                    'name' => $hostel_room_C->name,
                                    'hostel_block_id' => $hostel_block_B->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_B->id,
                                        'code' => $hostel_block_B->code
                                    ]
                                ]
                            ]
                        ],
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_A->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
                    'name' => $hostel_reward_punishment_settings_A->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_A->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_A->id,
                        'name' => $hostel_merit_demerit_setting_A->name,
                    ]
                ]
            ],
            [
                'id' => $hostel_reward_punishment_records[5]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[5]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[5]->student_id,
                'date' => $hostel_reward_punishment_records[5]->date->toISOString(), // 2024-10-20
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[5]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[5]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_B->id,
                    'name' => $person_in_charge_B->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_D->id,
                    'name' => $student_D->getTranslations('name'),
                    'student_number' => $student_D->student_number,
                    'beds' => [], // no bed in 2024
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_A->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
                    'name' => $hostel_reward_punishment_settings_A->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_A->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_A->id,
                        'name' => $hostel_merit_demerit_setting_A->name,
                    ]
                ]
            ],
        ]
    );

    // filter by student
    $payload = [
        'student_id' => $student_A['id'],
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];
    $response = $this->hostelRewardPunishmentRecordRepository->getHostelRewardPunishmentReportData($payload)->toArray();
    $response = collect($response)->sortBy('id')->toArray();

    expect($response)->toBe(
        [
            [
                'id' => $hostel_reward_punishment_records[0]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[0]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[0]->student_id,
                'date' => $hostel_reward_punishment_records[0]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[0]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[0]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_A->id,
                    'name' => $person_in_charge_A->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_A->id,
                    'name' => $student_A->getTranslations('name'),
                    'student_number' => $student_A->student_number,
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_A_2024_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_A_2024_04_30->start_date->toISOString(),
                            'end_date' => null,
                            'assignable_id' => $student_A->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_A->id,
                            'bed' => [
                                'id' => $hostel_room_bed_A->id,
                                'hostel_room_id' => $hostel_room_A->id,
                                'name' => $hostel_room_bed_A->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_A->id,
                                    'name' => $hostel_room_A->name,
                                    'hostel_block_id' => $hostel_block_A->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_A->id,
                                        'code' => $hostel_block_A->code
                                    ]
                                ]
                            ]
                        ]
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_A->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
                    'name' => $hostel_reward_punishment_settings_A->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_A->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_A->id,
                        'name' => $hostel_merit_demerit_setting_A->name,
                    ]
                ]
            ],
            [
                'id' => $hostel_reward_punishment_records[1]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[1]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[1]->student_id,
                'date' => $hostel_reward_punishment_records[1]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[1]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[1]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_A->id,
                    'name' => $person_in_charge_A->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_A->id,
                    'name' => $student_A->getTranslations('name'),
                    'student_number' => $student_A->student_number,
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_A_2024_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_A_2024_04_30->start_date->toISOString(),
                            'end_date' => null,
                            'assignable_id' => $student_A->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_A->id,
                            'bed' => [
                                'id' => $hostel_room_bed_A->id,
                                'hostel_room_id' => $hostel_room_A->id,
                                'name' => $hostel_room_bed_A->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_A->id,
                                    'name' => $hostel_room_A->name,
                                    'hostel_block_id' => $hostel_block_A->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_A->id,
                                        'code' => $hostel_block_A->code
                                    ]
                                ]
                            ]
                        ]
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_B->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
                    'name' => $hostel_reward_punishment_settings_B->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_B->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_B->id,
                        'name' => $hostel_merit_demerit_setting_B->name,
                    ]
                ]
            ]
        ]
    );

    // filter by room
    $payload = [
        'room_id' => $hostel_room_C->id,
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];
    $response = $this->hostelRewardPunishmentRecordRepository->getHostelRewardPunishmentReportData($payload)->toArray();
    $response = collect($response)->sortBy('id')->toArray();

    expect($response)->toBe(
        [
            [
                'id' => $hostel_reward_punishment_records[4]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[4]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[4]->student_id,
                'date' => $hostel_reward_punishment_records[4]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[4]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[4]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_B->id,
                    'name' => $person_in_charge_B->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_C->id,
                    'name' => $student_C->getTranslations('name'),
                    'student_number' => $student_C->student_number,
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_C_2023_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_C_2023_04_30->start_date->toISOString(), // 2023-04-30
                            'end_date' => null, // null
                            'assignable_id' => $student_C->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_C->id,
                            'bed' => [
                                'id' => $hostel_room_bed_C->id,
                                'hostel_room_id' => $hostel_room_C->id,
                                'name' => $hostel_room_bed_C->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_C->id,
                                    'name' => $hostel_room_C->name,
                                    'hostel_block_id' => $hostel_block_B->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_B->id,
                                        'code' => $hostel_block_B->code
                                    ]
                                ]
                            ]
                        ],
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_A->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
                    'name' => $hostel_reward_punishment_settings_A->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_A->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_A->id,
                        'name' => $hostel_merit_demerit_setting_A->name,
                    ]
                ]
            ],
        ]
    );

    // filter by block
    $payload = [
        'block_id' => $hostel_block_B->id,
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];
    $response = $this->hostelRewardPunishmentRecordRepository->getHostelRewardPunishmentReportData($payload)->toArray();
    $response = collect($response)->sortBy('id')->toArray();

    expect($response)->toBe(
        [
            [
                'id' => $hostel_reward_punishment_records[2]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[2]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[2]->student_id,
                'date' => $hostel_reward_punishment_records[2]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[2]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[2]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_B->id,
                    'name' => $person_in_charge_B->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_B->id,
                    'name' => $student_B->getTranslations('name'),
                    'student_number' => $student_B->student_number,
                    // only 2 beds, $hostel_bed_assignment_student_B_2023_04_30 end date is 2023-10-30 (before 2024-01-01)
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_B_2024_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_B_2024_04_30->start_date->toISOString(), // 2024-04-30
                            'end_date' => null, // null
                            'assignable_id' => $student_B->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_B->id,
                            'bed' => [
                                'id' => $hostel_room_bed_B->id,
                                'hostel_room_id' => $hostel_room_B->id,
                                'name' => $hostel_room_bed_B->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_B->id,
                                    'name' => $hostel_room_B->name,
                                    'hostel_block_id' => $hostel_block_B->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_B->id,
                                        'code' => $hostel_block_B->code
                                    ]
                                ]
                            ]
                        ],
                        [
                            'id' => $hostel_bed_assignment_student_B_2024_01_01->id,
                            'start_date' => $hostel_bed_assignment_student_B_2024_01_01->start_date->toISOString(), // 2024-01-01
                            'end_date' => $hostel_bed_assignment_student_B_2024_01_01->end_date->toISOString(), // 2024-04-30
                            'assignable_id' => $student_B->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_B->id,
                            'bed' => [
                                'id' => $hostel_room_bed_B->id,
                                'hostel_room_id' => $hostel_room_B->id,
                                'name' => $hostel_room_bed_B->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_B->id,
                                    'name' => $hostel_room_B->name,
                                    'hostel_block_id' => $hostel_block_B->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_B->id,
                                        'code' => $hostel_block_B->code
                                    ]
                                ]
                            ]
                        ],
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_C->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
                    'name' => $hostel_reward_punishment_settings_C->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_C->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_C->id,
                        'name' => $hostel_merit_demerit_setting_C->name,
                    ]
                ]
            ],
            [
                'id' => $hostel_reward_punishment_records[4]->id,
                'person_in_charge_id' => $hostel_reward_punishment_records[4]->person_in_charge_id,
                'student_id' => $hostel_reward_punishment_records[4]->student_id,
                'date' => $hostel_reward_punishment_records[4]->date->toISOString(),
                'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_records[4]->hostel_reward_punishment_setting_id,
                'remark' => $hostel_reward_punishment_records[4]->remark,
                'person_in_charge' => [
                    'id' => $person_in_charge_B->id,
                    'name' => $person_in_charge_B->getTranslations('name'),
                ],
                'student' => [
                    'id' => $student_C->id,
                    'name' => $student_C->getTranslations('name'),
                    'student_number' => $student_C->student_number,
                    'beds' => [
                        [
                            'id' => $hostel_bed_assignment_student_C_2023_04_30->id,
                            'start_date' => $hostel_bed_assignment_student_C_2023_04_30->start_date->toISOString(), // 2023-04-30
                            'end_date' => null, // null
                            'assignable_id' => $student_C->id,
                            'assignable_type' => Student::class,
                            'hostel_room_bed_id' => $hostel_room_bed_C->id,
                            'bed' => [
                                'id' => $hostel_room_bed_C->id,
                                'hostel_room_id' => $hostel_room_C->id,
                                'name' => $hostel_room_bed_C->name,
                                'hostel_room' => [
                                    'id' => $hostel_room_C->id,
                                    'name' => $hostel_room_C->name,
                                    'hostel_block_id' => $hostel_block_B->id,
                                    'hostel_block' => [
                                        'id' => $hostel_block_B->id,
                                        'code' => $hostel_block_B->code
                                    ]
                                ]
                            ]
                        ],
                    ],
                ],
                'hostel_reward_punishment_setting' => [
                    'id' => $hostel_reward_punishment_settings_A->id,
                    'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
                    'name' => $hostel_reward_punishment_settings_A->name,
                    'points' => number_format((float) $hostel_reward_punishment_settings_A->points, 2, '.', ''),
                    'hostel_merit_demerit_setting' => [
                        'id' => $hostel_merit_demerit_setting_A->id,
                        'name' => $hostel_merit_demerit_setting_A->name,
                    ]
                ]
            ],
        ]
    );
});

test('getAll(), sort correctly', function () {
    // PIC
    $pic_1 = Employee::factory()->create([
        'name->en' => 'ZZ Kevin Teacher',
    ]);

    $pic_2 = Employee::factory()->create([
        'name->en' => 'AA Jackson Teacher',
    ]);

    // Student
    $student_A = Student::factory()->create([
        'student_number' => 'C123',
        'name->en' => 'Arya Stark',

    ]);

    $student_B = Student::factory()->create([
        'student_number' => 'B123',
        'name->en' => 'Jon Snow',
    ]);

    $student_C = Student::factory()->create([
        'student_number' => 'A123',
        'name->en' => 'Sansa Stark',
    ]);

    // Merit Demerit Setting
    $demerit_setting = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Not Good',
    ]);

    $merit_setting = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Quite Good',
    ]);

    // Reward Punishment Setting
    $reward_punishment_bring_phone = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $demerit_setting->id,
        'name' => 'Bring Electronic item to school',
        'points' => -5
    ]);

    $reward_punishment_volunteer_work = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $merit_setting->id,
        'name' => 'Volunteer at old folks home',
        'points' => 10
    ]);

    // Record
    $record_1 = HostelRewardPunishmentRecord::factory()->create([
        'person_in_charge_id' => $pic_1->id,
        'student_id' => $student_A->id,
        'date' => '2024-01-01',
        'hostel_reward_punishment_setting_id' => $reward_punishment_bring_phone->id,
        'remark' => 'Multiple time done this',
    ]);

    $record_2 = HostelRewardPunishmentRecord::factory()->create([
        'person_in_charge_id' => $pic_1->id,
        'student_id' => $student_B->id,
        'date' => '2024-01-02',
        'hostel_reward_punishment_setting_id' => $reward_punishment_volunteer_work->id,
        'remark' => 'Good student',
    ]);

    $record_3 = HostelRewardPunishmentRecord::factory()->create([
        'person_in_charge_id' => $pic_2->id,
        'student_id' => $student_C->id,
        'date' => '2024-01-03',
        'hostel_reward_punishment_setting_id' => $reward_punishment_volunteer_work->id,
        'remark' => 'Well done!',
    ]);

    $record_1->load('personInCharge');
    $record_2->load('personInCharge');
    $record_3->load('personInCharge');


    // Sort by PIC->name asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['personInCharge'],
        'order_by' => [
            'person_in_charge_name' => 'asc',
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.person_in_charge.name.en', 'AA Jackson Teacher')
        ->toHaveKey('1.person_in_charge.name.en', 'ZZ Kevin Teacher')
        ->toHaveKey('2.person_in_charge.name.en', 'ZZ Kevin Teacher');

    // Sort by PIC->name desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['personInCharge'],
        'order_by' => [
            'person_in_charge_name' => 'desc',
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.person_in_charge.name.en', 'ZZ Kevin Teacher')
        ->toHaveKey('1.person_in_charge.name.en', 'ZZ Kevin Teacher')
        ->toHaveKey('2.person_in_charge.name.en', 'AA Jackson Teacher');


    // Sort by date asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'order_by' => [
            'date' => 'asc'
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.date', Carbon::parse('2024-01-01')->toIsoString())
        ->toHaveKey('1.date', Carbon::parse('2024-01-02')->toIsoString())
        ->toHaveKey('2.date', Carbon::parse('2024-01-03')->toIsoString());


    // Sort by date desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'order_by' => [
            'date' => 'desc'
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.date', Carbon::parse('2024-01-03')->toIsoString())
        ->toHaveKey('1.date', Carbon::parse('2024-01-02')->toIsoString())
        ->toHaveKey('2.date', Carbon::parse('2024-01-01')->toIsoString());


    // Sort by student_number asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['student'],
        'order_by' => [
            'student_number' => 'asc',
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.student.student_number', 'A123')
        ->toHaveKey('1.student.student_number', 'B123')
        ->toHaveKey('2.student.student_number', 'C123');

    // Sort by student_number desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['student'],
        'order_by' => [
            'student_number' => 'desc',
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.student.student_number', 'C123')
        ->toHaveKey('1.student.student_number', 'B123')
        ->toHaveKey('2.student.student_number', 'A123');


    // Sort by student_name asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['student'],
        'order_by' => [
            'student_name' => 'asc'
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.student.name.en', 'Arya Stark')
        ->toHaveKey('1.student.name.en', 'Jon Snow')
        ->toHaveKey('2.student.name.en', 'Sansa Stark');

    // Sort by student_name desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['student'],
        'order_by' => [
            'student_name' => 'desc',
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.student.name.en', 'Sansa Stark')
        ->toHaveKey('1.student.name.en', 'Jon Snow')
        ->toHaveKey('2.student.name.en', 'Arya Stark');


    // Sort by hostel_reward_punishment_setting->name asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['hostelRewardPunishmentSetting'],
        'order_by' => [
            'hostel_reward_punishment_setting_name' => 'asc',
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.hostel_reward_punishment_setting.name', 'Bring Electronic item to school')
        ->toHaveKey('1.hostel_reward_punishment_setting.name', 'Volunteer at old folks home')
        ->toHaveKey('2.hostel_reward_punishment_setting.name', 'Volunteer at old folks home');

    // Sort by hostel_reward_punishment_setting->name desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['hostelRewardPunishmentSetting'],
        'order_by' => [
            'hostel_reward_punishment_setting_name' => 'desc'
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.hostel_reward_punishment_setting.name', 'Volunteer at old folks home')
        ->toHaveKey('1.hostel_reward_punishment_setting.name', 'Volunteer at old folks home')
        ->toHaveKey('2.hostel_reward_punishment_setting.name', 'Bring Electronic item to school');


    // Sort by hostel_reward_punishment_setting->points asc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['hostelRewardPunishmentSetting'],
        'order_by' => [
            'hostel_reward_punishment_setting_points' => 'asc'
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.hostel_reward_punishment_setting.points', -5)
        ->toHaveKey('1.hostel_reward_punishment_setting.points', 10)
        ->toHaveKey('2.hostel_reward_punishment_setting.points', 10);

    // Sort by hostel_reward_punishment_setting->points desc
    $response = $this->hostelRewardPunishmentRecordRepository->getAll([
        'includes' => ['hostelRewardPunishmentSetting'],
        'order_by' => [
            'hostel_reward_punishment_setting_points' => 'desc'
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->toHaveKey('0.hostel_reward_punishment_setting.points', 10)
        ->toHaveKey('1.hostel_reward_punishment_setting.points', 10)
        ->toHaveKey('2.hostel_reward_punishment_setting.points', -5);
});
