<?php

use App\Enums\EnrollmentPaymentStatus;
use App\Enums\ExportType;
use App\Enums\Gender;
use App\Exports\EnrollmentsByDailyCollectionExport;
use App\Models\Bank;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Enrollment;
use App\Models\EnrollmentExam;
use App\Models\GlAccount;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\PaymentRequest;
use App\Models\Product;
use App\Models\UnpaidItem;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use App\Services\Reports\EnrollmentReportService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->enrollmentReportService = resolve(EnrollmentReportService::class);
});

test('getRegisteredStudentReport - testing date and payment status filter', function () {

    $filters = [
        'from_date' => '2025-06-01',
        'to_date' => '2025-10-10',
    ];

    Enrollment::factory(5)->state(new Sequence(
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-03'],
        ['payment_date' => '2024-07-01'],
    ))->create();

    $response = $this->enrollmentReportService->getRegisteredStudentReport($filters);

    expect($response['total'])->toMatchArray([
        "total_students" => 3,
        "male_hostel_students" => 0,
        "female_hostel_students" => 0,
        "mark_counts" => [
            "Under-50" => 0,
            "50-54.99" => 0,
            "55-59.99" => 0,
            "60-64.99" => 0,
            "65-69.99" => 0,
            "70-74.99" => 0,
            "75-79.99" => 0,
            "80-Above" => 0,
        ]
    ]);

    $response = $response['data']->toArray();
    expect($response)->toHaveCount(2)
        ->and($response['2025-07-01'])->toMatchArray([
            "date" => "2025-07-01",
            "total_students" => 2,
            "male_hostel_students" => 0,
            "female_hostel_students" => 0,
            "mark_counts" => [
                "Under-50" => 0,
                "50-54.99" => 0,
                "55-59.99" => 0,
                "60-64.99" => 0,
                "65-69.99" => 0,
                "70-74.99" => 0,
                "75-79.99" => 0,
                "80-Above" => 0
            ]
        ])
        ->and($response['2025-07-02'])->toMatchArray([
            "date" => "2025-07-02",
            "total_students" => 1,
            "male_hostel_students" => 0,
            "female_hostel_students" => 0,
            "mark_counts" => [
                "Under-50" => 0,
                "50-54.99" => 0,
                "55-59.99" => 0,
                "60-64.99" => 0,
                "65-69.99" => 0,
                "70-74.99" => 0,
                "75-79.99" => 0,
                "80-Above" => 0
            ]
        ]);
});

test('getRegisteredStudentReport - average mark grouping', function () {

    $filters = [
        'from_date' => '2025-06-01',
        'to_date' => '2025-10-10',
    ];

    $enrollments = Enrollment::factory(5)->state(new Sequence(
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
    ))->create();

    EnrollmentExam::factory(7)->state(new Sequence(
        [
            'enrollment_id' => $enrollments[0]->id,
            'total_average' => 90,
        ],
        [
            'enrollment_id' => $enrollments[0]->id,
            'total_average' => 75,
        ],
        [
            'enrollment_id' => $enrollments[0]->id,
            'total_average' => 80,
        ],
        [
            'enrollment_id' => $enrollments[1]->id,
            'total_average' => 60,
        ],
        [
            'enrollment_id' => $enrollments[1]->id,
            'total_average' => 62,
        ],
        [
            'enrollment_id' => $enrollments[3]->id,
            'total_average' => 69.2,
        ],
        [
            'enrollment_id' => $enrollments[4]->id,
            'total_average' => 70.1,
        ]
    ))->create();

    // 0 and 1 on 2025-07-01, 3 and 4 on 2025-07-02
    // Enrollment 0 average = 82
    // Enrollment 1 average = 61
    // Enrollment 2 average = null - testing no exam requried
    // Enrollment 3 average = 69.2 - testing rounding
    // Enrollment 4 average = 69.7 - testing rounding

    $response = $this->enrollmentReportService->getRegisteredStudentReport($filters, true);

    expect($response['total'])->toMatchArray([
        "total_students" => 5,
        "male_hostel_students" => 0,
        "female_hostel_students" => 0,
        "mark_counts" => [
            "Under-50" => 0,
            "50-54.99" => 0,
            "55-59.99" => 0,
            "60-64.99" => 1,
            "65-69.99" => 1,
            "70-74.99" => 1,
            "75-79.99" => 0,
            "80-Above" => 1,
        ]
    ]);

    $response = $response['data']->toArray();
    expect($response)->toHaveCount(2)
        ->and($response['2025-07-01'])->toMatchArray([
            "date" => "2025-07-01",
            "total_students" => 3,
            "male_hostel_students" => 0,
            "female_hostel_students" => 0,
            "mark_counts" => [
                "Under-50" => 0,
                "50-54.99" => 0,
                "55-59.99" => 0,
                "60-64.99" => 1,
                "65-69.99" => 0,
                "70-74.99" => 0,
                "75-79.99" => 0,
                "80-Above" => 1
            ]
        ])
        ->and($response['2025-07-02'])->toMatchArray([
            "date" => "2025-07-02",
            "total_students" => 2,
            "male_hostel_students" => 0,
            "female_hostel_students" => 0,
            "mark_counts" => [
                "Under-50" => 0,
                "50-54.99" => 0,
                "55-59.99" => 0,
                "60-64.99" => 0,
                "65-69.99" => 1,
                "70-74.99" => 1,
                "75-79.99" => 0,
                "80-Above" => 0
            ]
        ]);
});

test('getRegisteredStudentReport - male/female hostel grouping', function () {

    $filters = [
        'from_date' => '2025-06-01',
        'to_date' => '2025-10-10',
    ];

    Enrollment::factory(12)->state(new Sequence(
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'is_hostel' => true, 'gender' => Gender::FEMALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'is_hostel' => true, 'gender' => Gender::FEMALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
    ))->create();


    // 2025-07-01 has 1 male, 2 female hostel student
    // 2025-07-02 has 3 male hostel student
    $response = $this->enrollmentReportService->getRegisteredStudentReport($filters, true);

    expect($response['total'])->toMatchArray([
        "total_students" => 12,
        "male_hostel_students" => 4,
        "female_hostel_students" => 2,
        "mark_counts" => [
            "Under-50" => 0,
            "50-54.99" => 0,
            "55-59.99" => 0,
            "60-64.99" => 0,
            "65-69.99" => 0,
            "70-74.99" => 0,
            "75-79.99" => 0,
            "80-Above" => 0,
        ]
    ]);

    $response = $response['data']->toArray();
    expect($response)->toHaveCount(2)
        ->and($response['2025-07-01'])->toMatchArray([
            "date" => "2025-07-01",
            "total_students" => 6,
            "male_hostel_students" => 1,
            "female_hostel_students" => 2,
            "mark_counts" => [
                "Under-50" => 0,
                "50-54.99" => 0,
                "55-59.99" => 0,
                "60-64.99" => 0,
                "65-69.99" => 0,
                "70-74.99" => 0,
                "75-79.99" => 0,
                "80-Above" => 0
            ]
        ])
        ->and($response['2025-07-02'])->toMatchArray([
            "date" => "2025-07-02",
            "total_students" => 6,
            "male_hostel_students" => 3,
            "female_hostel_students" => 0,
            "mark_counts" => [
                "Under-50" => 0,
                "50-54.99" => 0,
                "55-59.99" => 0,
                "60-64.99" => 0,
                "65-69.99" => 0,
                "70-74.99" => 0,
                "75-79.99" => 0,
                "80-Above" => 0
            ]
        ]);
});

test('getRegisteredStudentReport - pdf test', function () {

    $filters = [
        'from_date' => '2025-06-01',
        'to_date' => '2025-10-10',
    ];

    $enrollments = Enrollment::factory(13)->state(new Sequence(
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'is_hostel' => true, 'gender' => Gender::FEMALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-01', 'is_hostel' => true, 'gender' => Gender::FEMALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
        ['payment_date' => '2025-07-02', 'is_hostel' => true, 'gender' => Gender::MALE, 'payment_status' => EnrollmentPaymentStatus::PAID->value],
    ))->create();


    EnrollmentExam::factory(12)->state(new Sequence(
        [
            'enrollment_id' => $enrollments[0]->id,
            'total_average' => 90,
        ],
        [
            'enrollment_id' => $enrollments[0]->id,
            'total_average' => 75,
        ],
        [
            'enrollment_id' => $enrollments[0]->id,
            'total_average' => 80,
        ],
        [
            'enrollment_id' => $enrollments[1]->id,
            'total_average' => 90,
        ],
        [
            'enrollment_id' => $enrollments[1]->id,
            'total_average' => 75,
        ],
        [
            'enrollment_id' => $enrollments[1]->id,
            'total_average' => 80,
        ],
        [
            'enrollment_id' => $enrollments[2]->id,
            'total_average' => 90,
        ],
        [
            'enrollment_id' => $enrollments[2]->id,
            'total_average' => 75,
        ],
        [
            'enrollment_id' => $enrollments[2]->id,
            'total_average' => 80,
        ],
        [
            'enrollment_id' => $enrollments[3]->id,
            'total_average' => 90,
        ],
        [
            'enrollment_id' => $enrollments[3]->id,
            'total_average' => 75,
        ],
        [
            'enrollment_id' => $enrollments[3]->id,
            'total_average' => 80,
        ]
    ))->create();


    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    $enrollmentReportService = resolve(EnrollmentReportService::class);

    $file_name = 'enrollment-report-by-student-registration-unit-test';
    SnappyPdf::fake();


    $enrollmentReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName('reports.enrollment.student-registration-report')
        ->setFileName('enrollment-report-by-student-registration-unit-test')
        ->getRegisteredStudentReport($filters);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.enrollment.student-registration-report');

    // check title
    SnappyPdf::assertSee('2026 Newly Registered Student Report');

    // check headers
    $headers = ['Date', 'Total Registered', '60-64', '65-69', '70-74', '75-79', '80-Above', 'Male Hostel Student', 'Female Hostel Student'];
    foreach ($headers as $header) {
        SnappyPdf::assertSee($header);
    }

    // Check total registed students per date (6 for 1st Day, 7 for 2nd Day)
    SnappyPdf::assertSee(6);
    SnappyPdf::assertSee(7);

    // Check total male and female hostel students (1 male, 2 female for 1st day, 3 male for 2nd day)
    SnappyPdf::assertSee(1);
    SnappyPdf::assertSee(2);
    SnappyPdf::assertSee(3);

    // Check total students scoring 80 and above on first day (4)
    SnappyPdf::assertSee(4);
});

test('getDailyCollectionReportData, test excel content', function () {
    $enrollments = Enrollment::factory(3)->create();

    $billing_documents = BillingDocument::factory(3)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 300.50,
            'bill_to_type' => $enrollments[0]->getBillToType(),
            'bill_to_id' => $enrollments[0]->getBillToId(),
            'bill_to_name' => $enrollments[0]->getBillToName(),
            'bill_to_reference_number' => $enrollments[0]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-01 12:00:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
            'document_date' => '2024-11-01',
            'amount_after_tax' => 400.50,
            'bill_to_type' => $enrollments[1]->getBillToType(),
            'bill_to_id' => $enrollments[1]->getBillToId(),
            'bill_to_name' => $enrollments[1]->getBillToName(),
            'bill_to_reference_number' => $enrollments[1]->getBillToReferenceNumber(),
            'paid_at' => '2024-11-01 12:00:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
            'document_date' => '2024-10-01',
            'amount_after_tax' => 100.50,
            'bill_to_type' => $enrollments[2]->getBillToType(),
            'bill_to_id' => $enrollments[2]->getBillToId(),
            'bill_to_name' => $enrollments[2]->getBillToName(),
            'bill_to_reference_number' => $enrollments[2]->getBillToReferenceNumber(),
            'paid_at' => '2024-10-01 15:59:59',
        ],
    ))->create();

    $unpaid_items = UnpaidItem::factory(3)->state(new Sequence(
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Enrollment Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Enrollment Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[2]->id,
            'description' => 'Enrollment Fees Jan 2024',
            'period' => '2024-01-01',
        ],
    ))->create();

    $product = Product::factory()->create([
        'name->en' => 'Enrollment Fees',
        'code' => Product::CODE_ENROLLMENT_EXAM,
        'gl_account_code' => GlAccount::CODE_ENROLLMENT,
    ]);

    $line_items = BillingDocumentLineItem::factory(3)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Enrollment Fees Jan 2024',
            'billable_item_id' => $unpaid_items[0]->id,
            'billable_item_type' => get_class($unpaid_items[0]),
            'amount_before_tax' => 300.50,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Enrollment Fees Jan 2024',
            'billable_item_id' => $unpaid_items[1]->id,
            'billable_item_type' => get_class($unpaid_items[1]),
            'amount_before_tax' => 400.50,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'description' => 'Enrollment Fees Jan 2024',
            'billable_item_id' => $unpaid_items[2]->id,
            'billable_item_type' => get_class($unpaid_items[2]),
            'amount_before_tax' => 100.50,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
    ))->create();

    $payment_methods = PaymentMethod::factory(3)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
        [
            'code' => PaymentMethod::CODE_BANK_TRANSFER,
            'name' => 'Bank Transfer',
        ],
    ))->create();

    $maybank = Bank::factory()->create();

    $payment_requests = PaymentRequest::factory(3)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id,
            'bank_id' => null, // Cash does not require bank
            'amount' => 300.50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'bank_id' => null, // FPX does not require bank
            'amount' => 400.50,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'payment_method_id' => $payment_methods[2]->id, // BANK_TRANSFER
            'bank_id' => $maybank->id, // to MAYBANK
            'amount' => 100.50,
        ],
    ))->create();

    $payments = Payment::factory(3)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => $billing_documents[0]->reference_no,
            'amount_received' => 300.50,
            'payment_source_type' => PaymentRequest::class,
            'payment_source_id' => $payment_requests[0]->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 400.50,
            'payment_source_type' => PaymentRequest::class,
            'payment_source_id' => $payment_requests[1]->id,
        ],
        [
            'billing_document_id' => $billing_documents[2]->id,
            'payment_method_id' => $payment_methods[2]->id, // Bank Transfer
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 100.50,
            'payment_source_type' => PaymentRequest::class,
            'payment_source_id' => $payment_requests[2]->id,
        ],
    ))->create();


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('url');
    });

    $enrollment_report_service = resolve(EnrollmentReportService::class);

    $file_name = 'enrollment-report-by-daily-collection';

    $expected_headers = [
        __('general.no'),
        __('general.payment_date'),
        __('general.invoice_date'),
        __('general.invoice_no'),
        __('general.bill_to_name'),
        __('general.bill_to_reference_no'),
        'Enrollment Fees (Jan 2024)',
        __('general.total_amount'),
        __('general.bank_charges'),
        __('general.payment'),
        __('general.reference_no'),
        __('general.bank'),
    ];

    // Test Excel
    Excel::fake();

    $enrollment_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'payment_date_from' => '2024-01-01',
            'payment_date_to' => '2024-12-31',
        ]);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (EnrollmentsByDailyCollectionExport $export) use (
            $expected_headers,
            $billing_documents,
            $enrollments,
            $payments,
            $maybank,
        ) {
            expect($export->headings())->toBe($expected_headers);

            $collection = $export->collection();

            // 3 billing documents + 1 total row for summary
            expect($collection->count())->toBe(4);

            // first enrollment
            $first_row = $collection->get(0);
            expect($first_row['no'])->toBe(1);
            expect($first_row['payment_date'])->toBe('2024-12-01');
            expect($first_row['invoice_date'])->toBe('2024-12-01');
            expect($first_row['invoice_no'])->toBe($billing_documents[0]->reference_no);
            expect($first_row['bill_to_name'])->toBe(join(' ', array_values($enrollments[0]->getTranslations('name'))));
            expect($first_row['bill_to_reference_no'])->toBe($enrollments[0]->getBillToReferenceNumber());
            expect($first_row['Enrollment Fees (Jan 2024)'])->toBe('300.50');
            expect($first_row['total_amount'])->toBe('300.50');
            expect($first_row['bank_charges'])->toBe('0');
            expect($first_row['payment'])->toBe('CASH');
            expect($first_row['reference_no'])->toBe($billing_documents[0]->reference_no);
            expect($first_row['bank'])->toBe('');

            // second enrollment
            $second_row = $collection->get(1);
            expect($second_row['no'])->toBe(2);
            expect($second_row['payment_date'])->toBe('2024-11-01');
            expect($second_row['invoice_date'])->toBe('2024-11-01');
            expect($second_row['invoice_no'])->toBe($billing_documents[1]->reference_no);
            expect($second_row['bill_to_name'])->toBe(join(' ', array_values($enrollments[1]->getTranslations('name'))));
            expect($second_row['bill_to_reference_no'])->toBe($enrollments[1]->getBillToReferenceNumber());
            expect($second_row['Enrollment Fees (Jan 2024)'])->toBe('400.50');
            expect($second_row['total_amount'])->toBe('400.50');
            expect($second_row['bank_charges'])->toBe(0.85);
            expect($second_row['payment'])->toBe('FPX');
            expect($second_row['reference_no'])->toBe($payments[1]->payment_reference_no);
            expect($second_row['bank'])->toBe('');

            // third enrollment
            $third_row = $collection->get(2);
            expect($third_row['no'])->toBe(3);
            expect($third_row['payment_date'])->toBe('2024-10-01');
            expect($third_row['invoice_date'])->toBe('2024-10-01');
            expect($third_row['invoice_no'])->toBe($billing_documents[2]->reference_no);
            expect($third_row['bill_to_name'])->toBe(join(' ', array_values($enrollments[2]->getTranslations('name'))));
            expect($third_row['bill_to_reference_no'])->toBe($enrollments[2]->getBillToReferenceNumber());
            expect($third_row['Enrollment Fees (Jan 2024)'])->toBe('100.50');
            expect($third_row['total_amount'])->toBe('100.50');
            expect($third_row['bank_charges'])->toBe('0');
            expect($third_row['payment'])->toBe('BANK');
            expect($third_row['reference_no'])->toBe($payments[2]->payment_reference_no);
            expect($third_row['bank'])->toBe("{$maybank->name} ({$maybank->swift_code})");

            // summary row
            $summary_row = $collection->get(3);
            expect($summary_row['no'])->toBe('');
            expect($summary_row['payment_date'])->toBe('');
            expect($summary_row['invoice_date'])->toBe('');
            expect($summary_row['invoice_no'])->toBe('');
            expect($summary_row['bill_to_name'])->toBe('');
            expect($summary_row['bill_to_reference_no'])->toBe('Total');
            expect($summary_row['Enrollment Fees (Jan 2024)'])->toBe('801.50');
            expect($summary_row['total_amount'])->toBe('801.50');
            expect($summary_row['bank_charges'])->toBe('0.85');

            // payment dates
            expect($export->paymentDateFrom)->toBe('2024-01-01');
            expect($export->paymentDateTo)->toBe('2024-12-31');

            // payment methods summary
            $expected_payment_methods = [
                'CASH' => '300.50',
                'FPX' => '400.50',
                'BANK' => '100.50',
            ];
            expect($export->paymentMethods)->toBe($expected_payment_methods);

            // products summary
            $expected_products = [
                'Enrollment Fees (Jan 2024)' => '801.50',
            ];
            expect($export->products)->toBe($expected_products);

            // totals
            expect($export->totalForProducts)->toBe('801.50');
            expect($export->summaryAmountForBankCharges)->toBe('0.85');

            return true;
        }
    );
});
