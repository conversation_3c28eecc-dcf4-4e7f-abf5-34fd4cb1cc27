<?php

use App\Helpers\PermissionHelper;
use App\Interfaces\PermissionDependencies;
use App\Models\Merchant;
use App\Models\User;
use Database\Seeders\PermissionSeeder;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        PermissionSeeder::class,
    ]);
});

test('getAvailablePermissionsFor', function () {
    // e.g.
    // 'AAA' => [
    //     'BBB',
    //     'CCC'
    // ],
    // 'DDD' => [
    //     'BBB',
    // ],
    // PermissionHelper::getAvailablePermissionsFor(BBB) return AAA + DDD and BBB(itself), PermissionHelper::getAvailablePermissionsFor(CCC) return AAA and CCC(itself)
    foreach (PermissionDependencies::DEPENDENCIES as $key => $permissions) {
        expect(in_array($key, PermissionHelper::getAvailablePermissionsFor($permissions)))->toBeTrue();
        foreach ($permissions as $permission) {
            expect(in_array($permission, PermissionHelper::getAvailablePermissionsFor($permissions)))->toBeTrue();
        }
    }
});

test('setMerchantIdByPermission', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    // user without view-all-merchant
    // logged in user not linked to merchant account
    try {
        $input = ['test' => 123];
        $input = PermissionHelper::setMerchantIdByPermission($input);
    } catch (\Throwable $th) {
        expect($th->getMessage())->toEqual('You do not have permission to access this resource.')
            ->and($th->getCode())->toEqual(4008);
    }

    $merchant = Merchant::factory()->create([
        'user_id' => $user->id,
    ]);

    $input = ['test' => 123];
    expect(PermissionHelper::setMerchantIdByPermission($input))->toBe([
        'test' => 123,
        'merchant_id' => $merchant->id,
    ]);

    // user with view-all-merchant (is merchant)
    $user->givePermissionTo('view-all-merchant');
    $user->refresh();
    $input = ['test' => 123];
    expect(PermissionHelper::setMerchantIdByPermission($input))->toBe([
        'test' => 123,
    ]);

    // user with view-all-merchant (not merchant)
    $user2 = User::factory()->create();
    Sanctum::actingAs($user2);
    $user2->givePermissionTo('view-all-merchant');
    $input = ['test' => 123];
    expect(PermissionHelper::setMerchantIdByPermission($input))->toBe([
        'test' => 123,
    ]);
});
