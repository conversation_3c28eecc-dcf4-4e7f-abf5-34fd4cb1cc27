<?php


use App\Models\Bank;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\LegalEntity;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\PaymentTerm;
use App\Models\Student;
use App\Models\Tax;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Services\Billing\BillingDocumentPostingService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

beforeEach(function () {

    $this->legalEntity = LegalEntity::factory()->create();
    $this->student = Student::factory()->has(User::factory()->state([]))->create([]);

    $this->system = Employee::factory()->create([
        'employee_number' => 'SYSTEM',
    ]);
    $this->employee = Employee::factory()->create();

    $this->bank = Bank::factory()->create([
        'name' => 'MAYBANK',
    ]);
    $this->bankAccount = BankAccount::factory()->create([
        'bank_id' => $this->bank->id,
        'bankable_id' => $this->legalEntity->id,
        'bankable_type' => LegalEntity::class,
    ]);
    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);

});

test('test getdata only paid billing documents', function () {

    $student2 = Student::factory()->create();

    $invoices = BillingDocument::factory(7)->state(new Sequence(
        [
            'document_date' => '2025-01-10',
            'paid_at' => '2025-01-17 15:59:59',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 30,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
        [
            'document_date' => '2025-01-12',
            'paid_at' => '2025-01-17 16:00:00',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 40,
            'bill_to_type' => $student2->getBillToType(),
            'bill_to_id' => $student2->getBillToId(),
            'reference_no' => 'ABC123',
        ],
        [
            'document_date' => '2025-01-13',
            'paid_at' => '2025-01-19 01:00:00',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_WALLET_TOPUP,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 50,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
        [
            'document_date' => '2025-01-19',
            'paid_at' => null,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_WALLET_TOPUP,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
            'amount_before_tax' => 60,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
        [
            'document_date' => '2025-01-19',
            'paid_at' => null,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_VOIDED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
            'amount_before_tax' => 10,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
        [
            'document_date' => '2025-01-17',
            'paid_at' => '2025-01-16 16:00:01',
            'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_OTHERS,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 30,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
        [
            'document_date' => '2025-01-30',
            'paid_at' => '2025-01-30 09:00:00',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_before_tax' => 70,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
    ))->create();
    
    $fpx_payment_method = PaymentMethod::factory()->create([
        'code' => PaymentMethod::CODE_FPX,
    ]);

    foreach ($invoices as $invoice) {

        $unpaid_item = UnpaidItem::factory()->create([
            'status' => UnpaidItem::STATUS_PENDING,
            'billing_document_id' => $invoice->id,
            'amount_before_tax' => 1,
            'paid_at' => null,
        ]);

        BillingDocumentLineItem::factory()->create([
            'billing_document_id' => $invoice->id,
            'billable_item_id' => $unpaid_item->id,
            'billable_item_type' => get_class($unpaid_item),
        ]);

        Payment::factory()->create([
            'billing_document_id' => $invoice->id,
            'payment_method_id' => $fpx_payment_method->id,
            'payment_reference_no' => 'FPX-' . Carbon::parse($invoice->paid_at, 'UTC')->tz(config('school.timezone'))->format('ymd'),
            'amount_received' => $invoice->amount_after_tax,
        ]);
    }

    $service = app()->make(BillingDocumentPostingService::class);

    // test with no date from/to
    expect(function () use (&$service) {
        $service->query();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(36012);
    }, __('system_error.36012'));

    // test with period filter
    $service->init()
        ->setPaymentDateFrom('2025-01-18')
        ->setPaymentDateTo('2025-01-19')
        ->query();

    expect($service->getDataset())->toHaveCount(2)
        ->and($service->getDataset()->pluck('id'))->toContain($invoices[1]->id, $invoices[2]->id);


    // test with period filter & status filter
    $service->init()
        ->setPaymentDateFrom('2025-01-19')
        ->setPaymentDateTo('2025-01-19')
        ->setStatus(BillingDocument::STATUS_CONFIRMED)
        ->query();

    expect($service->getDataset())->toHaveCount(1)
        ->and($service->getDataset()->pluck('id'))->toContain($invoices[2]->id);

    // test with period filter & payment status filter
    $service->init()
        ->setPaymentDateFrom('2025-01-19')
        ->setPaymentDateTo('2025-01-19')
        ->query();

    expect($service->getDataset())->toHaveCount(1)
        ->and($service->getDataset()->pluck('id'))->toContain($invoices[2]->id);


    // test with period filter & reference no
    $service->init()
        ->setPaymentDateFrom('2025-01-17')
        ->setPaymentDateTo('2025-01-19')
        ->setReferenceNo('ABC123')
        ->query();

    expect($service->getDataset())->toHaveCount(1)
        ->and($service->getDataset()->pluck('id'))->toContain($invoices[1]->id);


    // test with period filter & sub_type
    $service->init()
        ->setPaymentDateFrom('2025-01-17')
        ->setPaymentDateTo('2025-01-19')
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->query();

    expect($service->getDataset())->toHaveCount(2)
        ->and($service->getDataset()->pluck('id'))->toContain($invoices[0]->id, $invoices[1]->id);

    // test with period filter & type
    $service->init()
        ->setPaymentDateFrom('2025-01-17')
        ->setPaymentDateTo('2025-01-17')
        ->setType(BillingDocument::TYPE_INVOICE)
        ->query();

    expect($service->getDataset())->toHaveCount(1)
        ->and($service->getDataset()->pluck('id'))->toContain($invoices[0]->id);

    // test object has relationships loaded
    $service->init()
        ->setPaymentDateFrom('2025-01-17')
        ->setPaymentDateTo('2025-01-19')
        ->setReferenceNo('ABC123')
        ->query();

    $target = $service->getDataset()->first();

    expect($target->relationLoaded('billTo'))->toBeTrue()
        ->and($target->billTo->id)->toBe($student2->id)
        ->and($target->relationLoaded('payments'))->toBeTrue()
        ->and($target->relationLoaded('lineItems'))->toBeTrue()
        ->and($target->lineItems->first())->toBeInstanceOf(BillingDocumentLineItem::class)
        ->and($target->lineItems->first()->relationLoaded('billableItem'))->toBeTrue()
        ->and($target->lineItems->first()->billableItem)->toBeInstanceOf(UnpaidItem::class);

});

test('generateDescription for school fees', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'period' => '2025-02-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'period' => '2025-03-01',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    // exclude advance and discount
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'offset_billing_document_id' => 1,
        'is_discount' => false,
        'discount_id' => null,
        'amount_before_tax' => -100,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'offset_billing_document_id' => null,
        'is_discount' => true,
        'discount_id' => 1,
        'amount_before_tax' => -200,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDescription($invoice))->toBe('FEES 2025/02 - 2025/03');

});

test('generateDescription for enrollment fees', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_ENROLLMENT_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $unpaid_item1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_ENROLLMENT,
        'period' => '2025-02-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_ENROLLMENT,
        'period' => '2025-03-01',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item1->id,
        'billable_item_type' => get_class($unpaid_item1),
        'gl_account_code' => GlAccount::CODE_ENROLLMENT,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'gl_account_code' => GlAccount::CODE_ENROLLMENT,
    ]);

    // exclude advance and discount
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'offset_billing_document_id' => 1,
        'is_discount' => false,
        'discount_id' => null,
        'amount_before_tax' => -100,
        'gl_account_code' => GlAccount::CODE_ENROLLMENT,
    ]);
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'offset_billing_document_id' => null,
        'is_discount' => true,
        'discount_id' => 1,
        'amount_before_tax' => -200,
        'gl_account_code' => GlAccount::CODE_ENROLLMENT,
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDescription($invoice))->toBe('ENROLLMENT 2025/02 - 2025/03');
});


test('generateDescription for hostel fees', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'period' => '2025-02-01',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item->id,
        'billable_item_type' => get_class($unpaid_item),
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDescription($invoice))->toBe('HOSTEL 2025/02');

    $unpaid_item->period = '2024-12-01';
    $unpaid_item->save();
    $invoice->refresh();

    expect($service->generateDescription($invoice))->toBe('HOSTEL 2024/12');

});


test('generateDescription for book fees', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_BOOK_FEES,
        'period' => '2025-02-01',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item->id,
        'billable_item_type' => get_class($unpaid_item),
        'gl_account_code' => GlAccount::CODE_BOOK_FEES,
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDescription($invoice))->toBe('BOOK 2025');

    $unpaid_item->period = '2024-12-01';
    $unpaid_item->save();
    $invoice->refresh();

    expect($service->generateDescription($invoice))->toBe('BOOK 2024');

});


test('generateDescription for exam fees', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_EXAM_FEES,
        'period' => '2025-02-01',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item->id,
        'billable_item_type' => get_class($unpaid_item),
        'gl_account_code' => GlAccount::CODE_EXAM_FEES,
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDescription($invoice))->toBe('EXAM 2025/02');

    $unpaid_item->period = '2024-12-01';
    $unpaid_item->save();
    $invoice->refresh();

    expect($service->generateDescription($invoice))->toBe('EXAM 2024/12');

});


test('generateDescription for continuing student registration fees', function () {

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_ENROLLMENT_REGISTRATION_CONTINUING,
        'period' => '2025-01-01',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item->id,
        'billable_item_type' => get_class($unpaid_item),
        'gl_account_code' => GlAccount::CODE_ENROLLMENT_REGISTRATION_CONTINUING,
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDescription($invoice))->toBe('FEES-REGISTRATION-EXISTING 2025');

    $unpaid_item->period = '2024-12-01';
    $unpaid_item->save();
    $invoice->refresh();

    expect($service->generateDescription($invoice))->toBe('FEES-REGISTRATION-EXISTING 2024');

});


test('generateDescription combination of multiple fees with specific sorting', function () {

    // will always sort School fees first , followed by hostel fees, then others.

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $unpaid_item3 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'period' => '2025-01-01',
    ]);
    $unpaid_item = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'period' => '2025-01-01',
    ]);
    $unpaid_item5 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_EXAM_FEES,
        'period' => '2025-01-01',
    ]);
    $unpaid_item2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'period' => '2025-02-01',
    ]);
    $unpaid_item4 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PENDING,
        'billing_document_id' => $invoice->id,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'period' => '2025-02-01',
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item3->id,
        'billable_item_type' => get_class($unpaid_item3),
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
    ]);
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item->id,
        'billable_item_type' => get_class($unpaid_item),
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item5->id,
        'billable_item_type' => get_class($unpaid_item5),
        'gl_account_code' => GlAccount::CODE_EXAM_FEES,
    ]);
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item2->id,
        'billable_item_type' => get_class($unpaid_item2),
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice->id,
        'billable_item_id' => $unpaid_item4->id,
        'billable_item_type' => get_class($unpaid_item4),
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDescription($invoice))->toBe('FEES 2025/01 - 2025/02 & HOSTEL 2025/01 - 2025/02 & EXAM 2025/01');


});

test('generateDebtorCode', function () {

    // with H prefix
    $this->student->student_number = 'H12345678';
    $this->student->save();

    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDebtorCode($invoice))->toBe('300-*********');

    // with other prefix
    $this->student->student_number = 'HZ1234G5998';
    $this->student->save();

    $invoice->refresh();

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDebtorCode($invoice))->toBe('300-*********');

    // with no prefix
    $this->student->student_number = '20241155661';
    $this->student->save();

    $invoice->refresh();

    $service = app()->make(BillingDocumentPostingService::class);
    expect($service->generateDebtorCode($invoice))->toBe('300-020241155661');

});

test('generatePaymentInfo', function () {
    // multiple payments
    $invoice = BillingDocument::factory()->create([
        'document_date' => '2025-01-17',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 30,
        'bill_to_type' => $this->student->getBillToType(),
        'bill_to_id' => $this->student->getBillToId(),
    ]);

    $cheque_payment_method = PaymentMethod::factory()->create([
        'code' => PaymentMethod::CODE_CHEQUE,
    ]);
    $fpx_payment_method = PaymentMethod::factory()->create([
        'code' => PaymentMethod::CODE_FPX,
    ]);
    $bank_transfer_payment_method = PaymentMethod::factory()->create([
        'code' => PaymentMethod::CODE_BANK_TRANSFER,
    ]);
    $cash_payment_method = PaymentMethod::factory()->create([
        'code' => PaymentMethod::CODE_CASH,
    ]);

    $payments = Payment::factory(3)->state(new Sequence(
        [
            'billing_document_id' => $invoice->id,
            'payment_method_id' => $cheque_payment_method->id,
            'payment_reference_no' => 'CHEQUE-123456',
            'amount_received' => 10,
            'paid_at' => Carbon::now(),
        ],
        [

            'billing_document_id' => $invoice->id,
            'payment_method_id' => $cash_payment_method->id,
            'payment_reference_no' => 'PAIDCASH',
            'amount_received' => 10,
            'paid_at' => Carbon::now(),
        ],
        [
            'billing_document_id' => $invoice->id,
            'payment_method_id' => $bank_transfer_payment_method->id,
            'payment_reference_no' => 'BANK-123456',
            'amount_received' => 10,
            'paid_at' => Carbon::now(),
        ],
    ))->create();

    $service = app()->make(BillingDocumentPostingService::class);

    /** 
     * multiple payments NON-FPX
     * 
     * - expect no bank charges
     * - expect to see all payment methods
     * - expect to see all payment reference numbers
     */
    
    $result = $service->generatePaymentInfo($invoice);

    expect($result)->toEqual([
        'payment_method' => 'CHEQUE, CASH, BANK',
        'cheque_no' => 'CHEQUE-123456, PAIDCASH, BANK-123456',
        'bank_charges' => 0,
    ]);
    

    /**
     * all repeated CASH payment methods
     * - expect to see only 1 payment method
     * - expect to see 3 payment reference number
     * - expect to see no bank charges
     */

    foreach ($payments as $index => $payment) {
        $payment->payment_method_id = $cash_payment_method->id;
        $payment->payment_reference_no = 'CASH-' . $index + 1;
        $payment->save();
    }

    $result = $service->generatePaymentInfo($invoice->refresh());

    expect($result)->toEqual([
        'payment_method' => 'CASH',
        'cheque_no' => 'CASH-1, CASH-2, CASH-3',
        'bank_charges' => 0,
    ]);

    /**
     * all repeated FPX payment methods
     * - expect to see only 1 payment method
     * - expect to see 3 payment reference number
     * - expect to see bank charges -> only FPX has bank charges -> 0.6 + (0.001 * total_invoice) - less than 250
     */

    $default_ref = 'FPX-' . Carbon::parse($invoice->paid_at, 'UTC')->tz(config('school.timezone'))->format('ymd');

    foreach ($payments as $index => $payment) {
        $payment->payment_method_id = $fpx_payment_method->id;
        $payment->payment_reference_no = $default_ref;
        $payment->save();
    }

    $result = $service->generatePaymentInfo($invoice->refresh());

    expect($result)->toEqual([
        'payment_method' => 'FPX',
        'cheque_no' => "$default_ref, $default_ref, $default_ref",
        'bank_charges' => ($invoice->amount_after_tax * 0.001) + 0.6,
    ]);


    /**
     * all repeated FPX payment methods
     * - expect to see only 1 payment method
     * - expect to see 3 payment reference number
     * - expect to see bank charges -> only FPX has bank charges -> 0.85 - more than 250
     */

    $invoice->amount_after_tax = 3000;
    $invoice->save();

    foreach ($payments as $index => $payment) {
        $payment->amount_received = 1000;
        $payment->save();
    }

    $result = $service->generatePaymentInfo($invoice->refresh());

    expect($result)->toEqual([
        'payment_method' => 'FPX',
        'cheque_no' => "$default_ref, $default_ref, $default_ref",
        'bank_charges' => 0.85,
    ]);
});


test('transform', function () {

    $student2 = Student::factory()->create();

    $invoices = BillingDocument::factory(3)->state(new Sequence(
        [
            'document_date' => '2025-01-16',
            'paid_at' => '2025-01-17 16:08:19',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_after_tax' => 40,
            'bill_to_type' => $student2->getBillToType(),
            'bill_to_id' => $student2->getBillToId(),
            'reference_no' => 'ABC123',
        ],
        [
            'document_date' => '2025-01-10',
            'paid_at' => '2025-01-17 08:00:00',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_after_tax' => 30,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
        [
            'document_date' => '2025-01-19',
            'paid_at' => '2025-01-19 15:59:59',
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'amount_after_tax' => 50,
            'bill_to_type' => $this->student->getBillToType(),
            'bill_to_id' => $this->student->getBillToId(),
        ],
    ))->create();

    $fpx_payment_method = PaymentMethod::factory()->create([
        'code' => PaymentMethod::CODE_FPX,
    ]);

    foreach ($invoices as $invoice) {

        $unpaid_item = UnpaidItem::factory()->create([
            'status' => UnpaidItem::STATUS_PENDING,
            'billing_document_id' => $invoice->id,
            'paid_at' => null,
        ]);

        BillingDocumentLineItem::factory()->create([
            'billing_document_id' => $invoice->id,
            'billable_item_id' => $unpaid_item->id,
            'billable_item_type' => get_class($unpaid_item),
        ]);

        Payment::factory()->create([
            'billing_document_id' => $invoice->id,
            'payment_method_id' => $fpx_payment_method->id,
            'payment_reference_no' => 'FPX-' . Carbon::parse($invoice->paid_at, 'UTC')->tz(config('school.timezone'))->format('ymd'),
            'amount_received' => $invoice->amount_after_tax,
        ]);
    }

    $service = app()->make(BillingDocumentPostingService::class);

    $service->setPaymentDateFrom('2025-01-17')
        ->setPaymentDateTo('2025-01-19')
        ->query()
        ->transform();

    $data = $service->getTransformedDataset()->toArray();

    foreach ([1, 0, 2] as $index => $index2) {

        $transformed_invoice = $data[$index];

        $existing_invoice = $invoices[$index2];

        expect($transformed_invoice)->toMatchArray([
            'DocNo' => '<<New>>',
            'DocDate' => Carbon::parse($existing_invoice->paid_at, 'UTC')->tz(config('school.timezone'))->format('d/m/Y'),
            'DebtorCode' => $service->generateDebtorCode($existing_invoice),
            'Description' => $service->generateDescription($existing_invoice),
            'CurrencyCode' => $existing_invoice->currency_code,
            'ToHomeRate' => 1,
            'ToDebtorRate' => 1,
            'PaymentMethod' => 'CASH AT BANK-PBB',
            'ChequeNo' => 'FPX-' . Carbon::parse($existing_invoice->paid_at, 'UTC')->tz(config('school.timezone'))->format('ymd'),
            'PaymentAmt' => $existing_invoice->amount_after_tax,
            'BankCharge' => $existing_invoice->amount_after_tax <= 250 ? bcadd('0.6', bcmul('0.001', $existing_invoice->amount_after_tax, 2), 2) : '0.85',
            'ToBankRate' => 1,
            'DocNo2' => $existing_invoice->reference_no,
        ]);
    }


});


test('generateExcelAndGetFileUrl', function () {

    $user = User::factory()->create();
    Auth::onceUsingId($user->id);

    $transformed_data = [
        [
            'DocNo' => '<<New>>',
            'DocDate' => '01/12/2024',
            'DebtorCode' => '300-0102932',
            'Description' => 'SOME-DESCRIPTION-2025/12 & OTHERS-2026',
            'CurrencyCode' => 'MYR',
            'ToHomeRate' => 1,
            'ToDebtorRate' => 1,
            'PaymentMethod' => 'CASH AT BANK-PBB',
            'ChequeNo' => 'FPX-251120',
            'PaymentAmt' => 1561.36,
            'BankCharge' => 1,
            'ToBankRate' => 1,
            'DocNo2' => 'INV2023984',
        ],
        [
            'DocNo' => '<<New>>',
            'DocDate' => '18/12/2024',
            'DebtorCode' => '300-********',
            'Description' => 'SOME-DESCRIPTION-2025/08 & OTHERS-2026',
            'CurrencyCode' => 'MYR',
            'ToHomeRate' => 1,
            'ToDebtorRate' => 1,
            'PaymentMethod' => 'CASH AT BANK-PBB',
            'ChequeNo' => 'FPX-251122',
            'PaymentAmt' => 85.36,
            'BankCharge' => 1,
            'ToBankRate' => 1,
            'DocNo2' => 'INV2023933',
        ]
    ];

    $service = app()->make(BillingDocumentPostingService::class);

    Carbon::setTestNow('2025-01-17 16:00:00');

    $url = $service
        ->setPaymentDateFrom('2025-01-01')
        ->setPaymentDateTo('2025-01-31')
        ->setTransformedDataset(collect($transformed_data))
        ->generateExcelAndGetFileUrl();

    $bucket_name = config('filesystems.disks.s3-downloads.bucket');
    $filename = 'autocount-posting-********-********-' . md5($user->id . Carbon::now()->timestamp);

    // clean up
    sleep(1);
    Storage::disk('s3-downloads')->delete('posting/' . $filename . '.xlsx');

    expect($url)
        ->not()->toBeNull()
        ->and(preg_match("/^https\:\/\/$bucket_name\.s3\.ap-southeast-1\.amazonaws\.com\/download\/posting\/" . $filename . "\.xlsx$/",
            $url))->toBe(1);

});

test('generateExcelAndGetFileUrl empty no data', function () {

    $user = User::factory()->create();
    Auth::onceUsingId($user->id);

    $transformed_data = [];

    $service = app()->make(BillingDocumentPostingService::class);

    Carbon::setTestNow('2025-01-17 16:00:00');

    $url = $service
        ->setPaymentDateFrom('2025-01-01')
        ->setPaymentDateTo('2025-01-31')
        ->setTransformedDataset(collect($transformed_data))
        ->generateExcelAndGetFileUrl();

    expect($url)->toBeNull();

});
