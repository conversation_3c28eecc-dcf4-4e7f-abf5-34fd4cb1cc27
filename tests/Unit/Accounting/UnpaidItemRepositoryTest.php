<?php

use App\Enums\ClassType;
use App\Models\ClassModel;
use App\Models\Contractor;
use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\Guardian;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\Merchant;
use App\Models\Product;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\UnpaidItem;
use App\Repositories\UnpaidItemRepository;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->unpaidItemRepository = app(UnpaidItemRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(UnpaidItem::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->unpaidItemRepository->getModelClass();

    expect($response)->toEqual(UnpaidItem::class);
});

test('getAll()', function () {
    $unpaid_items = UnpaidItem::factory(3)->student()->create();

    $response = $this->unpaidItemRepository->getAll(['order_by' => 'id'])->toArray();

    expect($response)->toEqual($unpaid_items->toArray());
});

test('getAllPaginated()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $student = Student::factory()->create();
    $guardian = Guardian::factory()->create();
    $employee = Employee::factory()->create();
    $contractor = Contractor::factory()->create();
    $merchant = Merchant::factory()->create();

    $filter_options = [
        'student_id' => [
            'bill_to_type' => get_class($student),
            'bill_to_id' => $student->id,
        ],
        'guardian_id' => [
            'bill_to_type' => get_class($guardian),
            'bill_to_id' => $guardian->id,
        ],
        'employee_id' => [
            'bill_to_type' => get_class($employee),
            'bill_to_id' => $employee->id,
        ],
        'contractor_id' => [
            'bill_to_type' => get_class($contractor),
            'bill_to_id' => $contractor->id,
        ],
        'merchant_id' => [
            'bill_to_type' => get_class($merchant),
            'bill_to_id' => $merchant->id,
        ],
    ];

    $keys = [
        'first' => UnpaidItem::factory()->create([
            'bill_to_type' => get_class($student),
            'bill_to_id' => $student->id,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2024-10-10',
        ]),
        'second' => UnpaidItem::factory()->create([
            'bill_to_type' => get_class($guardian),
            'bill_to_id' => $guardian->id,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-01-01',
        ]),
        'third' => UnpaidItem::factory()->create([
            'bill_to_type' => get_class($employee),
            'bill_to_id' => $employee->id,
            'status' => UnpaidItem::STATUS_PAID,
            'period' => '2025-01-02',
        ]),
        'fourth' => UnpaidItem::factory()->create([
            'bill_to_type' => get_class($contractor),
            'bill_to_id' => $contractor->id,
            'status' => UnpaidItem::STATUS_PENDING,
            'period' => '2025-01-02',
        ]),
        'fifth' => UnpaidItem::factory()->create([
            'bill_to_type' => get_class($merchant),
            'bill_to_id' => $merchant->id,
            'status' => UnpaidItem::STATUS_VOIDED,
            'period' => '2025-01-03',
        ]),
    ];

    $actual_filters = isset($filter_options[$filter_by]) ? $filter_options[$filter_by] : [$filter_by => $filter_value];

    $result = $this->unpaidItemRepository->getAllPaginated($actual_filters)->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'filter by student_id' => [1, 'student_id', null, ['first']],
    'filter by guardian_id' => [1, 'guardian_id', null, ['second']],
    'filter by employee_id' => [1, 'employee_id', null, ['third']],
    'filter by contractor_id' => [1, 'contractor_id', null, ['fourth']],
    'filter by merchant_id' => [1, 'merchant_id', null, ['fifth']],
    'filter by status = UNPAID' => [2, 'status', 'UNPAID', ['first', 'second']],
    'filter by status = PAID' => [1, 'status', 'PAID', ['third']],
    'filter by status = PENDING' => [1, 'status', 'PENDING', ['fourth']],
    'filter by status = VOIDED' => [1, 'status', 'VOIDED', ['fifth']],
    'filter by period_from = 2025-01-03' => [1, 'period_from', '2025-01-03', ['fifth']],
    'filter by period_from = 2024-10-10' => [5, 'period_from', '2024-10-10', ['first', 'second', 'third', 'fourth', 'fifth']],
    'filter by period_to = 2024-10-10' => [1, 'period_to', '2024-10-10', ['first']],
    'filter by period_to = 2025-01-02' => [4, 'period_to', '2025-01-02', ['first', 'second', 'third', 'fourth']],
    'sort by id asc' => [5, 'order_by', ['id' => 'asc'], ['first', 'second', 'third', 'fourth', 'fifth']],
    'sort by id desc' => [5, 'order_by', ['id' => 'desc'], ['fifth', 'fourth', 'third', 'second', 'first']],
    'sort by status asc' => [5, 'order_by', ['status' => 'asc'], ['third', 'fourth', 'first', 'second', 'fifth']],
    'sort by status desc' => [5, 'order_by', ['status' => 'desc'], ['fifth', 'first', 'second', 'fourth', 'third']],
    'sort by period asc' => [5, 'order_by', ['period' => 'asc'], ['first', 'second', 'third', 'fourth', 'fifth']],
    'sort by period desc' => [5, 'order_by', ['period' => 'desc'], ['fifth', 'third', 'fourth', 'second', 'first']],
]);

test('getUnpaidItemsByIds()', function () {
    $unpaid_item_1 = UnpaidItem::factory()->student()->create([
        'period' => '2024-01-01',
    ]);

    $unpaid_item_2 = UnpaidItem::factory()->student()->create([
        'period' => '2024-02-01',
    ]);

    $response = $this->unpaidItemRepository->getUnpaidItemsByIds([$unpaid_item_1->id, $unpaid_item_2->id])->toArray();

    expect($response)->toEqual([
        $unpaid_item_1->load(['billTo', 'product'])->toArray(),
        $unpaid_item_2->load(['billTo', 'product'])->toArray(),
    ]);


    /**
     * empty array ids
     */
    $response = $this->unpaidItemRepository->getUnpaidItemsByIds([]);

    expect($response)->toHaveCount(0);
});

test('getAllPaginated(), filter by id', function () {
    $student = Student::factory()->create();

    $unpaid_item_1 = UnpaidItem::factory()->create([
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period' => '2024-10-10',
    ]);

    $unpaid_item_2 = UnpaidItem::factory()->create([
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period' => '2025-01-01',
    ]);

    $unpaid_item_3 = UnpaidItem::factory()->create([
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period' => '2025-01-01',
    ]);

    $payload = [
        'id' => $unpaid_item_1->id,
    ];

    $result = $this->unpaidItemRepository->getAllPaginated($payload)->toArray();

    expect($result['data'])->toHaveCount(1)
        ->toHaveKey('0.id', $unpaid_item_1->id);

    $payload = [
        'id' => [
            $unpaid_item_1->id,
            $unpaid_item_2->id,
        ],
    ];

    $result = $this->unpaidItemRepository->getAllPaginated($payload)->toArray();

    expect($result['data'])->toHaveCount(2)
        ->toHaveKey('0.id', $unpaid_item_1->id)
        ->toHaveKey('1.id', $unpaid_item_2->id);
});

test('getStudentOutstandingBalanceReportByClass', function () {
    Carbon::setTestNow('2024-07-01 00:00:00');
    
    $employee = Employee::factory()->create([
        'name->en' => 'teacher class 1',
    ]);
    $employee3 = Employee::factory()->create([
        'name->en' => 'teacher class 3',
    ]);
    $semester_class = SemesterClass::factory()->create(['class_id' => ClassModel::factory(['name->en' => 'class 1', 'type' => ClassType::PRIMARY]), 'homeroom_teacher_id' => $employee->id]);
    $semester_class2 = SemesterClass::factory()->create(['class_id' => ClassModel::factory(['name->en' => 'class 2', 'type' => ClassType::SOCIETY]), 'homeroom_teacher_id' => null]);
    $semester_class3 = SemesterClass::factory()->create(['class_id' => ClassModel::factory(['name->en' => 'class 3', 'type' => ClassType::PRIMARY]), 'homeroom_teacher_id' => $employee3->id]);

    $student1 = Student::factory()->create([
        'name->en' => 'student 1',
        'name->zh' => '学生1',
        'student_number' => '00002',
    ]);
    $student2 = Student::factory()->create([
        'name->en' => 'student 2',
        'name->zh' => '学生2',
        'student_number' => '00001',
    ]);
    $student3 = Student::factory()->create([
        'name->en' => 'student 3',
        'name->zh' => '学生3',
        'student_number' => '00003',
    ]);
    $student4 = Student::factory()->create([
        'name->en' => 'student 4',
        'name->zh' => '学生4',
        'student_number' => '00004',
    ]);
    $student5 = Student::factory()->create([
        'name->en' => 'student 5',
        'name->zh' => '学生5',
        'student_number' => '00005',
        'is_active' => false,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::SOCIETY->value,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student3->id,
        'class_type' => ClassType::SOCIETY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student4->id,
        'class_type' => ClassType::SOCIETY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class3->semester_setting_id,
        'semester_class_id' => $semester_class3->id,
        'student_id' => $student5->id,
        'class_type' => ClassType::PRIMARY->value,
    ]);

    // student 1 (class 1 and class 2) - Jan 2 unpaid, 100+150. Feb 1 unpaid, 200, Mar 1 unpaid, 200 (total 650)
    // student 2 (class 1) - Dec 1 unpaid, 50, Jan 1 unpaid, 50
    // student 3 (class 2)- Jan 1 unpaid, 150
    // student 4 (class 2) - Jan 1 unpaid, 150
    // student 5 (class 3) - got unpaid, but inactive

    $product_1 = Product::factory()->create([
        'name' => [
            'en' => '学费', 'zh' => '学费'
        ]
    ]);
    $product_2 = Product::factory()->create([
        'name' => [
            'en' => '社团费', 'zh' => '社团费'
        ]
    ]);
    $product_3 = Product::factory()->create([
        'name' => [
            'en' => '宿舍费', 'zh' => '宿舍费'
        ]
    ]);

    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '学费',
        'period' => '2024-01-01',
        'amount_before_tax' => 100,
        'product_id' => $product_1->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '社团费',
        'period' => '2024-01-01',
        'amount_before_tax' => 150,
        'product_id' => $product_2->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '学费',
        'period' => '2024-02-01',
        'amount_before_tax' => 200,
        'product_id' => $product_1->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '宿舍费',
        'period' => '2024-02-01',
        'amount_before_tax' => 200,
        'product_id' => $product_3->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student2->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '学费',
        'period' => '2023-12-01',
        'amount_before_tax' => 50,
        'product_id' => $product_1->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student2->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '学费',
        'period' => '2024-01-01',
        'amount_before_tax' => 50,
        'product_id' => $product_1->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student3->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '社团费',
        'period' => '2024-01-01',
        'amount_before_tax' => 150,
        'product_id' => $product_2->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student4->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '社团费',
        'period' => '2024-01-01',
        'amount_before_tax' => 150,
        'product_id' => $product_2->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student4->id,
        'status' => UnpaidItem::STATUS_PAID,
        'description' => '社团费',
        'period' => '2023-12-01',
        'amount_before_tax' => 150,
        'product_id' => $product_2->id,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student5->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => 'NOTIMPORTANT',
        'period' => '2023-12-01',
        'amount_before_tax' => 150,
        'product_id' => $product_2->id,
    ]);


    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    app()->setLocale('en');
    $payload = [$semester_class->id, $semester_class2->id, $semester_class3->id];
    $data = $this->unpaidItemRepository->getStudentOutstandingBalanceReportByClass($payload, '2024-02-01');

    expect($data)->toMatchArray([
        "students_group_by_semester_class" => [
            $semester_class->id => [
                "students" => [
                    [
                        "no" => 1,
                        "student_no" => "00001",
                        "name" => [
                            "en" => "student 2",
                            "zh" => "学生2",
                        ],
                        "description" => "学费 (Dec), 学费 (Jan)",
                        "months_group_by_year" => [
                            2023 => [
                                12 => "Dec",
                            ],
                            2024 => [
                                1 => "Jan",
                            ],
                        ],
                        "month_concat_string" => "2023(Dec), 2024(Jan)",
                        "student_total_amount" => "100.00",
                    ],
                    [
                        "no" => 2,
                        "student_no" => "00002",
                        "name" => [
                            "en" => "student 1",
                            "zh" => "学生1",
                        ],
                        "description" => "学费 (Jan), 社团费 (Jan), 学费 (Feb), 宿舍费 (Feb)",
                        "months_group_by_year" => [
                            2024 => [
                                1 => "Jan",
                                2 => "Feb",
                            ],
                        ],
                        "month_concat_string" => "2024(Jan, Feb)",
                        "student_total_amount" => "650.00",
                    ],
                ],
                "class_details" => [
                    "class_name" => "class 1",
                    "homeroom_teacher" => "teacher class 1",
                    "class_total_amount" => "750.00",
                ],
            ],
            $semester_class2->id => [
                "students" => [
                    [
                        "no" => 1,
                        "student_no" => "00002",
                        "name" => [
                            "en" => "student 1",
                            "zh" => "学生1",
                        ],
                        "description" => "学费 (Jan), 社团费 (Jan), 学费 (Feb), 宿舍费 (Feb)",
                        "months_group_by_year" => [
                            2024 => [
                                1 => "Jan",
                                2 => "Feb",
                            ],
                        ],
                        "month_concat_string" => "2024(Jan, Feb)",
                        "student_total_amount" => "650.00",
                    ],
                    [
                        "no" => 2,
                        "student_no" => "00003",
                        "name" => [
                            "en" => "student 3",
                            "zh" => "学生3",
                        ],
                        "description" => "社团费 (Jan)",
                        "months_group_by_year" => [
                            2024 => [
                                1 => "Jan",
                            ],
                        ],
                        "month_concat_string" => "2024(Jan)",
                        "student_total_amount" => "150.00",
                    ],
                    [
                        "no" => 3,
                        "student_no" => "00004",
                        "name" => [
                            "en" => "student 4",
                            "zh" => "学生4",
                        ],
                        "description" => "社团费 (Jan)",
                        "months_group_by_year" => [
                            2024 => [
                                1 => "Jan",
                            ],
                        ],
                        "month_concat_string" => "2024(Jan)",
                        "student_total_amount" => "150.00",
                    ],
                ],
                "class_details" => [
                    "class_name" => "class 2",
                    "homeroom_teacher" => "-",
                    "class_total_amount" => "950.00",
                ],
            ],
            $semester_class3->id => [
                "class_details" => [
                    "class_name" => "class 3",
                    "homeroom_teacher" => "teacher class 3",
                    "class_total_amount" => "0.00",
                ]
            ],
        ],
        "total_debtors_and_arrears_group_by_year_month" => [
            "2023(12)" => [
                "total_debtors" => 1,
                "total_arrears" => "50.00",
            ],
            "2024(1)" => [
                "total_debtors" => 4,
                "total_arrears" => "600.00",
            ],
            "2024(2)" => [
                "total_debtors" => 1,
                "total_arrears" => "400.00",
            ],
        ],
        "total_debtors_and_arrears_group_by_consecutive_year_month" => [
            "2023(Dec), 2024(Jan)" => [
                "total_debtors" => 1,
                "total_arrears" => "100.00",
            ],
            "2024(Jan, Feb)" => [
                "total_debtors" => 1,
                "total_arrears" => "650.00",
            ],
            "2024(Jan)" => [
                "total_debtors" => 2,
                "total_arrears" => "300.00",
            ],
        ],
        "all_classes_total_arrears" => "1,050.00",
        "all_classes_total_debtors" => 4,
    ]);
});

test('getStudentOutstandingBalanceReportByClass, for discount related', function () {
    Carbon::setTestNow('2025-07-01 00:00:00');
    $semester_year = SemesterYearSetting::factory()->create(['year' => 2025]);

    $semester_setting = SemesterSetting::factory()->create([
        'semester_year_setting_id' => $semester_year->id,
        'is_current_semester' => true
    ]);

    $teachers = Employee::factory(3)->state(new Sequence(
        [
            'name->en' => 'J111 Teacher',
        ],
        [
            'name->en' => 'J222 Teacher',
        ],
        [
            'name->en' => 'Photography Teacher',
        ],
    ))->create();

    $classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
        ],
        [
            'name->en' => 'J222',
            'type' => ClassType::PRIMARY,
        ],
        [
            'name->en' => 'Photography',
            'type' => ClassType::SOCIETY,
        ],
    ))->create();

    $semester_classes = SemesterClass::factory(3)->state(new Sequence(
        [
            'class_id' => $classes[0]->id,
            'homeroom_teacher_id' => $teachers[0]->id,
            'semester_setting_id' => $semester_setting->id,
        ],
        [
            'class_id' => $classes[1]->id,
            'homeroom_teacher_id' => $teachers[1]->id,
            'semester_setting_id' => $semester_setting->id,
        ],
        [
            'class_id' => $classes[2]->id,
            'homeroom_teacher_id' => $teachers[2]->id,
            'semester_setting_id' => $semester_setting->id,
        ],
    ))->create();

    $students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Student A+ Scholarship', // in J111 + Photography
            'student_number' => '00001',
        ],
        [
            'name->en' => 'Student 2', // in J111
            'student_number' => '00002',
        ],
        [
            'name->en' => 'Student 3', // in J222
            'student_number' => '00003',
        ],
        [
            'name->en' => 'Student 4', // in Photography
            'student_number' => '00004',
        ],
    ))->create();

    $student_classes = StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id, // J111
            'student_id' => $students[0]->id, // Student A+ Scholarship
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[2]->id, // Photography
            'student_id' => $students[0]->id, // Student A+ Scholarship
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id, // J111
            'student_id' => $students[1]->id, // Student 2
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id, // J222
            'student_id' => $students[2]->id, // Student 3
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[2]->id, // Photography
            'student_id' => $students[3]->id, // Student 4
        ],
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    /**
     * Student A+ Scholarship - 3 unpaid items for SCHOOL_FEE  , 1 unpaid item for SOCIETY_FEE
     * Student 2 - 2 unpaid items for SCHOOL_FEE
     * Student 3 - 1 unpaid item for SCHOOL_FEE
     * Student 4 - 1 unpaid item for SOCIETY_FEE
     */

    $unpaid_items = UnpaidItem::factory(8)->state(new Sequence(
    // Student A+ Scholarship Fees
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[0]->id,
            'description' => 'School Fees Jan 2025',
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'status' => UnpaidItem::STATUS_PENDING,
            'period' => '2025-01-01',
            'amount_before_tax' => 75,
        ],
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[0]->id,
            'description' => 'School Fees Feb 2025',
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-02-01',
            'amount_before_tax' => 75,
        ],
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[0]->id,
            'description' => 'School Fees Mac 2025',
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-03-01',
            'amount_before_tax' => 75,
        ],
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[0]->id,
            'description' => 'Cocu Fees 2025',
            'gl_account_code' => GlAccount::CODE_OTHERS,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-07-01',
            'amount_before_tax' => 20,
        ],
        // Student 2 Fees
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[1]->id,
            'description' => 'School Fees Jan 2025',
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-01-01',
            'amount_before_tax' => 75,
        ],
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[1]->id,
            'description' => 'School Fees Feb 2025',
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-02-01',
            'amount_before_tax' => 75,
        ],
        // Student 3 Fees
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[2]->id,
            'description' => 'School Fees Jan 2025',
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-01-01',
            'amount_before_tax' => 75,
        ],
        // Student 4 Fees , only COCU Fees
        [
            'bill_to_type' => Student::class,
            'bill_to_id' => $students[3]->id,
            'description' => 'Cocu Fees 2025',
            'gl_account_code' => GlAccount::CODE_OTHERS,
            'status' => UnpaidItem::STATUS_UNPAID,
            'period' => '2025-07-01',
            'amount_before_tax' => 20,
        ],
    ))->create();

    $payload = [$semester_classes[0]->id, $semester_classes[1]->id, $semester_classes[2]->id];


    /**
     * NO DISCOUNT
     *
     */

    $data = $this->unpaidItemRepository->getStudentOutstandingBalanceReportByClass($payload, '2025-12-01');

    expect($data['students_group_by_semester_class'])->toHaveCount(3) // 3 classes
    // J111
    ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'])->toHaveCount(2) // 2 students

    // Student A+ Scholarship J111
    ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][0]['student_no'])->toBe('00001')
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][0]['name']['en'])->toBe('Student A+ Scholarship')
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][0]['student_total_amount'])->toBe('245.00') // 75 + 75 + 75 + 20 = 245

        // Student 2 J111
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][1]['student_no'])->toBe('00002')
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][1]['name']['en'])->toBe('Student 2')
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][1]['student_total_amount'])->toBe('150.00') // 75 + 75 = 150

        // --------------------------------------------------

        // J222
        ->and($data['students_group_by_semester_class'][$semester_classes[1]->id]['students'])->toHaveCount(1) // 1 student

        // Student 3 J222
        ->and($data['students_group_by_semester_class'][$semester_classes[1]->id]['students'][0]['student_no'])->toBe('00003')
        ->and($data['students_group_by_semester_class'][$semester_classes[1]->id]['students'][0]['name']['en'])->toBe('Student 3')
        ->and($data['students_group_by_semester_class'][$semester_classes[1]->id]['students'][0]['student_total_amount'])->toBe('75.00') // 75

        // --------------------------------------------------

        // Photography
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'])->toHaveCount(2) // 2 student

        // Student A+ Scholarship Photography
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][0]['student_no'])->toBe('00001')
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][0]['name']['en'])->toBe('Student A+ Scholarship')
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][0]['student_total_amount'])->toBe('245.00') // 75 + 75 + 75 + 20 = 245

        // Student 4 Photography
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][1]['student_no'])->toBe('00004')
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][1]['name']['en'])->toBe('Student 4')
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][1]['student_total_amount'])->toBe('20.00') // 20
    ;


    /**
     *
     * APPLY DISCOUNTS TO STUDENTS
     *
     */

    $discount_20_STUDENT_A = DiscountSetting::factory()->create([
        'userable_id' => $students[0]->id,
        'userable_type' => Student::class,
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 20
    ]);

    $discount_30_STUDENT_A = DiscountSetting::factory()->create([
        'userable_id' => $students[0]->id,
        'userable_type' => Student::class,
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 30
    ]);

    $discount_10_STUDENT_2 = DiscountSetting::factory()->create([
        'userable_id' => $students[1]->id,
        'userable_type' => Student::class,
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 10
    ]);

    $discount_10_STUDENT_3 = DiscountSetting::factory()->create([
        'userable_id' => $students[2]->id,
        'userable_type' => Student::class,
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 75
    ]);

    $discount_10_STUDENT_4 = DiscountSetting::factory()->create([ // this discount will not apply to Student 4 because the gl_account_codes does not match
        'userable_id' => $students[3]->id,
        'userable_type' => Student::class,
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 10
    ]);

    $data = $this->unpaidItemRepository->getStudentOutstandingBalanceReportByClass($payload, '2025-12-01');

    expect($data['students_group_by_semester_class'])->toHaveCount(3) // 3 classes
    // J111
    ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'])->toHaveCount(2) // 2 students

    // Student A+ Scholarship J111
    ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][0]['student_no'])->toBe('00001')
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][0]['name']['en'])->toBe('Student A+ Scholarship')
        // FEE JAN = 75 + (-20) [$discount_20_STUDENT_A]  + (-30) [$discount_30_STUDENT_A] = 75 - 20 - 30 = 25
        // FEE FEB = 75 + (-20) [$discount_20_STUDENT_A]  + (-30) [$discount_30_STUDENT_A] = 75 - 20 - 30 = 25
        // FEE MAC = 75 + (-20) [$discount_20_STUDENT_A]  + (-30) [$discount_30_STUDENT_A] = 75 - 20 - 30 = 25
        // COCU FEE = 20
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][0]['student_total_amount'])->toBe('95.00') // 25 + 25 + 25 + 20 = 95

        // Student 2 J111
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][1]['student_no'])->toBe('00002')
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][1]['name']['en'])->toBe('Student 2')
        // FEE JAN = 75 + (-10) [$discount_10_STUDENT_2] = 75 - 10 = 65
        // FEE FEB = 75 + (-10) [$discount_10_STUDENT_2] = 75 - 10 = 65
        ->and($data['students_group_by_semester_class'][$semester_classes[0]->id]['students'][1]['student_total_amount'])->toBe('130.00') // 65 + 65 = 130

        // --------------------------------------------------

        // J222 (no students, but got class details, report will show 'No data available')
        // Student 3 J222
        // FEE JAN = 75 + (-75) [$discount_10_STUDENT_3] = 75 - 75 = 0 (total amount 0 so won't show in report)
        ->and(isset($data['students_group_by_semester_class'][$semester_classes[1]->id]['students']))->toBeFalse()
        ->and($data['students_group_by_semester_class'][$semester_classes[1]->id]['class_details'])->toBe([
            'class_name' => 'J222',
            'homeroom_teacher' => 'J222 Teacher',
            'class_total_amount' => '0.00',
        ])

        // --------------------------------------------------

        // Photography
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'])->toHaveCount(2) // 2 student

        // Student A+ Scholarship Photography
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][0]['student_no'])->toBe('00001')
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][0]['name']['en'])->toBe('Student A+ Scholarship')
        // FEE JAN = 75 + (-20) [$discount_20_STUDENT_A]  + (-30) [$discount_30_STUDENT_A] = 75 - 20 - 30 = 25
        // FEE FEB = 75 + (-20) [$discount_20_STUDENT_A]  + (-30) [$discount_30_STUDENT_A] = 75 - 20 - 30 = 25
        // FEE MAC = 75 + (-20) [$discount_20_STUDENT_A]  + (-30) [$discount_30_STUDENT_A] = 75 - 20 - 30 = 25
        // COCU FEE = 20
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][0]['student_total_amount'])->toBe('95.00') // 25 + 25 + 25 + 20 = 95

        // Student 4 Photography - dont have discount
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][1]['student_no'])->toBe('00004')
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][1]['name']['en'])->toBe('Student 4')
        // FEE COCU = 20
        ->and($data['students_group_by_semester_class'][$semester_classes[2]->id]['students'][1]['student_total_amount'])->toBe('20.00') // 20
    ;
});
