<?php

use App\Models\BillingDocument;
use App\Services\DocumentPrintService;
use Mo<PERSON>y\MockInterface;

test('regenerate single billing document by id', function () {
    // Create a mock of DocumentPrintService
    $this->mock(DocumentPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('setPrintable')->once()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andReturnSelf();
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('https://example.com/test.pdf');
    });

    // Create a test billing document with an existing receipt_url
    $old_receipt_url = 'https://example.com/old-test.pdf';
    $billing_document = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'receipt_url' => $old_receipt_url,
    ]);

    // Run the command with the billing document ID
    $this->artisan('billing:regenerate-document', ['ids' => [$billing_document->id]])
        ->expectsOutput('Processing 1 billing document(s)...')
        ->expectsOutput("Regenerating billing document: {$billing_document->reference_no}")
        ->expectsOutput("Old receipt URL: {$old_receipt_url}")
        ->expectsOutput('New receipt URL: https://example.com/test.pdf')
        ->expectsOutput("✓ Successfully regenerated billing document with ID {$billing_document->id}.")
        ->expectsOutput('=== Summary ===')
        ->expectsOutput('Successful: 1')
        ->assertExitCode(0);

    // Verify that the billing document was updated
    expect($billing_document->fresh()->receipt_url)->toBe('https://example.com/test.pdf');
});

test('regenerate multiple billing documents by ids', function () {
    // Create a mock of DocumentPrintService
    $this->mock(DocumentPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('setPrintable')->twice()->andReturnSelf();
        $mock->shouldReceive('generate')->twice()->andReturnSelf();
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')
            ->twice()
            ->andReturn('https://example.com/test1.pdf', 'https://example.com/test2.pdf');
    });

    // Create test billing documents
    $old_receipt_url_1 = 'https://example.com/old-test1.pdf';
    $old_receipt_url_2 = 'https://example.com/old-test2.pdf';

    $billing_document_1 = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'receipt_url' => $old_receipt_url_1,
    ]);

    $billing_document_2 = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'receipt_url' => $old_receipt_url_2,
    ]);

    // Run the command with multiple billing document IDs
    $this->artisan('billing:regenerate-document', ['ids' => [$billing_document_1->id, $billing_document_2->id]])
        ->expectsOutput('Processing 2 billing document(s)...')
        ->expectsOutput("Regenerating billing document: {$billing_document_1->reference_no}")
        ->expectsOutput("Old receipt URL: {$old_receipt_url_1}")
        ->expectsOutput('New receipt URL: https://example.com/test1.pdf')
        ->expectsOutput("✓ Successfully regenerated billing document with ID {$billing_document_1->id}.")
        ->expectsOutput("Regenerating billing document: {$billing_document_2->reference_no}")
        ->expectsOutput("Old receipt URL: {$old_receipt_url_2}")
        ->expectsOutput('New receipt URL: https://example.com/test2.pdf')
        ->expectsOutput("✓ Successfully regenerated billing document with ID {$billing_document_2->id}.")
        ->expectsOutput('=== Summary ===')
        ->expectsOutput('Successful: 2')
        ->assertExitCode(0);

    // Verify that both billing documents were updated
    expect($billing_document_1->fresh()->receipt_url)->toBe('https://example.com/test1.pdf');
    expect($billing_document_2->fresh()->receipt_url)->toBe('https://example.com/test2.pdf');
});

test('regenerate billing document not found', function () {
    // Run the command with a non-existent billing document ID
    $this->artisan('billing:regenerate-document', ['ids' => [999999]])
        ->expectsOutput('Processing 1 billing document(s)...')
        ->expectsOutput('Billing document with ID 999999 not found.')
        ->expectsOutput('=== Summary ===')
        ->expectsOutput('Successful: 0')
        ->expectsOutput('Not found: 1')
        ->assertExitCode(1);
});

test('regenerate billing documents with mixed valid and invalid ids', function () {
    // Create a mock of DocumentPrintService
    $this->mock(DocumentPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('setPrintable')->once()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andReturnSelf();
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('https://example.com/test.pdf');
    });

    // Create a test billing document
    $old_receipt_url = 'https://example.com/old-test.pdf';
    $billing_document = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'receipt_url' => $old_receipt_url,
    ]);

    $invalid_id = 999999;

    // Run the command with one valid and one invalid ID
    $this->artisan('billing:regenerate-document', ['ids' => [$billing_document->id, $invalid_id]])
        ->expectsOutput('Processing 2 billing document(s)...')
        ->expectsOutput("Regenerating billing document: {$billing_document->reference_no}")
        ->expectsOutput("Old receipt URL: {$old_receipt_url}")
        ->expectsOutput('New receipt URL: https://example.com/test.pdf')
        ->expectsOutput("✓ Successfully regenerated billing document with ID {$billing_document->id}.")
        ->expectsOutput("Billing document with ID {$invalid_id} not found.")
        ->expectsOutput('=== Summary ===')
        ->expectsOutput('Successful: 1')
        ->expectsOutput('Not found: 1')
        ->assertExitCode(1);

    // Verify that the valid billing document was updated
    expect($billing_document->fresh()->receipt_url)->toBe('https://example.com/test.pdf');
});

test('regenerate billing document with service failure', function () {
    // Create a mock of DocumentPrintService that throws an exception
    $this->mock(DocumentPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('setPrintable')->once()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andThrow(new \Exception('Service unavailable'));
    });

    // Create a test billing document
    $old_receipt_url = 'https://example.com/old-test.pdf';
    $billing_document = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'receipt_url' => $old_receipt_url,
    ]);

    // Run the command and expect it to handle the exception
    $this->artisan('billing:regenerate-document', ['ids' => [$billing_document->id]])
        ->expectsOutput('Processing 1 billing document(s)...')
        ->expectsOutput("Regenerating billing document: {$billing_document->reference_no}")
        ->expectsOutput("Old receipt URL: {$old_receipt_url}")
        ->expectsOutput('Error regenerating billing document: Service unavailable')
        ->expectsOutput("✗ Failed to regenerate billing document with ID {$billing_document->id}.")
        ->expectsOutput('=== Summary ===')
        ->expectsOutput('Successful: 0')
        ->expectsOutput('Failed: 1')
        ->assertExitCode(1);

    // Verify that the billing document was not updated
    expect($billing_document->fresh()->receipt_url)->toBe($old_receipt_url);
});

test('regenerate billing document with no ids provided', function () {
    // Run the command without any IDs
    $this->artisan('billing:regenerate-document', ['ids' => []])
        ->expectsOutput('No billing document IDs provided.')
        ->assertExitCode(1);
});

test('regenerate billing documents with mixed outcomes', function () {
    // Create a mock of DocumentPrintService
    $this->mock(DocumentPrintService::class, function (MockInterface $mock) {
        // First call succeeds
        $mock->shouldReceive('setPrintable')->once()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andReturnSelf();
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('https://example.com/test1.pdf');

        // Second call fails
        $mock->shouldReceive('setPrintable')->once()->andReturnSelf();
        $mock->shouldReceive('generate')->once()->andThrow(new \Exception('Network error'));
    });

    // Create test billing documents
    $old_receipt_url_1 = 'https://example.com/old-test1.pdf';
    $old_receipt_url_2 = 'https://example.com/old-test2.pdf';

    $billing_document_1 = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'receipt_url' => $old_receipt_url_1,
    ]);

    $billing_document_2 = BillingDocument::factory()->create([
        'status' => BillingDocument::STATUS_CONFIRMED,
        'receipt_url' => $old_receipt_url_2,
    ]);

    $invalid_id = 999999;

    // Run the command with success, failure, and not found scenarios
    $this->artisan('billing:regenerate-document', [
        'ids' => [$billing_document_1->id, $billing_document_2->id, $invalid_id]
    ])
        ->expectsOutput('Processing 3 billing document(s)...')
        // First document - success
        ->expectsOutput("Regenerating billing document: {$billing_document_1->reference_no}")
        ->expectsOutput("Old receipt URL: {$old_receipt_url_1}")
        ->expectsOutput('New receipt URL: https://example.com/test1.pdf')
        ->expectsOutput("✓ Successfully regenerated billing document with ID {$billing_document_1->id}.")
        // Second document - failure
        ->expectsOutput("Regenerating billing document: {$billing_document_2->reference_no}")
        ->expectsOutput("Old receipt URL: {$old_receipt_url_2}")
        ->expectsOutput('Error regenerating billing document: Network error')
        ->expectsOutput("✗ Failed to regenerate billing document with ID {$billing_document_2->id}.")
        // Third document - not found
        ->expectsOutput("Billing document with ID {$invalid_id} not found.")
        // Summary
        ->expectsOutput('=== Summary ===')
        ->expectsOutput('Successful: 1')
        ->expectsOutput('Failed: 1')
        ->expectsOutput('Not found: 1')
        ->assertExitCode(1);

    // Verify outcomes
    expect($billing_document_1->fresh()->receipt_url)->toBe('https://example.com/test1.pdf');
    expect($billing_document_2->fresh()->receipt_url)->toBe($old_receipt_url_2); // Should remain unchanged
});
