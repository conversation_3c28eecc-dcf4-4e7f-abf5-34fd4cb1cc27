<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Models\ClassModel;
use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');
    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->routeNamePrefix = 'reports.accounting';

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);
    $this->user->assignRole('Super Admin');
    Sanctum::actingAs($this->user);

    SnappyPdf::fake();
});

test('studentOutstandingBalanceReportByClass : download pdf , success', function () {
    $employee = Employee::factory()->create([
        'name->en' => 'teacher class 1',
    ]);
    $employee3 = Employee::factory()->create([
        'name->en' => 'teacher class 3',
    ]);
    $semester_class = SemesterClass::factory()->create(['class_id' => ClassModel::factory(['name->en' => 'class 1', 'type' => ClassType::PRIMARY]), 'homeroom_teacher_id' => $employee->id]);
    $semester_class2 = SemesterClass::factory()->create(['class_id' => ClassModel::factory(['name->en' => 'class 2', 'type' => ClassType::SOCIETY]), 'homeroom_teacher_id' => null]);
    $semester_class3 = SemesterClass::factory()->create(['class_id' => ClassModel::factory(['name->en' => 'class 3', 'type' => ClassType::PRIMARY]), 'homeroom_teacher_id' => $employee3->id]);

    $student1 = Student::factory()->create([
        'name->en' => 'student 1',
        'student_number' => '00002',
    ]);
    $student2 = Student::factory()->create([
        'name->en' => 'student 2',
        'student_number' => '00001',
    ]);
    $student3 = Student::factory()->create([
        'name->en' => 'student 3',
        'student_number' => '00003',
    ]);
    $student4 = Student::factory()->create([
        'name->en' => 'student 4',
        'student_number' => '00004',
    ]);
    $student5 = Student::factory()->create([
        'name->en' => 'student 5',
        'student_number' => '00005',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::SOCIETY->value,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student3->id,
        'class_type' => ClassType::SOCIETY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student4->id,
        'class_type' => ClassType::SOCIETY->value,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class3->semester_setting_id,
        'semester_class_id' => $semester_class3->id,
        'student_id' => $student5->id,
        'class_type' => ClassType::PRIMARY->value,
    ]);

    // student 1 (class 1 and class 2) - Jan 2 unpaid, 100+150. Feb 1 unpaid, 200, Mar 1 unpaid, 200 (total 650)
    // student 2 (class 1) - Dec 1 unpaid, 50, Jan 1 unpaid, 50
    // student 3 (class 2)- Jan 1 unpaid, 150
    // student 4 (class 2) - Jan 1 unpaid, 150 - discount 150 = 0
    // student 5 (class 3) - no unpaid
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_PENDING,
        'description' => '学费1月',
        'period' => '2024-01-01',
        'amount_before_tax' => 100,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '社团费1月',
        'period' => '2024-01-01',
        'amount_before_tax' => 150,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '学费2月',
        'period' => '2024-02-01',
        'amount_before_tax' => 200,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student1->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '宿舍费2月',
        'period' => '2024-02-01',
        'amount_before_tax' => 200,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student2->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '学费12月',
        'period' => '2023-12-01',
        'amount_before_tax' => 50,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student2->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '学费1月',
        'period' => '2024-01-01',
        'amount_before_tax' => 50,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student3->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '社团费1月',
        'period' => '2024-01-01',
        'amount_before_tax' => 150,
    ]);
    UnpaidItem::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student4->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'description' => '社团费1月',
        'period' => '2024-01-01',
        'amount_before_tax' => 150,
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $discount_20_STUDENT_A = DiscountSetting::factory()->create([
        'userable_id' => $student4->id,
        'userable_type' => Student::class,
        'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 150
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    Carbon::setTestNow('2024-07-01 00:00:00');

    $view_name = 'reports.accounting.student-outstanding-balance-report-by-class';

    $report_print_service_mock = $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('file_url');
    });

    // download pdf
    $payload = [
        'report_language' => 'en',
        'date_to' => '2024-02-01',
        'semester_class_ids' => [$semester_class->id, $semester_class2->id, $semester_class3->id],
        'export_type' => ExportType::PDF->value,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . '.student-outstanding-balance-report', $payload))->json();

    $filename = $report_print_service_mock->getFileName(); // get filename from ReportPrintService
    // to ensure url is retrieve using getFileUrl() function
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toEqual('file_url');

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $filename));
    SnappyPdf::assertViewIs($view_name);

    SnappyPdf::assertSee('Class : class 1');
    SnappyPdf::assertSee('Teacher Guardian : teacher class 1');
    SnappyPdf::assertSee($student2->student_number);
    SnappyPdf::assertSee($student2->getTranslation('name', 'en'));
    SnappyPdf::assertSee($student2->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('2023(Dec), 2024(Jan)');
    SnappyPdf::assertSee('100.00');

    SnappyPdf::assertSee($student1->student_number);
    SnappyPdf::assertSee($student1->getTranslation('name', 'en'));
    SnappyPdf::assertSee($student1->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('2024(Jan, Feb)');
    SnappyPdf::assertSee('650.00');

    // 650 + 100 (class 1)
    SnappyPdf::assertSee('750.00');

    SnappyPdf::assertSee('Class : class 2');
    SnappyPdf::assertSee('Teacher Guardian : -');
    SnappyPdf::assertSee($student3->student_number);
    SnappyPdf::assertSee($student3->getTranslation('name', 'en'));
    SnappyPdf::assertSee($student3->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('2024(Jan)');
    SnappyPdf::assertSee('150.00');
    // unpaid item's amount before tax = 0, shouldn't appear in report
    SnappyPdf::assertDontSee($student4->student_number);
    SnappyPdf::assertDontSee($student4->getTranslation('name', 'en'));
    SnappyPdf::assertDontSee($student4->getTranslation('name', 'zh'));

    // 650 (student 1) + 150 (class 2)
    SnappyPdf::assertSee('800.00');

    SnappyPdf::assertSee('Class : class 3');
    SnappyPdf::assertSee('Teacher Guardian : teacher class 3');
    SnappyPdf::assertSee('No data available');

    SnappyPdf::assertSee('2023(12)');
    SnappyPdf::assertSee('2024(1)');
    SnappyPdf::assertSee('2024(2)');
});
