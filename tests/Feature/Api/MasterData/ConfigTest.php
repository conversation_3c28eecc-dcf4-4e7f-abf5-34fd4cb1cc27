<?php

use App\Enums\Day;
use App\Models\Config;
use App\Models\User;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    Cache::clear();
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);
    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'master-data.configs';
});

test('admin index', function () {
    // get all config
    $array_config = Config::factory()->create([
        'key' => 'ENROLLMENT_DOCUMENT_TYPE',
        'value' => [
            'file_ic_front',
            'file_ic_back'
        ]
    ]);

    $assoc_array_config = Config::factory()->create([
        'key' => 'WALLET_TRANSFERABLE_MODEL',
        'value' => [
            'student' => 'student',
            'guardian' => 'guardian',
            'employee' => 'employee',
        ]
    ]);

    $response = $this->getJson(route("admin.$this->routeNamePrefix.index"))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            [
                'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
                'value' => [
                    'file_ic_front',
                    'file_ic_back'
                ],
                'category' => Config::CATEGORY_GENERAL,
                'type' => 'array',
                'options' => [
                    ['value' => 'file_ic_front', 'label' => 'IC Front'],
                    ['value' => 'file_ic_back', 'label' => 'IC Back'],
                    ['value' => 'file_upsr_result', 'label' => 'UPSR Result'],
                ],
            ],
            [
                'key' => Config::WALLET_TRANSFERABLE_MODEL,
                'value' => [
                    'student' => 'student',
                    'guardian' => 'guardian',
                    'employee' => 'employee',
                ],
                'category' => Config::CATEGORY_GENERAL,
                'type' => 'object',
                'options' => [
                    [
                        'value' => 'guardian',
                        'label' => 'Guardian',
                        'sub_value' => [
                            [
                                'value' => 'student',
                                'label' => 'Student'
                            ]
                        ]
                    ],
                    [
                        'value' => 'employee',
                        'label' => 'Employee',
                        'sub_value' => [
                            [
                                'value' => 'student',
                                'label' => 'Student'
                            ]
                        ]
                    ],
                    [
                        'value' => 'student',
                        'label' => 'Student',
                        'sub_value' => [
                            [
                                'value' => 'student',
                                'label' => 'Student'
                            ]
                        ]
                    ]
                ]
            ],
            [
                'key' => Config::PAYEX_MERCHANT_EMAIL,
                'value' => null,
                'category' => Config::CATEGORY_GENERAL,
                'type' => 'string',
                'options' => []
            ],
            [
                'key' => Config::PAYEX_MERCHANT_SECRET,
                'value' => Config::AVAILABLE_CONFIGS[Config::PAYEX_MERCHANT_SECRET],
                'category' => Config::CATEGORY_GENERAL,
                'type' => 'string',
                'options' => []
            ],
            [
                "key" => Config::DATA_COMPULSORY_LOCALE,
                "value" => Config::AVAILABLE_CONFIGS[Config::DATA_COMPULSORY_LOCALE],
                "category" => Config::CATEGORY_GENERAL,
                "type" => "array",
                "options" => ['en', 'zh']
            ],
            [
                "key" => Config::ENROLLMENT_FEES,
                "value" => Config::AVAILABLE_CONFIGS[Config::ENROLLMENT_FEES],
                "category" => Config::CATEGORY_GENERAL,
                "type" => "decimal",
                "options" => []
            ],
            [
                "key" => Config::ENROLLMENT_REPLY_TO_EMAIL,
                "value" => Config::AVAILABLE_CONFIGS[Config::ENROLLMENT_REPLY_TO_EMAIL],
                "category" => Config::CATEGORY_GENERAL,
                "type" => "string",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_BORROW_LIMIT_STUDENT,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_STUDENT],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_BORROW_LIMIT_EMPLOYEE,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_EMPLOYEE],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_BORROW_LIMIT_LIBRARIAN,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_LIBRARIAN],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_BORROW_LIMIT_OTHER,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_BORROW_LIMIT_OTHER],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::LOAN_PERIOD_DAY_STUDENT,
                "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_STUDENT],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::LOAN_PERIOD_DAY_EMPLOYEE,
                "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_EMPLOYEE],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::LOAN_PERIOD_DAY_LIBRARIAN,
                "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_LIBRARIAN],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::LOAN_PERIOD_DAY_OTHER,
                "value" => Config::AVAILABLE_CONFIGS[Config::LOAN_PERIOD_DAY_OTHER],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT,
                "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "boolean",
                "options" => []
            ],
            [
                "key" => Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE,
                "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "boolean",
                "options" => []
            ],
            [
                "key" => Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN,
                "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "boolean",
                "options" => []
            ],
            [
                "key" => Config::BORROW_WITH_UNRETURNED_BOOK_OTHER,
                "value" => Config::AVAILABLE_CONFIGS[Config::BORROW_WITH_UNRETURNED_BOOK_OTHER],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "boolean",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_FINE_PER_DAY_STUDENT,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_STUDENT],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "decimal",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_FINE_PER_DAY_EMPLOYEE,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_EMPLOYEE],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "decimal",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_FINE_PER_DAY_LIBRARIAN,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_LIBRARIAN],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "decimal",
                "options" => []
            ],
            [
                "key" => Config::LIBRARY_FINE_PER_DAY_OTHER,
                "value" => Config::AVAILABLE_CONFIGS[Config::LIBRARY_FINE_PER_DAY_OTHER],
                "category" => Config::CATEGORY_LIBRARY,
                "type" => "decimal",
                "options" => []
            ],
            [
                "key" => Config::HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS,
                "value" => Config::AVAILABLE_CONFIGS[Config::HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS],
                "category" => Config::CATEGORY_HOSTEL,
                "type" => "integer",
                "options" => []
            ],
            [
                "key" => Config::WALLET_DEPOSIT_MIN_AMOUNT,
                "value" => Config::AVAILABLE_CONFIGS[Config::WALLET_DEPOSIT_MIN_AMOUNT],
                "category" => Config::CATEGORY_GENERAL,
                "type" => "decimal",
                "options" => []
            ],
            [
                "key" => Config::WALLET_DEPOSIT_MAX_AMOUNT,
                "value" => Config::AVAILABLE_CONFIGS[Config::WALLET_DEPOSIT_MAX_AMOUNT],
                "category" => Config::CATEGORY_GENERAL,
                "type" => "decimal",
                "options" => []
            ],
            [
                'key' => Config::CANTEEN_OPEN_STATUS,
                'value' => true,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'boolean',
                'options' => []
            ],
            [
                'key' => Config::CANTEEN_OPENING_DAY,
                'value' => null,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'string',
                'options' => [
                    ['value' => Day::MONDAY->value, 'label' => Day::getLabel(Day::MONDAY)],
                    ['value' => Day::TUESDAY->value, 'label' => Day::getLabel(Day::TUESDAY)],
                    ['value' => Day::WEDNESDAY->value, 'label' => Day::getLabel(Day::WEDNESDAY)],
                    ['value' => Day::THURSDAY->value, 'label' => Day::getLabel(Day::THURSDAY)],
                    ['value' => Day::FRIDAY->value, 'label' => Day::getLabel(Day::FRIDAY)],
                    ['value' => Day::SATURDAY->value, 'label' => Day::getLabel(Day::SATURDAY)],
                    ['value' => Day::SUNDAY->value, 'label' => Day::getLabel(Day::SUNDAY)],
                ]
            ],
            [
                'key' => Config::CANTEEN_OPENING_TIME,
                'value' => null,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'time',
                'options' => []
            ],
            [
                'key' => Config::CANTEEN_CUTOFF_DAY,
                'value' => null,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'string',
                'options' => [
                    ['value' => Day::MONDAY->value, 'label' => Day::getLabel(Day::MONDAY)],
                    ['value' => Day::TUESDAY->value, 'label' => Day::getLabel(Day::TUESDAY)],
                    ['value' => Day::WEDNESDAY->value, 'label' => Day::getLabel(Day::WEDNESDAY)],
                    ['value' => Day::THURSDAY->value, 'label' => Day::getLabel(Day::THURSDAY)],
                    ['value' => Day::FRIDAY->value, 'label' => Day::getLabel(Day::FRIDAY)],
                    ['value' => Day::SATURDAY->value, 'label' => Day::getLabel(Day::SATURDAY)],
                    ['value' => Day::SUNDAY->value, 'label' => Day::getLabel(Day::SUNDAY)],
                ]
            ],
            [
                'key' => Config::CANTEEN_CUTOFF_TIME,
                'value' => null,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'time',
                'options' => []
            ],
            [
                'key' => Config::CANTEEN_NOTIFICATION_DAY,
                'value' => null,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'string',
                'options' => [
                    ['value' => Day::MONDAY->value, 'label' => Day::getLabel(Day::MONDAY)],
                    ['value' => Day::TUESDAY->value, 'label' => Day::getLabel(Day::TUESDAY)],
                    ['value' => Day::WEDNESDAY->value, 'label' => Day::getLabel(Day::WEDNESDAY)],
                    ['value' => Day::THURSDAY->value, 'label' => Day::getLabel(Day::THURSDAY)],
                    ['value' => Day::FRIDAY->value, 'label' => Day::getLabel(Day::FRIDAY)],
                    ['value' => Day::SATURDAY->value, 'label' => Day::getLabel(Day::SATURDAY)],
                    ['value' => Day::SUNDAY->value, 'label' => Day::getLabel(Day::SUNDAY)],
                ]
            ],
            [
                'key' => Config::CANTEEN_NOTIFICATION_TIME,
                'value' => null,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'time',
                'options' => []
            ],
            [
                'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
                'value' => null,
                'category' => Config::CATEGORY_CANTEEN,
                'type' => 'string',
                'options' => []
            ],
            [
                'key' => Config::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT,
                'value' => null,
                'category' => Config::CATEGORY_BANK_ACCOUNT,
                'type' => 'integer',
                'options' => []
            ],
            [
                'key' => Config::BANK_ACCOUNT_DEFAULT_SCHOOL_FEES,
                'value' => null,
                'category' => Config::CATEGORY_BANK_ACCOUNT,
                'type' => 'integer',
                'options' => []
            ],
            [
                'key' => Config::ATTENDANCE_TAP_CARD_INTERVAL_SECOND,
                'value' => 300,
                'category' => Config::CATEGORY_GENERAL,
                'type' => 'integer',
                'options' => []
            ],
        ]);
});

test('index', function () {
    // get all config
    Config::factory()->create([
        'key' => Config::CANTEEN_CUTOFF_DAY,
        'value' => 'FRIDAY'
    ]);

    Config::factory()->create([
        'key' => Config::CANTEEN_CUTOFF_TIME,
        'value' => '23:59'
    ]);

    Config::factory()->create([
        'key' => Config::WALLET_DEPOSIT_MIN_AMOUNT,
        'value' => '60'
    ]);

    Config::factory()->create([
        'key' => Config::WALLET_DEPOSIT_MAX_AMOUNT,
        'value' => '300'
    ]);

    $response = $this->getJson(route("$this->routeNamePrefix.index"))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0]['value'])->toBeString()
        ->and($response['data'][1]['value'])->toBeString()
        ->and($response['data'])->toMatchArray([
            [
                'key' => Config::CANTEEN_CUTOFF_DAY,
                'value' => 'FRIDAY',
            ],
            [
                'key' => Config::CANTEEN_CUTOFF_TIME,
                'value' => '23:59',
            ],
            [
                'key' => Config::WALLET_DEPOSIT_MIN_AMOUNT,
                'value' => '60',
            ],
            [
                'key' => Config::WALLET_DEPOSIT_MAX_AMOUNT,
                'value' => '300',
            ],
        ]);
});

test('index with no config setup', function () {
    $response = $this->getJson(route("$this->routeNamePrefix.index"))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            [
                'key' => Config::CANTEEN_CUTOFF_DAY,
                'value' => Config::AVAILABLE_CONFIGS[Config::CANTEEN_CUTOFF_DAY],
            ],
            [
                'key' => Config::CANTEEN_CUTOFF_TIME,
                'value' => Config::AVAILABLE_CONFIGS[Config::CANTEEN_CUTOFF_TIME],
            ],
            [
                'key' => Config::WALLET_DEPOSIT_MIN_AMOUNT,
                'value' => '10',
            ],
            [
                'key' => Config::WALLET_DEPOSIT_MAX_AMOUNT,
                'value' => '300',
            ]
        ]);
});

test('show : get config with value of type string', function () {
    $master_config = Config::factory()->create([
        'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_CANTEEN,
    ]);

    // test with key exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['key' => $master_config->key]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['value'])->toBeString()
        ->and($response['data'])->toMatchArray([
            'key' => $master_config->key,
            'value' => $master_config->value,
            'category' => Config::CATEGORY_CANTEEN,
        ]);
});

test('show : get config with value of type json', function () {
    $enrol_config = Config::factory()->create([
        'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
        'value' => [
            'ic_front',
            'ic_back'
        ],
        'category' => Config::CATEGORY_GENERAL,
    ]);

    // test with Config::ENROLLMENT_DOCUMENT_TYPE
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['key' => $enrol_config->key]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
            'value' => [
                'ic_front',
                'ic_back'
            ],
            'type' => 'array',
            'options' => Config::getAvailableConfigOptions(Config::ENROLLMENT_DOCUMENT_TYPE),
            'category' => Config::CATEGORY_GENERAL,
        ]);

    $wallet_config = Config::factory()->create([
        'key' => Config::WALLET_TRANSFERABLE_MODEL,
        'value' => [
            'guardian' => ['student'],
            'employee' => ['student'],
        ],
        'category' => Config::CATEGORY_GENERAL,
    ]);

    // test with Config::WALLET_TRANSFERABLE_MODEL
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['key' => $wallet_config->key]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'key' => Config::WALLET_TRANSFERABLE_MODEL,
            'value' => [
                'guardian' => ['student'],
                'employee' => ['student'],
            ],
            'category' => Config::CATEGORY_GENERAL,
            'type' => 'object',
            'options' => Config::getAvailableConfigOptions(Config::WALLET_TRANSFERABLE_MODEL)
        ]);
});

test('show : fail get config with non-existent key', function () {
    //test with key not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['key' => 'not exist']))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store : saving config with string data type', function () {
    //store new config
    $this->assertDatabaseCount('master_configs', 0);

    $payload = [
        'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_GENERAL,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
            'value' => '<EMAIL>',
            'category' => Config::CATEGORY_GENERAL,
        ]);

    $this->assertDatabaseCount('master_configs', 1);

    $this->assertDatabaseHas(resolve(Config::class)->getTable(), [
        'key' => $payload['key'],
        'value' => $payload['value'],
        'category' => $payload['category'],
    ]);

    // update config by key
    $new_payload = [
        'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
        'value' => '<EMAIL>',
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $new_payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
            'value' => '<EMAIL>',
            'category' => Config::CATEGORY_LIBRARY,
        ]);

    $this->assertDatabaseHas(resolve(Config::class)->getTable(), [
        'key' => $new_payload['key'],
        'value' => $new_payload['value'],
        'category' => $new_payload['category'],
    ]);
});

test('store : save non defined config fail', function () {
    //store new config
    $this->assertDatabaseCount('master_configs', 0);

    $payload = [
        'key' => 'NEW_KEY',
        'value' => 'NEW_VALUE',
        'category' => Config::CATEGORY_GENERAL,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toEqual([
            'key' => [
                'The selected key is invalid.'
            ]
        ]);

    $payload = [
        'key' => Config::CANTEEN_NOTIFICATION_EMAIL,
        'value' => '<EMAIL>',
        'category' => 'NEW_CATEGORY',
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toEqual([
            'category' => [
                'The selected category is invalid.'
            ]
        ]);

    $this->assertDatabaseCount('master_configs', 0);
});

test('store : saving config with json data type', function () {
    // store simple array
    $payload = [
        'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
        'value' => [
            'hello',
            'world'
        ],
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
            'value' => [
                'hello',
                'world'
            ],
            'category' => Config::CATEGORY_LIBRARY,
        ]);

    $this->assertDatabaseCount('master_configs', 1);

    $this->assertDatabaseHas(resolve(Config::class)->getTable(), [
        'key' => $payload['key'],
        'value' => json_encode($payload['value']),
        'category' => $payload['category'],
    ]);

    // store associative array
    $assoc_array_payload = [
        'key' => Config::WALLET_TRANSFERABLE_MODEL,
        'value' => [
            'SPM' => 'yes',
            'Koko' => 'no'
        ],
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $assoc_array_payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'key' => Config::WALLET_TRANSFERABLE_MODEL,
            'value' => [
                'SPM' => 'yes',
                'Koko' => 'no'
            ],
            'category' => Config::CATEGORY_LIBRARY,
        ]);

    $this->assertDatabaseCount('master_configs', 2);

    $this->assertDatabaseHas(resolve(Config::class)->getTable(), [
        'key' => $assoc_array_payload['key'],
        'value' => json_encode($assoc_array_payload['value']),
        'category' => $assoc_array_payload['category'],
    ]);
});

test('store : saving different Config::DATA_TYPE failed', function () {
    // store data type of json using string input => Config::ENROLLMENT_DOCUMENT_TYPE -> type -> json
    $payload = [
        'key' => Config::ENROLLMENT_DOCUMENT_TYPE,
        'value' => 'as a string',
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['value'][0])->toEqual(__('validation.json', ['attribute' => 'value']));

    // store data type of string using json array input => Config::CANTEEN_OPENING_DAY -> type -> string
    $payload = [
        'key' => Config::CANTEEN_OPENING_DAY,
        'value' => [
            'as a',
            'json'
        ],
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['value'][0])->toEqual(__('validation.string', ['attribute' => 'value']));

    // store data type of string using json aasoc array input => Config::CANTEEN_OPENING_DAY -> type -> string
    $payload = [
        'key' => Config::CANTEEN_OPENING_DAY,
        'value' => [
            'SPM' => 'yes',
            'Koko' => 'no'
        ],
        'category' => Config::CATEGORY_LIBRARY,
    ];

    $response = $this->postJson(route("admin.$this->routeNamePrefix.store"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['value'][0])->toEqual(__('validation.string', ['attribute' => 'value']));
});
