<?php

use App\Http\Resources\ScholarshipAwardResource;
use App\Http\Resources\SimpleEmployeeResource;
use App\Http\Resources\UserableResource;
use App\Interfaces\Userable;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\Enrollment;
use App\Models\GlAccount;
use App\Models\LegalEntity;
use App\Models\PaymentTerm;
use App\Models\Scholarship;
use App\Models\ScholarshipAward;
use App\Models\Student;
use App\Models\Tax;
use App\Models\UnpaidItem;
use App\Models\User;
use App\Services\Billing\DiscountSettingService;
use Carbon\Carbon;
use Database\Seeders\EmployeeSeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        PaymentMethodSeeder::class,
        EmployeeSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->employee = Employee::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->legalEntity = LegalEntity::factory()->create();
    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);

    $this->routeNamePrefix = 'admin.discounts';
});

test('create discount success', function () {
    /**
     * without gl_account_codes
     */
    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa Scholarship'
    ]);

    $scholarship_award_1 = ScholarshipAward::factory()->create([
        'student_id' => $student_1->id,
        'scholarship_id' => $scholarship->id
    ]);

    $scholarship_award_2 = ScholarshipAward::factory()->create([
        'student_id' => $student_2->id,
        'scholarship_id' => $scholarship->id
    ]);

    $payload = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award_1->id,
                'student_id' => $student_1->id,
                'description' => 'Test Description'
            ],
            [
                'basis' => 'FIXED',
                'basis_amount' => '50',
                'max_amount' => '500',
                'effective_from' => '2025-03-01',
                'effective_to' => '2025-04-01',
                'gl_account_codes' => [],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award_2->id,
                'student_id' => $student_2->id,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    // User Description takes priority
    $this->assertDatabaseHas('discount_settings', [
        'basis' => $payload['discounts'][0]['basis'],
        'basis_amount' => $payload['discounts'][0]['basis_amount'],
        'max_amount' => $payload['discounts'][0]['max_amount'],
        'effective_from' => $payload['discounts'][0]['effective_from'],
        'effective_to' => $payload['discounts'][0]['effective_to'],
        'source_type' => $payload['discounts'][0]['source_type'],
        'source_id' => $payload['discounts'][0]['source_id'],
        'userable_type' => get_class($student_1),
        'userable_id' => $student_1->id,
        'description' => $payload['discounts'][0]['description'],
        'is_active' => false,
        'created_by_employee_id' => $this->employee->id,
    ]);

    // If no description, discount source (scholarship) description is used instead
    $this->assertDatabaseHas('discount_settings', [
        'basis' => $payload['discounts'][1]['basis'],
        'basis_amount' => $payload['discounts'][1]['basis_amount'],
        'max_amount' => $payload['discounts'][1]['max_amount'],
        'effective_from' => $payload['discounts'][1]['effective_from'],
        'effective_to' => $payload['discounts'][1]['effective_to'],
        'source_type' => $payload['discounts'][1]['source_type'],
        'source_id' => $payload['discounts'][1]['source_id'],
        'userable_type' => get_class($student_2),
        'userable_id' => $student_2->id,
        'description' => $scholarship->description,
        'is_active' => false,
        'created_by_employee_id' => $this->employee->id,
    ]);

    /**
     * with gl_account_codes
     */

    $gl_account_1 = GlAccount::factory()->create([
        'code' => '123',
    ]);

    $gl_account_2 = GlAccount::factory()->create([
        'code' => '234',
    ]);

    $student_3 = Student::factory()->create();

    $scholarship_award_3 = ScholarshipAward::factory()->create([
        'student_id' => $student_3->id,
    ]);

    $payload_2 = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [
                    $gl_account_1->code,
                    $gl_account_2->code,
                ],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award_3->id,
                'student_id' => $student_3->id,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload_2)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas(DiscountSetting::class, [
        'basis' => $payload_2['discounts'][0]['basis'],
        'basis_amount' => $payload_2['discounts'][0]['basis_amount'],
        'max_amount' => $payload_2['discounts'][0]['max_amount'],
        'effective_from' => $payload_2['discounts'][0]['effective_from'],
        'effective_to' => $payload_2['discounts'][0]['effective_to'],
        'gl_account_codes->0' => $gl_account_1->code,
        'gl_account_codes->1' => $gl_account_2->code,
        'source_type' => $payload_2['discounts'][0]['source_type'],
        'source_id' => $payload_2['discounts'][0]['source_id'],
        'userable_type' => get_class($student_3),
        'userable_id' => $student_3->id,
        'is_active' => false,
        'created_by_employee_id' => $this->employee->id,
    ]);

    $student_4 = Student::factory()->create();
    $student_5 = Student::factory()->create();

    $student_5_scholarship_award = ScholarshipAward::factory()->create([
        'student_id' => $student_5->id,
    ]);

    // Create discount without source type and id
    $payload_3 = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [
                    $gl_account_1->code,
                    $gl_account_2->code,
                ],
                'source_type' => null,
                'source_id' => null,
                'student_id' => $student_4->id,
            ],
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [
                    $gl_account_1->code,
                    $gl_account_2->code,
                ],
                'source_type' => ScholarshipAward::class,
                'source_id' => $student_5_scholarship_award->id,
                'student_id' => $student_5->id,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload_3)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas(DiscountSetting::class, [
        'basis' => $payload_3['discounts'][0]['basis'],
        'basis_amount' => $payload_3['discounts'][0]['basis_amount'],
        'max_amount' => $payload_3['discounts'][0]['max_amount'],
        'effective_from' => $payload_3['discounts'][0]['effective_from'],
        'effective_to' => $payload_3['discounts'][0]['effective_to'],
        'gl_account_codes->0' => $gl_account_1->code,
        'gl_account_codes->1' => $gl_account_2->code,
        'source_type' => null,
        'source_id' => null,
        'userable_type' => get_class($student_4),
        'userable_id' => $student_4->id,
        'is_active' => false,
        'created_by_employee_id' => $this->employee->id,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'basis' => $payload_3['discounts'][1]['basis'],
        'basis_amount' => $payload_3['discounts'][1]['basis_amount'],
        'max_amount' => $payload_3['discounts'][1]['max_amount'],
        'effective_from' => $payload_3['discounts'][1]['effective_from'],
        'effective_to' => $payload_3['discounts'][1]['effective_to'],
        'gl_account_codes->0' => $gl_account_1->code,
        'gl_account_codes->1' => $gl_account_2->code,
        'source_type' => $payload_3['discounts'][1]['source_type'],
        'source_id' => $payload_3['discounts'][1]['source_id'],
        'userable_type' => get_class($student_5),
        'userable_id' => $student_5->id,
        'is_active' => false,
        'created_by_employee_id' => $this->employee->id,
    ]);
});

test('create discount failed because of validation', function () {
    /**
     * no payload
     */

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), []);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts' => [
                'The discounts field is required.'
            ],
        ],
        'data' => null,
    ]);


    /**
     * invalid gl_account_codes
     */

    $scholarship_award = ScholarshipAward::factory()->create();

    $gl_account = GlAccount::factory()->create([
        'code' => '123',
    ]);

    $payload = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [
                    '1234',
                    $gl_account->code,
                ],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award->id,
                'student_id' => $scholarship_award->student->id,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts.0.gl_account_codes' => [
                'The selected gl account code is invalid.'
            ],
        ],
        'data' => null,
    ]);

    /**
     * invalid source_id and student_id
     */

    $payload = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award->id,
                'student_id' => $scholarship_award->student->id,
            ],
            [
                'basis' => 'FIXED',
                'basis_amount' => '50',
                'max_amount' => '500',
                'effective_from' => '2025-03-01',
                'effective_to' => '2025-04-01',
                'gl_account_codes' => [],
                'source_type' => ScholarshipAward::class,
                'source_id' => 888,
                'student_id' => 99,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts.1.source_id' => [
                'The selected source id is invalid.'
            ],
            'discounts.1.student_id' => [
                'The selected student id is invalid.'
            ],
        ],
        'data' => null,
    ]);

    /**
     * invalid source_type
     */
    $payload = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [],
                'source_type' => 'Invalid Source Type',
                'source_id' => $scholarship_award->id,
                'student_id' => $scholarship_award->student->id,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts.0.source_type' => [
                'The selected source type is invalid.'
            ],
        ],
        'data' => null,
    ]);

    /**
     * multiple source_type in one request
     */

    $enrollment = Enrollment::factory()->create(['id' => 10000]);

    $payload = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award->id,
                'student_id' => $scholarship_award->student->id,
            ],
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [],
                'source_type' => Enrollment::class,
                'source_id' => $enrollment->id,
                'student_id' => 999,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts' => [
                'Only one type of source can be created at a time.'
            ],
            'discounts.1.source_type' => [
                'The selected source type is invalid.'
            ],
            'discounts.1.source_id' => [
                'The selected source id is invalid.'
            ],
            'discounts.1.student_id' => [
                'The selected student id is invalid.'
            ],
        ],
        'data' => null,
    ]);

    /**
     * duplicate source_id in one request
     */
    $payload = [
        'discounts' => [
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award->id,
                'student_id' => $scholarship_award->student->id,
            ],
            [
                'basis' => 'PERCENT',
                'basis_amount' => '10',
                'max_amount' => '100',
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-02-01',
                'gl_account_codes' => [],
                'source_type' => ScholarshipAward::class,
                'source_id' => $scholarship_award->id,
                'student_id' => $scholarship_award->student->id,
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.create'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts.0.source_id' => [
                'The source id field has a duplicate value.'
            ],
            'discounts.1.source_id' => [
                'The source id field has a duplicate value.'
            ],
            'discounts.0.student_id' => [
                'The student id field has a duplicate value.'
            ],
            'discounts.1.student_id' => [
                'The student id field has a duplicate value.'
            ],
        ],
        'data' => null,
    ]);
});

test('index', function () {
    DiscountSetting::factory(2)->studentUserable()->create();

    GlAccount::factory()->create([
        'code' => 'TESTING',
    ]);

    $scholarship_award = ScholarshipAward::factory()->create();
    $scholarship_award->load('scholarship');

    $discount = DiscountSetting::factory()->studentUserable()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-07-01',
        'gl_account_codes' => json_encode(['TESTING']),
        'source_type' => get_class($scholarship_award),
        'source_id' => $scholarship_award->id,
        'userable_id' => $scholarship_award->student->id,
        'userable_type' => get_class($scholarship_award->student),
        'description' => 'some description',
        'is_active' => true,
    ]);

    $filters = [
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-07-01',
        'gl_account_code' => 'TESTING',
        'source_type' => get_class($scholarship_award),
        'source_id' => $scholarship_award->id,
        'userable_id' => $scholarship_award->student->id,
        'userable_type' => Userable::USER_TYPE_MAPPING[get_class($scholarship_award->student)],
        'is_active' => true,
        'includes' => [
            'source.student',
            'userable',
        ],
    ];

    $this->mock(DiscountSettingService::class, function (MockInterface $mock) use ($filters, $discount) {
        $mock->shouldReceive('getAllPaginatedDiscountSettings')
            ->once()
            ->andReturn(new LengthAwarePaginator([
                $discount->loadMissing([
                    'source.student',
                    'source.scholarship',
                    'userable',
                ])
            ], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $discount->id,
            'basis' => $discount->basis,
            'basis_amount' => $discount->basis_amount,
            'max_amount' => $discount->max_amount,
            'used_amount' => $discount->used_amount,
            'effective_from' => $discount->effective_from,
            'effective_to' => $discount->effective_to,
            'gl_account_codes' => json_decode($discount->gl_account_codes, true),
            'description' => $discount->description,
            'is_active' => $discount->is_active,
            'created_at' => Carbon::parse($discount->created_at, 'UTC')->tz(config('school.timezone'))->toIso8601String(),
            'source' => resourceToArray(new ScholarshipAwardResource($scholarship_award)),
            'userable' => resourceToArray(new UserableResource($scholarship_award->student)),
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(DiscountSettingService::class, function (MockInterface $mock) {
        $discount = DiscountSetting::factory()->create();

        $mock->shouldReceive('getAllPaginatedDiscountSettings')
            ->once()
            ->andReturn(new LengthAwarePaginator([$discount], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index determine getAll per_page is -1', function () {
    $this->mock(DiscountSettingService::class, function (MockInterface $mock) {
        $discounts = DiscountSetting::factory(2)->create();

        $mock->shouldReceive('getAllDiscountSettings')->once()->andReturn($discounts);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('update discount success', function () {
    $discount_1 = DiscountSetting::factory()->studentUserable()->create([
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-07-01',
        'description' => 'old description',
        'is_active' => false,
    ]);

    $discount_2 = DiscountSetting::factory()->studentUserable()->create([
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-01',
        'description' => 'old description 2',
        'is_active' => false,
    ]);

    $payload = [
        'discounts' => [
            [
                'discount_id' => $discount_1->id,
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-07-01',
                'description' => 'new description'
            ],
            [
                'discount_id' => $discount_2->id,
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-12-01',
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeNamePrefix . '.update'), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas('discount_settings', [
        'id' => $discount_1->id,
        'effective_from' => $payload['discounts'][0]['effective_from'],
        'effective_to' => $payload['discounts'][0]['effective_to'],
        'description' => 'new description',
        'is_active' => false,
    ]);

    $this->assertDatabaseHas('discount_settings', [
        'id' => $discount_2->id,
        'effective_from' => $payload['discounts'][1]['effective_from'],
        'effective_to' => $payload['discounts'][1]['effective_to'],
        'description' => $discount_2->description,
        'is_active' => false,
    ]);
});

test('update discount failed because of validation', function () {
    /**
     * no payload
     */

    $response = $this->putJson(route($this->routeNamePrefix . '.update'), []);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts' => [
                'The discounts field is required.'
            ],
        ],
        'data' => null,
    ]);

    /**
     * incomlete payload
     */
    $discount = DiscountSetting::factory()->studentUserable()->create();

    $payload = [
        'discounts' => [
            [
                'discount_id' => $discount->id,
                // 'effective_from' => '2025-01-01',
                // 'effective_to' => '2025-07-01',
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeNamePrefix . '.update'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts.0.effective_from' => [
                'The effective from field is required.'
            ],
            'discounts.0.effective_to' => [
                'The effective to field is required.'
            ],
        ],
        'data' => null,
    ]);

    /**
     * invalid discount_id
     */
    $payload = [
        'discounts' => [
            [
                'discount_id' => $discount->id,
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-07-01',
            ],
            [
                'discount_id' => 999, // invalid discount_id
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-07-01',
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeNamePrefix . '.update'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts.1.discount_id' => [
                'The selected discount id is invalid.'
            ],
        ],
        'data' => null,
    ]);

    /**
     * duplicate discount_id
     */

    $payload = [
        'discounts' => [
            [
                'discount_id' => $discount->id,
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-07-01',
            ],
            [
                'discount_id' => $discount->id,
                'effective_from' => '2025-01-01',
                'effective_to' => '2025-07-01',
            ],
        ],
    ];

    $response = $this->putJson(route($this->routeNamePrefix . '.update'), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'discounts.0.discount_id' => [
                'The discount id field has a duplicate value.'
            ],
            'discounts.1.discount_id' => [
                'The discount id field has a duplicate value.'
            ],
        ],
        'data' => null,
    ]);
});

test('show', function () {
    $scholarship_award = ScholarshipAward::factory()->create();

    $discount = DiscountSetting::factory()->studentUserable()->create([
        'source_type' => get_class($scholarship_award),
        'source_id' => $scholarship_award->id,
        'userable_id' => $scholarship_award->student->id,
        'userable_type' => get_class($scholarship_award->student),
        'description' => 'some description'
    ]);

    $discount->load([
        'source' => function (MorphTo $morph_to) {
            $morph_to->morphWith([
                ScholarshipAward::class => ['scholarship'],
            ]);
        },
        'userable.latestPrimaryClassAndGrade',
        'createdByEmployee',
    ]);

    $response = $this->getJson(route($this->routeNamePrefix . '.show', ['discount_setting' => $discount->id]))->json();


    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $discount->id,
            'basis' => $discount->basis,
            'basis_amount' => $discount->basis_amount,
            'max_amount' => $discount->max_amount,
            'used_amount' => $discount->used_amount,
            'effective_from' => $discount->effective_from,
            'effective_to' => $discount->effective_to,
            'gl_account_codes' => json_decode($discount->gl_account_codes, true),
            'description' => $discount->description,
            'is_active' => $discount->is_active,
            'created_at' => Carbon::parse($discount->created_at, 'UTC')->tz(config('school.timezone'))->toIso8601String(),
            'source' => resourceToArray(new ScholarshipAwardResource($discount->source)),
            'userable' => resourceToArray(new UserableResource($discount->userable)),
            'created_by' => resourceToArray(new SimpleEmployeeResource($discount->createdByEmployee)),
        ]);
});

test('confirm', function () {
    $student = Student::factory()->create();

    $scholarship_award = ScholarshipAward::factory()->create([
        'student_id' => $student->id,
    ]);

    $discount = DiscountSetting::factory()->studentUserable()->create([
        'source_type' => get_class($scholarship_award),
        'source_id' => $scholarship_award->id,
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'is_active' => false,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'description' => 'some description'
    ]);

    /**
     * back apply paid invoices
     */

    Carbon::setTestNow('2024-10-30 04:30:00');

    // paid invoice, eligible for backapply discount
    $paid_invoice = BillingDocument::factory()->create([
        'legal_entity_id' => $this->legalEntity->id,
        'payment_term_id' => $this->paymentTerm->id,
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 400,
        'amount_after_tax' => 400,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => 'Lucas',
        'bill_to_address' => '123, Jalan ABC, 47000 Selangor.',
        'paid_at' => '2024-07-25 04:30:00',
        'tax_code' => $this->tax->code,
    ]);

    $unpaid_item = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => null,
        'amount_before_tax' => 400,
        'period' => '2024-01-01',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $line_item = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $paid_invoice,
        'amount_before_tax' => 400,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item->id,
        'billable_item_type' => get_class($unpaid_item),
        'description' => 'School Fees Jan 2024',
    ]);

    $this->assertDatabaseCount(BillingDocument::class, 1);

    $payload = [
        'discount_ids' => [
            $discount->id
        ],
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.confirm'), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount(BillingDocument::class, 2);

    // already tested in DiscountSettingServiceTest
    $this->assertDatabaseHas(BillingDocument::class, [ // original invoice
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'bill_to_type' => $paid_invoice->bill_to_type,
        'bill_to_id' => $paid_invoice->bill_to_id,
        'amount_before_tax' => 400,
        'amount_before_tax_after_less_advance' => 0,
        'amount_after_tax' => 400,
        'tax_amount' => 0,
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [ // advance invoice
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'document_date' => '2024-10-30',
        'posting_date' => null,
        'bill_to_type' => $paid_invoice->bill_to_type,
        'bill_to_id' => $paid_invoice->bill_to_id,
        'amount_before_tax' => 400,
        'amount_before_tax_after_less_advance' => 400,
        'amount_after_tax' => 400,
        'tax_amount' => 0,
    ]);


    /**
     *
     * multiple discount_ids
     *
     */


    /**
     * STUDENT A
     */

    $studentA = Student::factory()->create();

    $scholarship_awardA = ScholarshipAward::factory()->create([
        'student_id' => $studentA->id,
    ]);

    $scholarship_awardAA = ScholarshipAward::factory()->create([
        'student_id' => $studentA->id,
    ]);

    $discountA = DiscountSetting::factory()->studentUserable()->create([
        'source_type' => get_class($scholarship_awardA),
        'source_id' => $scholarship_awardA->id,
        'userable_id' => $studentA->id,
        'userable_type' => get_class($studentA),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'is_active' => false,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'basis_amount' => 50,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
    ]);

    $discountAA = DiscountSetting::factory()->studentUserable()->create([
        'source_type' => get_class($scholarship_awardAA),
        'source_id' => $scholarship_awardAA->id,
        'userable_id' => $studentA->id,
        'userable_type' => get_class($studentA),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'is_active' => false,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'basis_amount' => 124.50,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
    ]);

    // 2 PAID INVOICE STUDENT A, Each invoice paid is eligible for 2 discounts before the discount is confirmed
    $paid_invoice_studentA1 = BillingDocument::factory()->create([
        'legal_entity_id' => $this->legalEntity->id,
        'payment_term_id' => $this->paymentTerm->id,
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 200, // will create advance of 50 + 124.50 because this bill is eligible and there are 2 discounts
        'amount_after_tax' => 200,
        'bill_to_type' => $studentA->getBillToType(),
        'bill_to_id' => $studentA->getBillToId(),
        'paid_at' => '2024-07-25 04:30:00',
        'tax_code' => $this->tax->code,
    ]);

    $unpaid_item_A1 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => null,
        'amount_before_tax' => 200,
        'period' => '2024-01-01',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $paid_invoice_studentA1,
        'amount_before_tax' => 200,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item_A1->id,
        'billable_item_type' => get_class($unpaid_item_A1),
        'description' => 'School Fees Jan 2024',
    ]);

    $paid_invoice_studentA2 = BillingDocument::factory()->create([
        'legal_entity_id' => $this->legalEntity->id,
        'payment_term_id' => $this->paymentTerm->id,
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 200, // will create advance of 50 + 124.50 because this bill is eligible and there are 2 discounts
        'amount_after_tax' => 200,
        'bill_to_type' => $studentA->getBillToType(),
        'bill_to_id' => $studentA->getBillToId(),
        'paid_at' => '2024-07-25 04:30:00',
        'tax_code' => $this->tax->code,
    ]);

    $unpaid_item_A2 = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => null,
        'amount_before_tax' => 200,
        'period' => '2024-01-01',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $paid_invoice_studentA2,
        'amount_before_tax' => 200,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item_A2->id,
        'billable_item_type' => get_class($unpaid_item_A2),
        'description' => 'School Fees Feb 2024',
    ]);


    /**
     * STUDENT B
     */

    // DISCOUNT FOR STUDENT B
    $studentB = Student::factory()->create();

    $scholarship_awardB = ScholarshipAward::factory()->create([
        'student_id' => $studentB->id,
    ]);

    $discountB = DiscountSetting::factory()->studentUserable()->create([
        'source_type' => get_class($scholarship_awardB),
        'source_id' => $scholarship_awardB->id,
        'userable_id' => $studentB->id,
        'userable_type' => get_class($studentB),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'is_active' => false,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'basis_amount' => 65.50,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
    ]);

    // PAID INVOICE STUDENT B
    $paid_invoice_studentB = BillingDocument::factory()->create([
        'legal_entity_id' => $this->legalEntity->id,
        'payment_term_id' => $this->paymentTerm->id,
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 175, // will create advance of 65.50 because this bill is eligible and there is 1 discount
        'amount_after_tax' => 175,
        'bill_to_type' => $studentB->getBillToType(),
        'bill_to_id' => $studentB->getBillToId(),
        'paid_at' => '2024-07-25 04:30:00',
        'tax_code' => $this->tax->code,
    ]);

    $unpaid_item_B = UnpaidItem::factory()->create([
        'status' => UnpaidItem::STATUS_PAID,
        'billing_document_id' => null,
        'amount_before_tax' => 175,
        'period' => '2024-01-01',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $paid_invoice_studentB,
        'amount_before_tax' => 175,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'currency_code' => 'MYR',
        'billable_item_id' => $unpaid_item_B->id,
        'billable_item_type' => get_class($unpaid_item_B),
        'description' => 'School Fees Jan 2024',
    ]);

    $payload = [
        'discount_ids' => [
            $discountA->id,
            $discountAA->id,
            $discountB->id,
        ],
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.confirm'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discountA->id,
        'is_active' => true,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discountAA->id,
        'is_active' => true,
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discountB->id,
        'is_active' => true,
    ]);

    // STUDENT A - pay 2 UNPAID_ITEM, each UNPAID_ITEM is eligible for the 2 discount, which will then receive 4 ADVANCE INVOICES
    $advance_invoices_studentA = BillingDocument::with('lineItems')->where('type', BillingDocument::TYPE_ADVANCE_INVOICE)
        ->where('bill_to_type', $studentA->getBillToType())
        ->where('bill_to_id', $studentA->getBillToId())
        ->get();

    expect($advance_invoices_studentA->toArray())->toHaveCount(4)

        // Discount A
        ->toHaveKey('0.line_items.0.description', "Discounted amount as advance payment from {$paid_invoice_studentA1->reference_no} - School Fees Jan 2024")
        ->toHaveKey('0.line_items.0.amount_before_tax', 50)

        // Discount A
        ->toHaveKey('1.line_items.0.description', "Discounted amount as advance payment from {$paid_invoice_studentA2->reference_no} - School Fees Feb 2024")
        ->toHaveKey('1.line_items.0.amount_before_tax', 50)

        // Discount AA
        ->toHaveKey('2.line_items.0.description', "Discounted amount as advance payment from {$paid_invoice_studentA1->reference_no} - School Fees Jan 2024")
        ->toHaveKey('2.line_items.0.amount_before_tax', 124.50)

        // Discount AA
        ->toHaveKey('3.line_items.0.description', "Discounted amount as advance payment from {$paid_invoice_studentA2->reference_no} - School Fees Feb 2024")
        ->toHaveKey('3.line_items.0.amount_before_tax', 124.50);


    // STUDENT B - pay 1 UNPAID_ITEM, each UNPAID_ITEM is eligible for the 1 discount, which will then receive 1 ADVANCE INVOICE
    $advance_invoices_studentB = BillingDocument::with('lineItems')->where('type', BillingDocument::TYPE_ADVANCE_INVOICE)
        ->where('bill_to_type', $studentB->getBillToType())
        ->where('bill_to_id', $studentB->getBillToId())
        ->get();

    expect($advance_invoices_studentB->toArray())->toHaveCount(1)
        ->toHaveKey('0.line_items.0.description', "Discounted amount as advance payment from {$paid_invoice_studentB->reference_no} - School Fees Jan 2024")
        ->toHaveKey('0.line_items.0.amount_before_tax', 65.50);
});
