<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Enums\Gender;
use App\Enums\MeritDemeritType;
use App\Enums\StudentAdmissionType;
use App\Enums\SubjectType;
use App\Exports\GenericExcelExportViaView;
use App\Enums\RewardPunishmentRecordStatus;
use App\Factories\ExportAdapterFactory;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\ClassSubjectTeacher;
use App\Models\Employee;
use App\Models\Exam;
use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\MeritDemeritSetting;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceExam;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SchoolProfile;
use App\Models\RewardPunishmentRecord;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\StudentReportCard;
use App\Models\Subject;
use App\Models\User;
use App\Repositories\ExamRepository;
use App\Services\DocumentPrintService;
use App\Services\Exam\ExamResultsPostingService;
use App\Services\Exam\StudentGradingFrameworkService;
use App\Repositories\StudentRepository;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    Cache::clear();
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');
    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.academy.';

    SnappyPdf::fake();

    $this->student2 = Student::factory()->create(['name' => 'Student2', 'student_number' => 'AAAA']);
    $this->student = Student::factory()->create(['name' => 'Student1', 'student_number' => 'BBBB']);
    $this->student3 = Student::factory()->create(['name' => 'Student3', 'student_number' => 'CCCC']);
    $this->student4 = Student::factory()->create(['name' => 'Student4', 'student_number' => 'DDDD']);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'code' => 'SEM2',
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $this->junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $this->senior_grade = Grade::factory()->create([
        'name->en' => 'Senior'
    ]);

    $this->junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年11班',
        'grade_id' => $this->junior_grade->id,
    ]);
    $this->junior_second_class = ClassModel::factory()->create([
        'name->en' => 'J112',
        'name->zh' => '一年12班',
        'grade_id' => $this->junior_grade->id,
    ]);
    $this->senior_first_class = ClassModel::factory()->create([
        'name->en' => 'S111',
        'name->zh' => '高一11班',
        'grade_id' => $this->senior_grade->id,
    ]);

    $this->semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
        'is_current_semester' => true,
        'code' => 'SEM2', // same as grading_framework code
        'semester_year_setting_id' => SemesterYearSetting::create(['year' => 2024])->id,
    ]);

    $this->employee_junior_first_class = Employee::factory()->create([
        'name->en' => 'junior first class teacher',
        'name->zh' => 'junior first class teacher ZH',
    ]);
    $this->employee_junior_second_class = Employee::factory()->create([
        'name->en' => 'junior second class teacher',
        'name->zh' => 'junior second class teacher ZH',
    ]);
    $this->employee_senior_first_class = Employee::factory()->create([
        'name->en' => 'senior first class teacher',
        'name->zh' => 'senior first class teacher ZH',
    ]);
    $this->first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'class_id' => $this->junior_first_class->id,
        'is_active' => true,
        'homeroom_teacher_id' => $this->employee_junior_first_class->id,
    ]);
    $this->second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'class_id' => $this->junior_second_class->id,
        'is_active' => true,
        'homeroom_teacher_id' => $this->employee_junior_second_class->id,
    ]);
    $this->third_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->semester_setting->id,
        'class_id' => $this->senior_first_class->id,
        'is_active' => true,
        'homeroom_teacher_id' => $this->employee_senior_first_class->id,
    ]);

    // student 1, 2 - junior first class
    // student 3 - junior second class
    // student 4 - senior first class
    $this->student2_class = StudentClass::factory()->create([
        'student_id' => $this->student2->id,
        'semester_class_id' => $this->first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $this->semester_setting->id
    ]);
    $this->student_class = StudentClass::factory()->create([
        'student_id' => $this->student->id,
        'semester_class_id' => $this->first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $this->semester_setting->id
    ]);
    $this->student3_class = StudentClass::factory()->create([
        'student_id' => $this->student3->id,
        'semester_class_id' => $this->second_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $this->semester_setting->id
    ]);
    $this->student4_class = StudentClass::factory()->create([
        'student_id' => $this->student4->id,
        'semester_class_id' => $this->third_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $this->semester_setting->id
    ]);

    $settings = [
        $this->first_semester_class->id => [$this->student, $this->student2],
        $this->second_semester_class->id => [$this->student3],
        $this->third_semester_class->id => [$this->student4],
    ];

    $this->exam_sem1 = Exam::factory()->create([
        'code' => 'SEM1EXAM',
        'results_entry_period_from' => '2024-11-20 16:00:00',
        'results_entry_period_to' => '2024-11-30 15:59:59'
    ]);
    $this->exam_sem2 = Exam::factory()->create([
        'code' => 'SEM2EXAM',
        'results_entry_period_from' => '2024-11-01 16:00:00',
        'results_entry_period_to' => '2024-11-21 15:59:59'
    ]);
    $this->exam_final = Exam::factory()->create([
        'code' => 'FINALEXAM',
        'results_entry_period_from' => '2024-12-01 16:00:00',
        'results_entry_period_to' => '2024-12-30 15:59:59'
    ]);

    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01', 'name->en' => 'BM', 'name->zh' => '马来西亚文'],
        ['code' => '02', 'name->en' => 'English', 'name->zh' => '英文'],
        ['code' => '03', 'name->en' => 'Math', 'name->zh' => '数学'],
        ['code' => '04', 'name->en' => 'History', 'name->zh' => '历史'],
        ['code' => '70', 'name->en' => 'Geography', 'name->zh' => '地理'],
    ))->create();

    $gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);

    foreach ($settings as $semester_class_id => $student_list) {
        foreach ($this->subjects as $subject) {
            $class_subject = ClassSubject::factory()->create([
                'semester_class_id' => $semester_class_id,
                'subject_id' => $subject->id,
            ]);
            ClassSubjectTeacher::factory()->create([
                'class_subject_id' => $class_subject->id
            ]);
            foreach ($student_list as $s) {
                ClassSubjectStudent::factory()->create([
                    'class_subject_id' => $class_subject->id,
                    'student_id' => $s->id,
                ]);
            }
        }
    }
    Carbon::setTestNow('2024-11-21');

    // setup student's grading framework
    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student3)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($this->student4)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();

    StudentGradingFramework::whereIn('id', [$sgf1->id, $sgf2->id, $sgf3->id, $sgf4->id])->update(['academic_year' => $this->semester_setting->semesterYearSetting->year]);

    ResultSourceSubject::truncate();
    ResultSourceSubjectComponent::truncate();

    $result_source_student1 = $sgf1->resultSources->where('code', $this->exam_sem2->code)->first();
    $result_source_student2 = $sgf2->resultSources->where('code', $this->exam_sem2->code)->first();
    $result_source_student3 = $sgf3->resultSources->where('code', $this->exam_sem2->code)->first();
    $result_source_student4 = $sgf4->resultSources->where('code', $this->exam_sem2->code)->first();

    // student 1 (BM - 0, English - null, Math - 13 (11+2), History - null, Geography - 14)
    // student 2 (BM - 21, English - 22, Math - 23, History - 24, Geography - 25)
    // student 3 (BM - 31, English - 32, Math - 33, History - 34, Geography - 35)
    // student 4 (BM - 41, English - 42, Math - 43, History - 44, Geography - 45)

    // student 1
    $this->student_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[0]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 0,
    ]);
    $this->student_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[1]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => null,
    ]);
    $this->student_result_source_subject_math = ResultSourceSubject::factory()->create([
        'subject_id' => $this->subjects[2]->id,
        'result_source_id' => $result_source_student1->id,
    ]);
    ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => $this->student_result_source_subject_math->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 11,
    ]);
    ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => $this->student_result_source_subject_math->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 2,
    ]);
    $this->student_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[4]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 14,
    ]);

    // student 2
    $this->student2_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[0]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 21,
    ]);
    $this->student2_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[1]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 22,
    ]);
    $this->student2_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[2]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 23,
    ]);
    $this->student2_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[3]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 24,
    ]);
    $this->student2_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[4]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 25,
    ]);

    // student 3
    $this->student3_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[0]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 31,
    ]);
    $this->student3_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[1]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 32,
    ]);
    $this->student3_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[2]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 33,
    ]);
    $this->student3_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[3]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 34,
    ]);
    $this->student3_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[4]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 35,
    ]);

    // student 4
    $this->student4_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[0]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 41,
    ]);
    $this->student4_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[1]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 42,
    ]);
    $this->student4_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[2]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 43,
    ]);
    $this->student4_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[3]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 44,
    ]);
    $this->student4_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $this->subjects[4]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 45,
    ]);

    $this->school_profile = SchoolProfile::factory()->create([
        'principal_name->en' => 'principal name',
        'principal_name->zh' => 'principal name ZH',
    ]);

    $this->examRepository = app(ExamRepository::class);
    $this->examResultsPostingService = app(ExamResultsPostingService::class);
    $this->studentRepository = app(StudentRepository::class);
    $this->reportPrintService = app(ReportPrintService::class);
});

test('transferredStudentListByAdmissionYear return data', function () {
    // create transferred student with multiple classes
    $semester_setting = SemesterSetting::factory()->create();
    $primary_class_junior = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior->id
    ]);
    $semester_setting2 = SemesterSetting::factory()->create();
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    $student = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student A',
        'name->zh' => '学生 A',
        'student_number' => '0001',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_junior_1->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-01-05',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class_junior_2->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-08-05',
    ]);

    // create transferred student without class
    $student3 = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student D',
        'name->zh' => '学生 D',
        'student_number' => '0004',
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'report_language' => 'zh',
        'admission_year' => "2024",
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'transferred-student-list', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            [
                "student_name" => [
                    "en" => "student A",
                    "zh" => "学生 A",
                ],
                "student_number" => "0001",
                "class_name" => "J11 - 一年1班",
            ],
            [
                "student_name" => [
                    "en" => "student D",
                    "zh" => "学生 D",
                ],
                "student_number" => "0004",
                "class_name" => "-",
            ],
        ]);
});

test('transferredStudentListByAdmissionYear return pdf', function () {
    // create transferred student with multiple classes
    $semester_setting = SemesterSetting::factory()->create();
    $primary_class_junior = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior->id
    ]);
    $semester_setting2 = SemesterSetting::factory()->create();
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    $student = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student A',
        'name->zh' => '学生 A',
        'student_number' => '0001',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_junior_1->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-01-05',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class_junior_2->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-08-05',
    ]);

    // create transferred student without class
    $student3 = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student D',
        'name->zh' => '学生 D',
        'student_number' => '0004',
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'report_language' => 'en',
        'admission_year' => "2024",
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'academy-transferred-student-list';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'transferred-student-list', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('studentAnalysisReportBySemesterGroupByGrade return data', function () {
    Grade::truncate();
    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(4)->create(new Sequence(
        [
            'name->en' => 'Junior 1',
            'name->zh' => '初一',
            'sequence' => 3,
        ],
        [
            'name->en' => 'Junior 2',
            'name->zh' => '初二',
            'sequence' => 2,
        ],
        [
            'name->en' => 'Junior 3',
            'name->zh' => '初三',
            'sequence' => 1,
        ],
        // Grade without student
        [
            'name->en' => 'Senior 1',
            'name->zh' => '高一',
            'sequence' => 0,
        ],
    ));
    $primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J12',
        'name->zh' => '一年2班',
    ]);
    $second_primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    // class without student
    $second_primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J22',
        'name->zh' => '二年2班',
    ]);
    $primary_class_junior_3 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[2]->id,
        'name->en' => 'J31',
        'name->zh' => '三年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_1->id
    ]);
    $second_semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_1->id
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    // class without student
    $second_semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_2->id
    ]);
    $semester_class_junior_3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_3->id
    ]);

    $data_to_be_created = [
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_1,
            'male_students_hostel' => 5,
            'male_students_non_hostel' => 10,
            'female_students_hostel' => 4,
            'female_students_non_hostel' => 12,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $second_semester_class_junior_1,
            'male_students_hostel' => 6,
            'male_students_non_hostel' => 9,
            'female_students_hostel' => 7,
            'female_students_non_hostel' => 10,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_2,
            'male_students_hostel' => 0,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 2,
            'female_students_non_hostel' => 0,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_3,
            'male_students_hostel' => 5,
            'male_students_non_hostel' => 0,
            'female_students_hostel' => 0,
            'female_students_non_hostel' => 10,
        ],
    ];

    foreach ($data_to_be_created as $data) {
        for ($i = 1; $i <= $data['male_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['male_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $semester_setting->id,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'student-analysis', $filters)
    )->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            "summary" => [
                "total_students" => 81,
                "total_male_students" => 36,
                "total_female_students" => 45,
                "total_hostel_students" => 29,
                "total_non_hostel_students" => 52,
            ],
            "summary_by_grade" => [
                $grades[0]->id => [
                    "grade_name" => "Junior 1",
                    "total_students" => 63,
                    "total_male_students" => 30,
                    "total_female_students" => 33,
                    "total_hostel_students" => 22,
                    "total_non_hostel_students" => 41,
                    "classes_data" => [
                        [
                            "class_name" => "J11",
                            "total_students" => 32,
                            "total_male_students" => 15,
                            "total_female_students" => 17,
                            "total_hostel_students" => 13,
                            "total_non_hostel_students" => 19,
                        ],
                        [
                            "class_name" => "J12",
                            "total_students" => 31,
                            "total_male_students" => 15,
                            "total_female_students" => 16,
                            "total_hostel_students" => 9,
                            "total_non_hostel_students" => 22,
                        ]
                    ],
                ],
                $grades[1]->id => [
                    "grade_name" => "Junior 2",
                    "total_students" => 3,
                    "total_male_students" => 1,
                    "total_female_students" => 2,
                    "total_hostel_students" => 2,
                    "total_non_hostel_students" => 1,
                    "classes_data" => [
                        [
                            "class_name" => "J21",
                            "total_students" => 3,
                            "total_male_students" => 1,
                            "total_female_students" => 2,
                            "total_hostel_students" => 2,
                            "total_non_hostel_students" => 1,
                        ],
                        [
                            "class_name" => "J22",
                            "total_students" => 0,
                            "total_male_students" => 0,
                            "total_female_students" => 0,
                            "total_hostel_students" => 0,
                            "total_non_hostel_students" => 0,
                        ]
                    ],
                ],
                $grades[2]->id => [
                    "grade_name" => "Junior 3",
                    "total_students" => 15,
                    "total_male_students" => 5,
                    "total_female_students" => 10,
                    "total_hostel_students" => 5,
                    "total_non_hostel_students" => 10,
                    "classes_data" => [
                        [
                            "class_name" => "J31",
                            "total_students" => 15,
                            "total_male_students" => 5,
                            "total_female_students" => 10,
                            "total_hostel_students" => 5,
                            "total_non_hostel_students" => 10,
                        ],
                    ],
                ],
                $grades[3]->id => [
                    "grade_name" => "Senior 1",
                    "total_students" => 0,
                    "total_male_students" => 0,
                    "total_female_students" => 0,
                    "total_hostel_students" => 0,
                    "total_non_hostel_students" => 0,
                    "classes_data" => [],
                ],
            ],
        ]);
});

test('studentAnalysisReportBySemesterGroupByGrade return pdf', function () {
    Grade::truncate();
    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(4)->create(new Sequence(
        [
            'name->en' => 'Junior 1',
            'name->zh' => '初一',
            'sequence' => 0,
        ],
        [
            'name->en' => 'Junior 2',
            'name->zh' => '初二',
            'sequence' => 1,
        ],
        [
            'name->en' => 'Junior 3',
            'name->zh' => '初三',
            'sequence' => 2,
        ],
        // Grade without student
        [
            'name->en' => 'Senior 1',
            'name->zh' => '高一',
            'sequence' => 3,
        ],
    ));
    $primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J12',
        'name->zh' => '一年2班',
    ]);
    $second_primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    // class without student
    $second_primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J22',
        'name->zh' => '二年2班',
    ]);
    $primary_class_junior_3 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[2]->id,
        'name->en' => 'J31',
        'name->zh' => '三年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_1->id
    ]);
    $second_semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_1->id
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    // class without student
    $second_semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_2->id
    ]);
    $semester_class_junior_3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_3->id
    ]);

    $data_to_be_created = [
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_1,
            'male_students_hostel' => 1,
            'male_students_non_hostel' => 2,
            'female_students_hostel' => 2,
            'female_students_non_hostel' => 1,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $second_semester_class_junior_1,
            'male_students_hostel' => 2,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 1,
            'female_students_non_hostel' => 1,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_2,
            'male_students_hostel' => 0,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 1,
            'female_students_non_hostel' => 0,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_3,
            'male_students_hostel' => 2,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 0,
            'female_students_non_hostel' => 2,
        ],
    ];

    foreach ($data_to_be_created as $data) {
        for ($i = 1; $i <= $data['male_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['male_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable();

    $filters = [
        'report_language' => 'en',
        'semester_setting_id' => $semester_setting->id,
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'academy-student-analysis-report-by-semester';
    $extension = '.pdf';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'student-analysis', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('examinationResultByExamReport validation error', function () {
    $payload = [
        'report_language' => 'en',
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'semester_setting_id' => [
                    'The semester setting id field is required.'
                ],
                'exam_id' => [
                    'The exam id field is required.'
                ],
                'filter_by' => [
                    'The filter by field is required.'
                ],
            ],
            'data' => null
        ]);

    // filter by GRADE but null grade_ids
    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'grade_ids' => [
                    'The grade ids field is required.'
                ],
            ],
            'data' => null
        ]);

    // filter by SEMESTER_CLASS but null semester_class_ids
    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'SEMESTER_CLASS',
        'semester_class_ids' => [],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'semester_class_ids' => [
                    'The semester class ids field is required.'
                ],
            ],
            'data' => null
        ]);
});

test('examinationResultByExamReport return data - filter by grade', function () {
    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [$this->junior_grade->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'data_by_classes' => [
                [
                    'class_name' => 'J111',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student2_class->seat_no, // 1
                            'student_no' => $this->student2->student_number,
                            'student_name' => $this->student2->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_geography->resultSourceSubject),
                        ],
                        [
                            'seat_no' => $this->student_class->seat_no, // 2
                            'student_no' => $this->student->student_number,
                            'student_name' => $this->student->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_result_source_subject_math),
                            'subject_' . $this->subjects[3]->id => null,
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
                [
                    'class_name' => 'J112',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student3_class->seat_no, // 1
                            'student_no' => $this->student3->student_number,
                            'student_name' => $this->student3->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
            ]
        ]);

    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [$this->junior_grade->id, $this->senior_grade->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'data_by_classes' => [
                [
                    'class_name' => 'J111',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student2_class->seat_no, // 1
                            'student_no' => $this->student2->student_number,
                            'student_name' => $this->student2->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_geography->resultSourceSubject),
                        ],
                        [
                            'seat_no' => $this->student_class->seat_no, // 2
                            'student_no' => $this->student->student_number,
                            'student_name' => $this->student->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_result_source_subject_math),
                            'subject_' . $this->subjects[3]->id => null,
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
                [
                    'class_name' => 'J112',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student3_class->seat_no, // 1
                            'student_no' => $this->student3->student_number,
                            'student_name' => $this->student3->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student3_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
                [
                    'class_name' => 'S111',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student4_class->seat_no, // 1
                            'student_no' => $this->student4->student_number,
                            'student_name' => $this->student4->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
            ],
        ]);
});

test('examinationResultByExamReport return data - filter by semester class', function () {
    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'SEMESTER_CLASS',
        'semester_class_ids' => [$this->first_semester_class->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'data_by_classes' => [
                [
                    'class_name' => 'J111',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student2_class->seat_no, // 1
                            'student_no' => $this->student2->student_number,
                            'student_name' => $this->student2->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_geography->resultSourceSubject),
                        ],
                        [
                            'seat_no' => $this->student_class->seat_no, // 2
                            'student_no' => $this->student->student_number,
                            'student_name' => $this->student->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_result_source_subject_math),
                            'subject_' . $this->subjects[3]->id => null,
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
            ]
        ]);

    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'SEMESTER_CLASS',
        'semester_class_ids' => [$this->first_semester_class->id, $this->third_semester_class->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'data_by_classes' => [
                [
                    'class_name' => 'J111',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student2_class->seat_no, // 1
                            'student_no' => $this->student2->student_number,
                            'student_name' => $this->student2->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student2_subject_component_geography->resultSourceSubject),
                        ],
                        [
                            'seat_no' => $this->student_class->seat_no, // 2
                            'student_no' => $this->student->student_number,
                            'student_name' => $this->student->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_result_source_subject_math),
                            'subject_' . $this->subjects[3]->id => null,
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
                [
                    'class_name' => 'S111',
                    'header' => [
                        'seat_no' => 'Seat No.',
                        'student_no' => 'Student No.',
                        'student_name' => 'Student Name',
                        'subject_' . $this->subjects[0]->id => 'BM',
                        'subject_' . $this->subjects[1]->id => 'English',
                        'subject_' . $this->subjects[2]->id => 'Math',
                        'subject_' . $this->subjects[3]->id => 'History',
                        'subject_' . $this->subjects[4]->id => 'Geography',
                    ],
                    'data' => [
                        [
                            'seat_no' => $this->student4_class->seat_no, // 1
                            'student_no' => $this->student4->student_number,
                            'student_name' => $this->student4->getFormattedTranslations('name'),
                            'subject_' . $this->subjects[0]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_bm->resultSourceSubject),
                            'subject_' . $this->subjects[1]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_english->resultSourceSubject),
                            'subject_' . $this->subjects[2]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_math->resultSourceSubject),
                            'subject_' . $this->subjects[3]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_history->resultSourceSubject),
                            'subject_' . $this->subjects[4]->id => $this->examResultsPostingService->calculateResultSourceSubjectActualScore($this->student4_subject_component_geography->resultSourceSubject),
                        ],
                    ]
                ],
            ],
        ]);
});

test('examinationResultByExamReport return pdf - en', function () {
    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [$this->junior_grade->id],
        'export_type' => ExportType::PDF->value,
    ];

    $filename = 'examination-result-by-exam';
    $extension = '.pdf';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        'Seat No.',
        'Student No.',
        'Student Name',
        'BM',
        'English',
        'Math',
        'History',
        'Geography',
    ];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee('Class: J111');
    SnappyPdf::assertSee($this->student->student_number);
    SnappyPdf::assertSee($this->student->getFormattedTranslations('name'));
    SnappyPdf::assertSee('0');
    SnappyPdf::assertSee('-');
    SnappyPdf::assertSee('13');
    SnappyPdf::assertSee('14');

    SnappyPdf::assertSee($this->student2->student_number);
    SnappyPdf::assertSee($this->student2->getFormattedTranslations('name'));
    SnappyPdf::assertSee('21');
    SnappyPdf::assertSee('22');
    SnappyPdf::assertSee('23');
    SnappyPdf::assertSee('24');
    SnappyPdf::assertSee('25');

    SnappyPdf::assertSee('Class: J112');
    SnappyPdf::assertSee($this->student3->student_number);
    SnappyPdf::assertSee($this->student3->getFormattedTranslations('name'));
    SnappyPdf::assertSee('31');
    SnappyPdf::assertSee('32');
    SnappyPdf::assertSee('33');
    SnappyPdf::assertSee('34');
    SnappyPdf::assertSee('35');

    SnappyPdf::assertDontSee('Class: S111');
    SnappyPdf::assertDontSee($this->student4->student_number);
    SnappyPdf::assertDontSee($this->student4->getFormattedTranslations('name'));
    SnappyPdf::assertDontSee('41');
    SnappyPdf::assertDontSee('42');
    SnappyPdf::assertDontSee('43');
    SnappyPdf::assertDontSee('44');
    SnappyPdf::assertDontSee('45');
});

test('examinationResultByExamReport return pdf - zh', function () {
    $payload = [
        'report_language' => 'zh',
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [$this->junior_grade->id],
        'export_type' => ExportType::PDF->value,
    ];

    $filename = 'examination-result-by-exam';
    $extension = '.pdf';
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-exam-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    SnappyPdf::assertSee('班级: 一年11班');
    SnappyPdf::assertSee($this->student->student_number);
    SnappyPdf::assertSee($this->student->getFormattedTranslations('name'));
    SnappyPdf::assertSee('0');
    SnappyPdf::assertSee('-');
    SnappyPdf::assertSee('13');
    SnappyPdf::assertSee('14');

    SnappyPdf::assertSee($this->student2->student_number);
    SnappyPdf::assertSee($this->student2->getFormattedTranslations('name'));
    SnappyPdf::assertSee('21');
    SnappyPdf::assertSee('22');
    SnappyPdf::assertSee('23');
    SnappyPdf::assertSee('24');
    SnappyPdf::assertSee('25');

    SnappyPdf::assertSee('班级: 一年12班');
    SnappyPdf::assertSee($this->student3->student_number);
    SnappyPdf::assertSee($this->student3->getFormattedTranslations('name'));
    SnappyPdf::assertSee('31');
    SnappyPdf::assertSee('32');
    SnappyPdf::assertSee('33');
    SnappyPdf::assertSee('34');
    SnappyPdf::assertSee('35');

    SnappyPdf::assertDontSee('班级: 高一11班');
    SnappyPdf::assertDontSee($this->student4->student_number);
    SnappyPdf::assertDontSee($this->student4->getFormattedTranslations('name'));
    SnappyPdf::assertDontSee('41');
    SnappyPdf::assertDontSee('42');
    SnappyPdf::assertDontSee('43');
    SnappyPdf::assertDontSee('44');
    SnappyPdf::assertDontSee('45');
});

test('examinationResultByStudentReport validation error', function () {
    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", []))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'semester_setting_id' => [
                    'The semester setting id field is required.'
                ],
                'exam_id' => [
                    'The exam id field is required.'
                ],
                'filter_by' => [
                    'The filter by field is required.'
                ],
            ],
            'data' => null
        ]);

    // filter by GRADE but null grade_ids
    $payload = [
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'grade_ids' => [
                    'The grade ids field is required.'
                ],
            ],
            'data' => null
        ]);

    // filter by SEMESTER_CLASS but null semester_class_ids
    $payload = [
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'SEMESTER_CLASS',
        'semester_class_ids' => [],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'semester_class_ids' => [
                    'The semester class ids field is required.'
                ],
            ],
            'data' => null
        ]);
});

test('examinationResultByStudentReport return data - filter by grade', function () {
    $payload = [
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [$this->junior_grade->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student2->student_number,
                'student_name_zh' => $this->student2->getTranslation('name', 'zh'),
                'student_name_en' => $this->student2->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '21',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '22',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '23',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '24',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '25',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 2,
                'student_no' => $this->student->student_number,
                'student_name_zh' => $this->student->getTranslation('name', 'zh'),
                'student_name_en' => $this->student->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '0',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => null,
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '13',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '14',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student3->student_number,
                'student_name_zh' => $this->student3->getTranslation('name', 'zh'),
                'student_name_en' => $this->student3->getTranslation('name', 'en'),
                'class_name' => $this->junior_second_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_second_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '31',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '32',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '33',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '34',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '35',
                    ],
                ],
            ],
        ]);

    $payload = [
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [$this->junior_grade->id, $this->senior_grade->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student2->student_number,
                'student_name_zh' => $this->student2->getTranslation('name', 'zh'),
                'student_name_en' => $this->student2->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '21',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '22',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '23',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '24',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '25',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 2,
                'student_no' => $this->student->student_number,
                'student_name_zh' => $this->student->getTranslation('name', 'zh'),
                'student_name_en' => $this->student->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '0',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => null,
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '13',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '14',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student3->student_number,
                'student_name_zh' => $this->student3->getTranslation('name', 'zh'),
                'student_name_en' => $this->student3->getTranslation('name', 'en'),
                'class_name' => $this->junior_second_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_second_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '31',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '32',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '33',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '34',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '35',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student4->student_number,
                'student_name_zh' => $this->student4->getTranslation('name', 'zh'),
                'student_name_en' => $this->student4->getTranslation('name', 'en'),
                'class_name' => $this->senior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_senior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '41',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '42',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '43',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '44',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '45',
                    ],
                ],
            ],
        ]);
});

test('examinationResultByStudentReport return data - filter by semester class', function () {
    $payload = [
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'SEMESTER_CLASS',
        'semester_class_ids' => [$this->first_semester_class->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student2->student_number,
                'student_name_zh' => $this->student2->getTranslation('name', 'zh'),
                'student_name_en' => $this->student2->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '21',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '22',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '23',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '24',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '25',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 2,
                'student_no' => $this->student->student_number,
                'student_name_zh' => $this->student->getTranslation('name', 'zh'),
                'student_name_en' => $this->student->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '0',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => null,
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '13',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '14',
                    ],
                ],
            ],
        ]);

    $payload = [
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'SEMESTER_CLASS',
        'semester_class_ids' => [$this->first_semester_class->id, $this->third_semester_class->id],
    ];

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student2->student_number,
                'student_name_zh' => $this->student2->getTranslation('name', 'zh'),
                'student_name_en' => $this->student2->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '21',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '22',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '23',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '24',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '25',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 2,
                'student_no' => $this->student->student_number,
                'student_name_zh' => $this->student->getTranslation('name', 'zh'),
                'student_name_en' => $this->student->getTranslation('name', 'en'),
                'class_name' => $this->junior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_junior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '0',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => null,
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '13',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '14',
                    ],
                ],
            ],
            [
                'exam' => $this->exam_sem2->getFormattedTranslations('name'),
                'seat_no' => 1,
                'student_no' => $this->student4->student_number,
                'student_name_zh' => $this->student4->getTranslation('name', 'zh'),
                'student_name_en' => $this->student4->getTranslation('name', 'en'),
                'class_name' => $this->senior_first_class->getTranslation('name', 'en'),
                'homeroom_teacher' => $this->employee_senior_first_class->getTranslation('name', 'zh'),
                'principal_name' => $this->school_profile->getTranslation('principal_name', 'zh'),
                'scores' => [
                    [
                        'subject_name_zh' => '马来西亚文',
                        'subject_name_en' => 'BM',
                        'score' => '41',
                    ],
                    [
                        'subject_name_zh' => '英文',
                        'subject_name_en' => 'English',
                        'score' => '42',
                    ],
                    [
                        'subject_name_zh' => '数学',
                        'subject_name_en' => 'Math',
                        'score' => '43',
                    ],
                    [
                        'subject_name_zh' => '历史',
                        'subject_name_en' => 'History',
                        'score' => '44',
                    ],
                    [
                        'subject_name_zh' => '地理',
                        'subject_name_en' => 'Geography',
                        'score' => '45',
                    ],
                ],
            ],
        ]);
});

test('examinationResultByStudentReport return pdf - en', function () {
    $payload = [
        'semester_setting_id' => $this->semester_setting->id,
        'exam_id' => $this->exam_sem2->id,
        'filter_by' => 'GRADE',
        'grade_ids' => [$this->junior_grade->id],
        'export_type' => ExportType::PDF->value,
    ];

    $filename = 'examination-result-by-student';
    $extension = '.pdf';
    Carbon::setTestNow('2025-05-20');
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-student-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    SnappyPdf::assertSee($this->exam_sem2->getFormattedTranslations('name'));

    SnappyPdf::assertSee("(" . $this->school_profile->getTranslation('principal_name', 'zh') . ")");

    SnappyPdf::assertSee("(" . $this->employee_junior_first_class->getTranslation('name', 'zh') . ")");
    SnappyPdf::assertSee('J111');
    SnappyPdf::assertSee($this->student->student_number);
    SnappyPdf::assertSee($this->student->getTranslation('name', 'en'));
    SnappyPdf::assertSee($this->student->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('0');
    SnappyPdf::assertSee('13');
    SnappyPdf::assertSee('14');

    SnappyPdf::assertSee($this->student2->student_number);
    SnappyPdf::assertSee($this->student2->getTranslation('name', 'en'));
    SnappyPdf::assertSee($this->student2->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('21');
    SnappyPdf::assertSee('22');
    SnappyPdf::assertSee('23');
    SnappyPdf::assertSee('24');
    SnappyPdf::assertSee('25');

    SnappyPdf::assertSee("(" . $this->employee_junior_second_class->getTranslation('name', 'zh') . ")");
    SnappyPdf::assertSee('J112');
    SnappyPdf::assertSee($this->student3->student_number);
    SnappyPdf::assertSee($this->student3->getTranslation('name', 'en'));
    SnappyPdf::assertSee($this->student3->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('31');
    SnappyPdf::assertSee('32');
    SnappyPdf::assertSee('33');
    SnappyPdf::assertSee('34');
    SnappyPdf::assertSee('35');

    SnappyPdf::assertDontSee("(" . $this->employee_senior_first_class->getTranslation('name', 'zh') . ")");
    SnappyPdf::assertDontSee('S111');
    SnappyPdf::assertDontSee($this->student4->student_number);
    SnappyPdf::assertDontSee($this->student4->getTranslation('name', 'en'));
    SnappyPdf::assertDontSee($this->student4->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee('41');
    SnappyPdf::assertDontSee('42');
    SnappyPdf::assertDontSee('43');
    SnappyPdf::assertDontSee('44');
    SnappyPdf::assertDontSee('45');

    SnappyPdf::assertSee('发出日期 (Date Issued): 2025-05-20');
});

test('examinationResultBySemesterClass validation error', function () {
    $payload = [];
    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-semester-class", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'results_posting_header_id' => [
                    'The results posting header id field is required.'
                ],
                'semester_class_id' => [
                    'The semester class id field is required.'
                ],
                'report_language' => [
                    'The report language field is required.'
                ],
            ],
            'data' => null
        ]);
});

test('examinationResultBySemesterClass return data', function () {
    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);
    $student3 = Student::factory()->create(['name' => 'Student3']);

    $junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年11班',
        'grade_id' => $junior_grade->id,
    ]);
    $junior_second_class = ClassModel::factory()->create([
        'name->en' => 'J112',
        'name->zh' => '一年12班',
        'grade_id' => $junior_grade->id,
    ]);
    $semester_setting = SemesterSetting::factory()->create();
    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_first_class->id,
        'is_active' => true
    ]);
    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_second_class->id,
        'is_active' => true
    ]);
    // student 1, 2 - junior first class
    // student 3 - junior second class
    $student2_class = StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student3_class = StudentClass::factory()->create([
        'student_id' => $student3->id,
        'semester_class_id' => $second_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'student_ids' => [$student->id, $student2->id, $student3->id],
        'semester_setting_id' => $semester_setting->id,
        'grade_id' => $junior_grade->id,
        'report_card_output_code' => 'SEM1RESULT',
    ]);

    $chinese_subject = Subject::factory()->create([
        'name->en' => 'Chinese Language',
        'name->zh' => '华文',
        'code' => 'CHINESE'
    ]);

    $english_subject = Subject::factory()->create([
        'name->en' => 'English Language',
        'name->zh' => '英文',
        'code' => 'ENGLISH'
    ]);

    $english_oral_subject = Subject::factory()->create([
        'name->en' => 'English Oral',
        'name->zh' => '英文会话',
        'code' => 'ENG_ORAL'
    ]);

    $line_items = [
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GT',
            'total' => 40.44,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GA',
            'total' => 38.88,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MS',
            'total' => 4,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MA',
            'total' => 2,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'total' => 40,
            'label' => 'D',
            'class_rank' => '1',
            'class_population' => '10',
            'grade_rank' => '5',
            'grade_population' => '15',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'C',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $chinese_subject->code,
            'subject_id' => $chinese_subject->id,
            'total' => 70.7,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $english_subject->code,
            'subject_id' => $english_subject->id,
            'total' => 80,
            'output_type' => null,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $english_oral_subject->code,
            'subject_id' => $english_oral_subject->id,
            'label' => 'A+',
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student2->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'E',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $second_semester_class->id,
            'student_id' => $student3->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'A',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $second_semester_class->id,
            'student_id' => $student3->id,
            'report_card_output_component_code' => $chinese_subject->code,
            'subject_id' => $chinese_subject->id,
            'total' => 90,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $second_semester_class->id,
            'student_id' => $student3->id,
            'report_card_output_component_code' => $english_oral_subject->code,
            'subject_id' => $english_oral_subject->id,
            'label' => 'A',
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
        ],
    ];

    foreach ($line_items as $line_item) {
        ResultsPostingLineItem::factory()->create($line_item);
    }

    $payload = [
        'report_language' => 'en',
        'semester_class_id' => $first_semester_class->id,
        'results_posting_header_id' => $header1->id,
        'with_position_in_standard' => true,
    ];
    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-semester-class", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'class' => $junior_first_class->getFormattedTranslations('name'),
            'header' => [
                'seat_no' => 'Seat No.',
                'student_no' => 'Student No.',
                'student_name_en' => 'Student Name',
                'student_name_zh' => 'Student Name',
                'gross_total' => 'Gross Total',
                'gross_average' => 'Gross Average',
                'mark_subtracted' => 'Mark Subtracted',
                'mark_added' => 'Mark Added',
                'net_average' => 'Net Average',
                'grade_exam' => 'Grade',
                'position_in_class' => 'Position In Class',
                'position_in_standard' => 'Position In Standard',
                'conduct' => 'Conduct',
                'subject_' . $chinese_subject->id => 'Chinese Language',
                'subject_' . $english_subject->id => 'English Language',
                'subject_' . $english_oral_subject->id => 'English Oral',
            ],
            'body' => [
                [
                    'seat_no' => $student2_class->seat_no,
                    'student_no' => $student2->student_number,
                    'student_name_en' => $student2->getTranslation('name', 'en'),
                    'student_name_zh' => $student2->getTranslation('name', 'zh'),
                    'gross_total' => '',
                    'gross_average' => '',
                    'mark_subtracted' => '',
                    'mark_added' => '',
                    'net_average' => '',
                    'grade_exam' => '',
                    'position_in_class' => '',
                    'position_in_standard' => '',
                    'conduct' => 'E',
                    'subject_' . $chinese_subject->id => '',
                    'subject_' . $english_subject->id => '',
                    'subject_' . $english_oral_subject->id => '',
                ],
                [
                    'seat_no' => $student_class->seat_no,
                    'student_no' => $student->student_number,
                    'student_name_en' => $student->getTranslation('name', 'en'),
                    'student_name_zh' => $student->getTranslation('name', 'zh'),
                    'gross_total' => '40.44',
                    'gross_average' => '38.88',
                    'mark_subtracted' => '4.00',
                    'mark_added' => '2.00',
                    'net_average' => '40.00',
                    'grade_exam' => 'D',
                    'position_in_class' => '1 / 10',
                    'position_in_standard' => '5 / 15',
                    'conduct' => 'C',
                    'subject_' . $chinese_subject->id => '70.70',
                    'subject_' . $english_subject->id => '80',
                    'subject_' . $english_oral_subject->id => 'A+',
                ],
            ]
        ]);

    $payload = [
        'report_language' => 'zh',
        'semester_class_id' => $second_semester_class->id,
        'results_posting_header_id' => $header1->id,
        'with_position_in_standard' => true,
    ];
    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-semester-class", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'class' => $junior_second_class->getFormattedTranslations('name'),
            'header' => [
                'seat_no' => '座位号',
                'student_no' => '学号',
                'student_name_en' => '学生姓名',
                'student_name_zh' => '学生姓名',
                'gross_total' => '总分',
                'gross_average' => '总平均',
                'mark_subtracted' => '扣分',
                'mark_added' => '校外学艺得分',
                'net_average' => '实得平均',
                'grade_exam' => '成绩等级',
                'position_in_class' => '班级名次',
                'position_in_standard' => '全级名次',
                'conduct' => '操行',
                'subject_' . $chinese_subject->id => '华文',
                'subject_' . $english_oral_subject->id => '英文会话',
            ],
            'body' => [
                [
                    'seat_no' => $student3_class->seat_no,
                    'student_no' => $student3->student_number,
                    'student_name_en' => $student3->getTranslation('name', 'en'),
                    'student_name_zh' => $student3->getTranslation('name', 'zh'),
                    'gross_total' => '',
                    'gross_average' => '',
                    'mark_subtracted' => '',
                    'mark_added' => '',
                    'net_average' => '',
                    'grade_exam' => '',
                    'position_in_class' => '',
                    'position_in_standard' => '',
                    'conduct' => 'A',
                    'subject_' . $chinese_subject->id => '90',
                    'subject_' . $english_oral_subject->id => 'A',
                ],
            ]
        ]);

    $payload = [
        'report_language' => 'zh',
        'semester_class_id' => $second_semester_class->id,
        'results_posting_header_id' => $header1->id,
        'with_position_in_standard' => false,
    ];
    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-semester-class", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'class' => $junior_second_class->getFormattedTranslations('name'),
            'header' => [
                'seat_no' => '座位号',
                'student_no' => '学号',
                'student_name_en' => '学生姓名',
                'student_name_zh' => '学生姓名',
                'gross_total' => '总分',
                'gross_average' => '总平均',
                'mark_subtracted' => '扣分',
                'mark_added' => '校外学艺得分',
                'net_average' => '实得平均',
                'grade_exam' => '成绩等级',
                'position_in_class' => '班级名次',
                'conduct' => '操行',
                'subject_' . $chinese_subject->id => '华文',
                'subject_' . $english_oral_subject->id => '英文会话',
            ],
            'body' => [
                [
                    'seat_no' => $student3_class->seat_no,
                    'student_no' => $student3->student_number,
                    'student_name_en' => $student3->getTranslation('name', 'en'),
                    'student_name_zh' => $student3->getTranslation('name', 'zh'),
                    'gross_total' => '',
                    'gross_average' => '',
                    'mark_subtracted' => '',
                    'mark_added' => '',
                    'net_average' => '',
                    'grade_exam' => '',
                    'position_in_class' => '',
                    'conduct' => 'A',
                    'subject_' . $chinese_subject->id => '90',
                    'subject_' . $english_oral_subject->id => 'A',
                ],
            ]
        ]);
});

test('examinationResultBySemesterClass return pdf', function () {
    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);

    $junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年11班',
        'grade_id' => $junior_grade->id,
    ]);
    $semester_setting = SemesterSetting::factory()->create([
        'semester_year_setting_id' => SemesterYearSetting::create(['year' => 2025])->id,
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_first_class->id,
        'is_active' => true
    ]);

    // student 1, 2 - junior first class
    $student2_class = StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $semester_setting->id
    ]);

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'student_ids' => [$student->id, $student2->id],
        'semester_setting_id' => $semester_setting->id,
        'grade_id' => $junior_grade->id,
        'report_card_output_code' => 'SEM1RESULT',
    ]);

    $chinese_subject = Subject::factory()->create([
        'name->en' => 'Chinese Language',
        'name->zh' => '华文',
        'code' => 'CHINESE'
    ]);
    $english_oral_subject = Subject::factory()->create([
        'name->en' => 'English Oral',
        'name->zh' => '英文会话',
        'code' => 'ENG_ORAL'
    ]);

    $line_items = [
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GT',
            'total' => 40.44,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GA',
            'total' => 38.88,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MS',
            'total' => 4,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MA',
            'total' => 2,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'total' => 40,
            'label' => 'D',
            'class_rank' => '1',
            'class_population' => '10',
            'grade_rank' => '5',
            'grade_population' => '15',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'C',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $chinese_subject->code,
            'subject_id' => $chinese_subject->id,
            'total' => 70.7,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $english_oral_subject->code,
            'subject_id' => $english_oral_subject->id,
            'label' => 'A+',
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student2->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'E',
        ],
    ];

    foreach ($line_items as $line_item) {
        ResultsPostingLineItem::factory()->create($line_item);
    }

    $payload = [
        'report_language' => 'en',
        'semester_class_id' => $first_semester_class->id,
        'results_posting_header_id' => $header1->id,
        'export_type' => ExportType::PDF->value,
        'with_position_in_standard' => true,
    ];

    $filename = 'academy-examination-result-by-semester-class';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-semester-class", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        'Seat No.',
        'Student No.',
        'Student Name',
        'Student Name',
        'Gross Total',
        'Gross Average',
        'Mark Subtracted',
        'Mark Added',
        'Net Average',
        'Grade',
        'Position In Class',
        'Position In Standard',
        'Conduct',
        'Chinese Language',
        'English Oral',
    ];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee('2025 Semester 1 Examination Result');
    SnappyPdf::assertSee('Class: J111 - 一年11班');
    SnappyPdf::assertSee($student->student_number);
    SnappyPdf::assertSee($student->getTranslation('name', 'en'));
    SnappyPdf::assertSee($student->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('40.44');
    SnappyPdf::assertSee('38.88');
    SnappyPdf::assertSee('4.00');
    SnappyPdf::assertSee('2.00');
    SnappyPdf::assertSee('40.00');
    SnappyPdf::assertSee('D');
    SnappyPdf::assertSee('1 / 10');
    SnappyPdf::assertSee('5 / 15');
    SnappyPdf::assertSee('A+');
    SnappyPdf::assertSee('70.70');
    SnappyPdf::assertSee($student2->student_number);
    SnappyPdf::assertSee($student2->getTranslation('name', 'en'));
    SnappyPdf::assertSee($student2->getTranslation('name', 'zh'));
});

test('examinationResultBySemesterClass return pdf with_position_in_standard = false', function () {
    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);

    $junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年11班',
        'grade_id' => $junior_grade->id,
    ]);
    $semester_setting = SemesterSetting::factory()->create([
        'semester_year_setting_id' => SemesterYearSetting::create(['year' => 2025])->id,
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_first_class->id,
        'is_active' => true
    ]);

    // student 1, 2 - junior first class
    $student2_class = StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $semester_setting->id
    ]);

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'student_ids' => [$student->id, $student2->id],
        'semester_setting_id' => $semester_setting->id,
        'grade_id' => $junior_grade->id,
        'report_card_output_code' => 'SEM1RESULT',
    ]);

    $chinese_subject = Subject::factory()->create([
        'name->en' => 'Chinese Language',
        'name->zh' => '华文',
        'code' => 'CHINESE'
    ]);
    $english_oral_subject = Subject::factory()->create([
        'name->en' => 'English Oral',
        'name->zh' => '英文会话',
        'code' => 'ENG_ORAL'
    ]);

    $line_items = [
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GT',
            'total' => 40.44,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GA',
            'total' => 38.88,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MS',
            'total' => 4,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MA',
            'total' => 2,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'total' => 40,
            'label' => 'D',
            'class_rank' => '1',
            'class_population' => '10',
            'grade_rank' => '5',
            'grade_population' => '15',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'C',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $chinese_subject->code,
            'subject_id' => $chinese_subject->id,
            'total' => 70.7,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $english_oral_subject->code,
            'subject_id' => $english_oral_subject->id,
            'label' => 'A+',
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student2->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'E',
        ],
    ];

    foreach ($line_items as $line_item) {
        ResultsPostingLineItem::factory()->create($line_item);
    }

    // with_position_in_standard = false
    $payload = [
        'report_language' => 'en',
        'semester_class_id' => $first_semester_class->id,
        'results_posting_header_id' => $header1->id,
        'export_type' => ExportType::PDF->value,
        'with_position_in_standard' => false,
    ];

    $filename = 'academy-examination-result-by-semester-class';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-semester-class", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    SnappyPdf::assertDontSee('Position In Standard');
    SnappyPdf::assertDontSee('5 / 15');

    SnappyPdf::assertSee('2025 Semester 1 Examination Result');
    SnappyPdf::assertSee('Class: J111 - 一年11班');
    SnappyPdf::assertSee($student->student_number);
    SnappyPdf::assertSee($student->getTranslation('name', 'en'));
    SnappyPdf::assertSee($student->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('40.44');
    SnappyPdf::assertSee('38.88');
    SnappyPdf::assertSee('4.00');
    SnappyPdf::assertSee('2.00');
    SnappyPdf::assertSee('40.00');
    SnappyPdf::assertSee('D');
    SnappyPdf::assertSee('1 / 10');
    SnappyPdf::assertSee('A+');
    SnappyPdf::assertSee('70.70');
    SnappyPdf::assertSee($student2->student_number);
    SnappyPdf::assertSee($student2->getTranslation('name', 'en'));
    SnappyPdf::assertSee($student2->getTranslation('name', 'zh'));
});

test('examinationResultBySemesterClass return excel', function () {
    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);

    $junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年11班',
        'grade_id' => $junior_grade->id,
    ]);
    $semester_setting = SemesterSetting::factory()->create([
        'semester_year_setting_id' => SemesterYearSetting::create(['year' => 2025])->id,
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_first_class->id,
        'is_active' => true
    ]);

    // student 1, 2 - junior first class
    $student2_class = StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $semester_setting->id
    ]);

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'student_ids' => [$student->id, $student2->id],
        'semester_setting_id' => $semester_setting->id,
        'grade_id' => $junior_grade->id,
        'report_card_output_code' => 'SEM1RESULT',
    ]);

    $chinese_subject = Subject::factory()->create([
        'name->en' => 'Chinese Language',
        'name->zh' => '华文',
        'code' => 'CHINESE'
    ]);
    $english_oral_subject = Subject::factory()->create([
        'name->en' => 'English Oral',
        'name->zh' => '英文会话',
        'code' => 'ENG_ORAL'
    ]);

    $line_items = [
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GT',
            'total' => 40.44,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GA',
            'total' => 38.88,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MS',
            'total' => 4,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MA',
            'total' => 2,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'total' => 40,
            'label' => 'D',
            'class_rank' => '1',
            'class_population' => '10',
            'grade_rank' => '5',
            'grade_population' => '15',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'C',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $chinese_subject->code,
            'subject_id' => $chinese_subject->id,
            'total' => 70.7,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $english_oral_subject->code,
            'subject_id' => $english_oral_subject->id,
            'label' => 'A+',
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student2->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'E',
        ],
    ];

    foreach ($line_items as $line_item) {
        ResultsPostingLineItem::factory()->create($line_item);
    }

    $payload = [
        'report_language' => 'en',
        'semester_class_id' => $first_semester_class->id,
        'results_posting_header_id' => $header1->id,
        'export_type' => ExportType::EXCEL->value,
        'with_position_in_standard' => true,
    ];

    Excel::fake();

    $report_print_service_mock = $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('file_url');
    });

    $response = $this->getJson(route($this->routeNamePrefix . "examination-result-by-semester-class", $payload))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toEqual('file_url');

    $report_view_name = 'reports.academy.examination-result-by-semester-class';
    $filename = $report_print_service_mock->getFileName(); // get filename from ReportPrintService
    expect($filename)->toContain('academy-examination-result-by-semester-class.xlsx');

    $expected_headers = [
        'Seat No.',
        'Student No.',
        'Student Name',
        'Student Name',
        'Gross Total',
        'Gross Average',
        'Mark Subtracted',
        'Mark Added',
        'Net Average',
        'Grade',
        'Position In Class',
        'Position In Standard',
        'Conduct',
        'Chinese Language',
        'English Oral',
    ];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $filename,
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $expected_headers, $student, $student2) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee('2025 Semester 1 Examination Result');
            $view->assertSee('Class: J111 - 一年11班');
            $view->assertSee($student->student_number);
            $view->assertSee($student->getTranslation('name', 'en'));
            $view->assertSee($student->getTranslation('name', 'zh'));
            $view->assertSee('40.44');
            $view->assertSee('38.88');
            $view->assertSee('4.00');
            $view->assertSee('2.00');
            $view->assertSee('40.00');
            $view->assertSee('D');
            $view->assertSee('1 / 10');
            $view->assertSee('5 / 15');
            $view->assertSee('A+');
            $view->assertSee('70.70');
            $view->assertSee($student2->student_number);
            $view->assertSee($student2->getTranslation('name', 'en'));
            $view->assertSee($student2->getTranslation('name', 'zh'));

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            return true;
        }
    );
});

test('netAveragePassingRateReportData', function () {
    $grading_scheme = GradingScheme::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);
    $grade_3 = Grade::factory()->create(['name->en' => 'J3']);

    $grades = Grade::all();

    foreach ($grades as $grade) {
        // Create Classes and Semester Classes (3 per grade, per semester, all Junior Grade)
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name . '11', 'name->en' => $grade->name . '11', 'grade_id' => $grade->id,],
            ['code' => $grade->name . '12', 'name->en' => $grade->name . '12', 'grade_id' => $grade->id],
            ['code' => $grade->name . '13', 'name->en' => $grade->name . '13', 'grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        // Creating posting headers, 3 per code, linked with one active report card
        $sem1_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ]);

        $sem2_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ]);

        $final_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ]);

        StudentReportCard::factory()->create(['results_posting_header_id' => $sem1_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $sem2_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $final_header->id]);

        $sem1_output = ReportCardOutput::factory()->create(['name->en' => 'Semester 1', 'name->zh' => '第一学期']);
        $sem2_output = ReportCardOutput::factory()->create(['name->en' => 'Semester 2', 'name->zh' => '第二学期']);
        $final_output =  ReportCardOutput::factory()->create(['name->en' => 'Final', 'name->zh' => '最终的']);

        // Create Result Posting Line Items, each class will have 4 students, 1 student pass
        foreach ($sem1_classes as $sem_class) {
            ResultsPostingLineItem::factory(3)->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'report_card_output_component_id' => ReportCardOutputComponent::factory()->create(['report_card_output_id' => $sem1_output->id]),
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'report_card_output_component_id' => ReportCardOutputComponent::factory()->create(['report_card_output_id' => $sem1_output->id]),
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory(3)->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'report_card_output_component_id' => ReportCardOutputComponent::factory()->create(['report_card_output_id' => $sem2_output->id]),
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'report_card_output_component_id' => ReportCardOutputComponent::factory()->create(['report_card_output_id' => $sem2_output->id]),
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory(3)->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'report_card_output_component_id' => ReportCardOutputComponent::factory()->create(['report_card_output_id' => $final_output->id]),
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'report_card_output_component_id' => ReportCardOutputComponent::factory()->create(['report_card_output_id' => $final_output->id]),
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }
    }

    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id, $grade_2->id, $grade_3->id])
        ->get();

    $filters = [
        'report_language' => 'en',
        'grade_ids' => [$grade_1->id, $grade_2->id, $grade_3->id],
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_ids' => $headers->toArray(),
    ];


    $response = $this->getJson(
        route($this->routeNamePrefix . 'net-average-passing-rate', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            "title" => "Semester 1 J1, J2, J3 Net Average Passing Rate Report",
            "report_data" => [
                "J111" => 75.00,
                "J112" => 75.00,
                "J113" => 75.00,
                "J211" => 75.00,
                "J212" => 75.00,
                "J213" => 75.00,
                "J311" => 75.00,
                "J312" => 75.00,
                "J313" => 75.00
            ]
        ]);

    // validator test
    $filters = [
        'grade_ids' => [9999],
        'semester_setting_id' => 9999,
        'result_posting_header_ids' => [9999]
    ];


    $response = $this->getJson(
        route($this->routeNamePrefix . 'net-average-passing-rate', $filters)
    )->json();

    //dd($response);
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toHaveCount(4)
        ->and($response['error'])->toMatchArray([
            'report_language' => ['The report language field is required.'],
            'semester_setting_id' => ['The selected semester setting id is invalid.'],
            'result_posting_header_ids.0' => ["The selected result_posting_header_ids.0 is invalid."],
            'grade_ids.0' => ['The selected grade_ids.0 is invalid.']
        ]);
});

test('subjectPassingrateReportData', function () {
    $year = SemesterYearSetting::factory()->create(['year' => '2025']);

    $semester_setting_1 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);
    $semester_setting_2 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);


    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);
    $grade_3 = Grade::factory()->create(['name->en' => 'J3']);

    $subject_1 = Subject::factory()->create(['name->en' => 'Chinese Language']);
    $subject_2 = Subject::factory()->create(['name->en' => 'Mathematics']);

    $grades = Grade::all();

    $exam_1 = Exam::factory()->create(['name->en' => '2025 First Semester Exam', 'code' => 'SEM1FINAL']);
    $exam_2 = Exam::factory()->create(['name->en' => '2025 Second Semester Exam', 'code' => 'SEM2FINAL']);

    foreach ($grades as $grade) {
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name . '11', 'name->en' => $grade->name . '11', 'grade_id' => $grade->id,],
            ['code' => $grade->name . '12', 'name->en' => $grade->name . '12', 'grade_id' => $grade->id],
            ['code' => $grade->name . '13', 'name->en' => $grade->name . '13', 'grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        foreach ($sem1_classes as $sem_class) {
            $students = Student::factory(5)->create();

            foreach ($students as $student) {
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_1->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => 2025]);

                $result_source = ResultSource::factory()->create(['code' => 'SEM1EXAM', 'student_grading_framework_id' => $sgf->id]);
                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_1->id]);

                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);
                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                $rss_subject_1_com_1->update(['actual_score' => 80]);

                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                $rss_subject_2_com_1->update(['actual_score' => 80]);
                $rss_subject_2_com_2->update(['actual_score' => 70]);
            }

        }

        foreach ($sem2_classes as $sem_class) {
            $students = Student::factory(5)->create();

            foreach ($students as $student) {
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_2->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);

                $result_source = ResultSource::factory()->create(['code' => 'SEM2EXAM', 'student_grading_framework_id' => $sgf->id]);
                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_2->id]);

                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);
                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                $rss_subject_1_com_1->update(['actual_score' => 80]);

                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                $rss_subject_2_com_1->update(['actual_score' => 80]);
                $rss_subject_2_com_2->update(['actual_score' => 70]);
            }
        }
    }

    $filters = [
        'report_language' => 'en',
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'subject-passing-rate', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            "title" => "2025 First Semester Exam J1 Chinese Language Passing Rate Report",
            "report_data" => [
                "J111" => "100.00",
                "J112" => "100.00",
                "J113" => "100.00"
            ]
        ]);

    $filters = [
        'subject_id' => 9999,
        'grade_id' => 9999,
        'semester_setting_id' => 9999,
        'exam_id' => 9999
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'subject-passing-rate', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'report_language' => ['The report language field is required.'],
            'semester_setting_id' => ['The selected semester setting id is invalid.'],
            'exam_id' => ['The selected exam id is invalid.'],
            'grade_id' => ['The selected grade id is invalid.'],
            'subject_id' => ['The selected subject id is invalid.']
        ]);
});

test('subjectAverageMarkReportData', function () {
    $year = SemesterYearSetting::factory()->create(['year' => '2025']);

    $semester_setting_1 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);
    $semester_setting_2 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);


    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);
    $grade_3 = Grade::factory()->create(['name->en' => 'J3']);

    $subject_1 = Subject::factory()->create(['name->en' => 'Chinese Language']);
    $subject_2 = Subject::factory()->create(['name->en' => 'Mathematics']);

    $grades = Grade::all();

    $exam_1 = Exam::factory()->create(['name->en' => '2025 First Semester Exam', 'code' => 'SEM1FINAL']);
    $exam_2 = Exam::factory()->create(['name->en' => '2025 Second Semester Exam', 'code' => 'SEM2FINAL']);

    foreach ($grades as $grade) {
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name . '11', 'name->en' => $grade->name . '11', 'grade_id' => $grade->id,],
            ['code' => $grade->name . '12', 'name->en' => $grade->name . '12', 'grade_id' => $grade->id],
            ['code' => $grade->name . '13', 'name->en' => $grade->name . '13', 'grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        foreach ($sem1_classes as $sem_class) {
            $students = Student::factory(5)->create();

            foreach ($students as $key => $student) {
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_1->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => 2025]);

                $result_source = ResultSource::factory()->create(['code' => 'SEM1EXAM', 'student_grading_framework_id' => $sgf->id]);
                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_1->id]);

                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                // this will sum up to an average of 75 (65, 70, 75, 80, 85);
                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                // this will sum up to an average of 75 (65, 70, 75, 80, 85);
                $rss_subject_1_com_1->update(['actual_score' => bcadd(65, $key * 5, 2)]);

                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                // after weightage multiplied totals : 61, 66, 71, 76, 81
                $rss_subject_2_com_1->update(['actual_score' => bcadd(65, $key * 5, 2)]);
                $rss_subject_2_com_2->update(['actual_score' => bcadd(55, $key * 5, 2)]);
            }

        }

        foreach ($sem2_classes as $sem_class) {
            $students = Student::factory(5)->create();

            foreach ($students as $key => $student) {
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_2->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);

                $result_source = ResultSource::factory()->create(['code' => 'SEM2EXAM', 'student_grading_framework_id' => $sgf->id]);
                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_2->id]);

                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);
                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                // this will sum up to an average of 75 (65, 70, 75, 80, 85);
                $rss_subject_1_com_1->update(['actual_score' => bcadd(65, $key * 5, 2)]);


                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                $rss_subject_2_com_1->update(['actual_score' => bcadd(65, $key * 5, 2)]);
                $rss_subject_2_com_2->update(['actual_score' => bcadd(55, $key * 5, 2)]);
            }
        }
    }

    $filters = [
        'report_language' => 'en',
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id,
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'subject-average-mark', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            "title" => "2025 First Semester Exam J1 Chinese Language Average Mark Report",
            "report_data" => [
                "J111" => "75.00",
                "J112" => "75.00",
                "J113" => "75.00"
            ]
        ]);

    $filters = [
        'subject_id' => 9999,
        'grade_id' => 9999,
        'semester_setting_id' => 9999,
        'exam_id' => 9999
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'subject-average-mark', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'report_language' => ['The report language field is required.'],
            'semester_setting_id' => ['The selected semester setting id is invalid.'],
            'exam_id' => ['The selected exam id is invalid.'],
            'grade_id' => ['The selected grade id is invalid.'],
            'subject_id' => ['The selected subject id is invalid.']
        ]);
});

test('subjectAnalysisReportData', function () {
    $year = SemesterYearSetting::factory()->create(['year' => '2025']);

    $semester_setting_1 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);
    $semester_setting_2 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);


    $subjects = Subject::factory(3)->state(new Sequence (
        [
            'name->en' => 'Chinese Language',
            'name->zh' => '华文'
        ],
        [
            'name->en' => 'Mathematics',
            'name->zh' => '数学'
        ],
        [
            'name->en' => 'History',
            'name->zh' => '历史'
        ]
    ))->create();


    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);

    $class_1 = ClassModel::factory()->create(['name->en' => 'J111', 'code' => 'J111', 'grade_id' => $grade_1->id]);
    $class_2 = ClassModel::factory()->create(['name->en' => 'J111', 'code' => 'J112', 'grade_id' => $grade_1->id]);

    $sem_class_1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_1->id]);
    $sem_class_2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_2->id]);

    $exam_1 = Exam::factory()->create(['name->en' => '2025 First Semester Exam', 'code' => 'SEM1FINAL']);
    $exam_2 = Exam::factory()->create(['name->en' => '2025 Second Semester Exam', 'code' => 'SEM2FINAL']);

    // Creating Students for Sem Class J111
    $students_in_class_1 = Student::factory(5)->create();
    foreach ($students_in_class_1 as $key => $student) {
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $sem_class_1->id,
            'student_id' => $student->id
        ]);

        $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);
        $result_source = ResultSource::factory()->create(['code' => 'SEM1EXAM', 'student_grading_framework_id' => $sgf->id]);
        ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_1->id]);

        // Subject 0 marks
        $rss_subject_0 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source->id,
        ]);
        $rss_subject_0_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_0->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_0_com_1->update(['actual_score' => 30 + $key * 10]);

        // Subject 1 Marks
        $rss_subject_1 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 60
        ]);
        $rss_subject_1_com_2 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 40
        ]);

        $rss_subject_1_com_1->update(['actual_score' => 35 + $key * 10]);
        $rss_subject_1_com_2->update(['actual_score' => 35 + $key * 10]);

        // Subject 2 Marks
        $rss_subject_2 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_2->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_2_com_1->update(['actual_score' => 47 + $key * 10]);

    }

    // Creating Students for Sem Class J112
    $students_in_class_2 = Student::factory(5)->create();
    foreach ($students_in_class_2 as $key => $student) {
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $sem_class_2->id,
            'student_id' => $student->id
        ]);

        $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);
        $result_source = ResultSource::factory()->create(['code' => 'SEM2EXAM', 'student_grading_framework_id' => $sgf->id]);
        ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_2->id]);

        // Subject 0 Marks
        $rss_subject_0 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source->id,
        ]);
        $rss_subject_0_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_0->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_0_com_1->update(['actual_score' => 20 + $key * 5]);

        // Subject 1 Marks
        $rss_subject_1 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 60
        ]);
        $rss_subject_1_com_2 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 40
        ]);

        $rss_subject_1_com_1->update(['actual_score' => 25 + $key * 5]);
        $rss_subject_1_com_2->update(['actual_score' => 25 + $key * 5]);

        // Subject 2 Marks
        $rss_subject_2 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_2->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_2_com_1->update(['actual_score' => 40 + $key * 5]);
    };

    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $sem_class_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'subject-analysis-data', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['title'])->toBe('2025 First Semester Exam Analysis of Examination Results for J111')
        ->and($response['data']['report_data'][$subjects[0]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 1,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['data']['report_data'][$subjects[1]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 1,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['data']['report_data'][$subjects[2]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 1,
            "90-100" => 0,
        ]);

    $filters = [
        'semester_class_id' => 9999,
        'semester_setting_id' => 9999,
        'exam_id' => 99999
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'subject-analysis-data', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'report_language' => ['The report language field is required.'],
            'semester_setting_id' => ['The selected semester setting id is invalid.'],
            'exam_id' => ['The selected exam id is invalid.'],
            'semester_class_id' => ['The selected semester class id is invalid.']
        ]);
});

test('studentMeritAndExceptionalPerformanceReport return pdf', function () {
    //Student 1
    $student1 = Student::factory()->create([
        'name->en' => 'Student 1'
    ]);

    $semester_setting = SemesterSetting::factory()->create();

    $class = ClassModel::factory()->create([
        'name->en' => 'Class 1'
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
    ]);

    StudentClass::factory()->create([
        'semester_class_id' => $semester_class->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY,
    ]);

    $student1_reward_punishment_records = RewardPunishmentRecord::factory(3)
        ->state(new Sequence(
            [
                'student_id' => $student1->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-01'
            ],
            [
                'student_id' => $student1->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-03'
            ],
            [
                'student_id' => $student1->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-02'
            ],
        ))->create();

    RewardPunishmentRecord::factory(3)
        ->create([
            'student_id' => $student1->id,
            'status' => RewardPunishmentRecordStatus::POSTED,
            'conduct_marks' => -1,
            'display_in_report_card' => true
        ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student1->id,
        'status' => RewardPunishmentRecordStatus::POSTED,
        'conduct_marks' => 1,
        'display_in_report_card' => false
    ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student1->id,
        'status' => RewardPunishmentRecordStatus::DRAFT,
        'conduct_marks' => 1,
        'display_in_report_card' => true
    ]);

    CompetitionRecord::factory(2)->create([
        'student_id' => $student1->id,
    ]);

    //Student 2
    $student2 = Student::factory()->create([
        'name->en' => 'Student 2'
    ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student2->id,
        'conduct_marks' => 1,
        'display_in_report_card' => true
    ]);

    CompetitionRecord::factory(2)->create([
        'student_id' => $student2->id,
    ]);

    $filters = [
        'report_language' => 'en',
        'semester_class_id' => $student1->latestPrimaryClass->semester_class_id,
        'student_ids' => [$student1->id],
        'export_type' => ExportType::PDF->value
    ];

    $filename = 'student-merit-exceptional-performance';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->postJson(
        route($this->routeNamePrefix . 'student-merit-exceptional-performance', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});

test('studentMeritAndExceptionalPerformanceReport() generate pdf', function () {
    Carbon::setTestNow('2025-01-01');
    //Student 1
    $student1 = Student::factory()->create([
        'name->en' => 'Student 1'
    ]);

    $semester_setting = SemesterSetting::factory()->create();

    $class = ClassModel::factory()->create([
        'name->en' => 'Class 1'
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
    ]);

    StudentClass::factory()->create([
        'semester_class_id' => $semester_class->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY,
    ]);

    $student1_reward_punishment_records = RewardPunishmentRecord::factory(3)
        ->state(new Sequence(
            [
                'student_id' => $student1->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-01'
            ],
            [
                'student_id' => $student1->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-03'
            ],
            [
                'student_id' => $student1->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-02'
            ],
        ))->create();

    RewardPunishmentRecord::factory(3)
        ->create([
            'student_id' => $student1->id,
            'status' => RewardPunishmentRecordStatus::POSTED,
            'conduct_marks' => -1,
            'display_in_report_card' => true
        ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student1->id,
        'status' => RewardPunishmentRecordStatus::POSTED,
        'conduct_marks' => 1,
        'display_in_report_card' => false
    ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student1->id,
        'status' => RewardPunishmentRecordStatus::DRAFT,
        'conduct_marks' => 1,
        'display_in_report_card' => true
    ]);

    $student1_competitions = CompetitionRecord::factory(2)
        ->state(new Sequence(
            [
                'student_id' => $student1->id,
                'competition_id' => Competition::factory()->create(['date' => '2024-01-02'])->id,
            ],
            [
                'student_id' => $student1->id,
                'competition_id' => Competition::factory()->create(['date' => '2024-01-01'])->id,
            ]
        ))
        ->create();

    //Student 2
    $student2 = Student::factory()->create([
        'name->en' => 'Student 2'
    ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student2->id,
        'conduct_marks' => 1,
        'display_in_report_card' => true
    ]);

    $merit_setting = MeritDemeritSetting::factory()->create([
        'name' => 'Merit Setting',
        'type' => MeritDemeritType::MERIT,
    ]);

    foreach (RewardPunishmentRecord::all() as $record) {
        if ($record->conduct_marks > 0) {
            $record->rewardPunishment->meritDemeritSettings()->sync($merit_setting);
        }
    }

    CompetitionRecord::factory(2)->create([
        'student_id' => $student2->id,
    ]);

    $data = $this->studentRepository->studentMeritAndExceptionalPerformanceData([$student1->id]);

    $report_data = [
        'students' => $data->toArray(),
        'student_class' => $semester_class->load('classModel')->toArray(),
    ];

    $report_view_name = 'reports.academy.student-merit-exceptional-performance';
    $file_name = 'student-merit-exceptional-performance';
    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate();

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    SnappyPdf::assertSee('Name');
    SnappyPdf::assertSee('姓名');
    SnappyPdf::assertSee('Class');
    SnappyPdf::assertSee('班级');
    SnappyPdf::assertSee('Student No.');
    SnappyPdf::assertSee('学号');
    SnappyPdf::assertSee('Date Issued');
    SnappyPdf::assertSee('发出日期');

    SnappyPdf::assertSee($student1->getTranslation('name', 'en'));
    SnappyPdf::assertSee(e($student1->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($class->getTranslation('name', 'en'));
    SnappyPdf::assertSee(e($class->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($student1->student_number);
    SnappyPdf::assertSee('2025-01-01');

    SnappyPdf::assertSee('No.');
    SnappyPdf::assertSee('Year');
    SnappyPdf::assertSee('奖励记录 Merit Record');

    SnappyPdf::assertSee('1');
    SnappyPdf::assertSee('2024');
    SnappyPdf::assertSee(e($student1_reward_punishment_records[1]->rewardPunishment->name));

    SnappyPdf::assertSee('2');
    SnappyPdf::assertSee('2024');
    SnappyPdf::assertSee(e($student1_reward_punishment_records[2]->rewardPunishment->name));

    SnappyPdf::assertSee('3');
    SnappyPdf::assertSee('2024');
    SnappyPdf::assertSee(e($student1_reward_punishment_records[0]->rewardPunishment->name));

    //competitions
    SnappyPdf::assertSee('1');
    SnappyPdf::assertSee('2024');
    SnappyPdf::assertSee(e($student1_competitions[0]->competition->name) . ' (' . $student1_competitions[0]->award->name . ')');

    SnappyPdf::assertSee('2');
    SnappyPdf::assertSee('2024');
    SnappyPdf::assertSee(e($student1_competitions[1]->competition->name) . ' (' . $student1_competitions[1]->award->name . ')');
});

test('allPassedStudentReport - PDF check', function () {
    $grading_scheme = GradingScheme::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grades = Grade::all();

    foreach ($grades as $grade) {
        // Create Classes and Semester Classes (3 per grade, per semester, all Junior Grade)
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name . '11', 'name->en' => $grade->name . '11', 'grade_id' => $grade->id,],
            ['code' => $grade->name . '12', 'name->en' => $grade->name . '12', 'grade_id' => $grade->id],
            ['code' => $grade->name . '13', 'name->en' => $grade->name . '13', 'grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        // Creating posting headers, 3 per code, linked with one active report card
        $sem1_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ]);

        $sem2_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ]);

        $final_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ]);

        StudentReportCard::factory()->create(['results_posting_header_id' => $sem1_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $sem2_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $final_header->id]);

        // Create Result Posting Line Items, each class will have 4 students, 1 student pass
        foreach ($sem1_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {


            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }
    }

    $header = ResultsPostingHeader::select('id')
        ->where([
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ])
        ->first();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_id' => $header->id,
        'grade_id' => $grade_1->id,
    ];

    $filename = 'all-passed-student-report';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "all-passed-student-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = ['No', 'Class Name', 'Student No', 'Student Name', 'Student Name'];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    // Expect to see grade name
    SnappyPDF::assertSee($grade_1->name);

    //Expect to see J111, J112, J113 classes
    SnappyPDF::assertSee('J111');
    SnappyPDF::assertSee('J112');
    SnappyPDF::assertSee('J113');

    // Expect to see Student A and Student B in J111/J112
    SnappyPDF::assertSee('Student A J111');
    SnappyPDF::assertSee('Student A J112');
    SnappyPDF::assertSee('Student A J113');
    SnappyPDF::assertSee('Student B J111');
    SnappyPDF::assertSee('Student B J112');
    SnappyPDF::assertSee('Student B J113');
});


test('allPassedStudentReport - Excel check', function () {
    $grading_scheme = GradingScheme::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grades = Grade::all();

    foreach ($grades as $grade) {
        // Create Classes and Semester Classes (3 per grade, per semester, all Junior Grade)
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name . '11', 'name->en' => $grade->name . '11', 'grade_id' => $grade->id,],
            ['code' => $grade->name . '12', 'name->en' => $grade->name . '12', 'grade_id' => $grade->id],
            ['code' => $grade->name . '13', 'name->en' => $grade->name . '13', 'grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        // Creating posting headers, 3 per code, linked with one active report card
        $sem1_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ]);

        $sem2_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ]);

        $final_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ]);

        StudentReportCard::factory()->create(['results_posting_header_id' => $sem1_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $sem2_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $final_header->id]);

        // Create Result Posting Line Items, each class will have 4 students, 1 student pass
        foreach ($sem1_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {


            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }
    }

    $header = ResultsPostingHeader::select('id')
        ->where([
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ])
        ->first();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_id' => $header->id,
        'grade_id' => $grade_1->id,
    ];

    $filename = 'all-passed-student-report';
    $extension = '.xslx';

    Excel::fake();

    $report_print_service_mock = $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "all-passed-student-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = ['No', 'Class Name', 'Student No', 'Student Name', 'Student Name'];
    $report_view_name = 'reports.academy.all-passed-student-report';

    $filename = $report_print_service_mock->getFileName();
    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $filename,
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $expected_headers, $grade_1) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($grade_1->name);
            $view->assertSee('J111');
            $view->assertSee('J112');
            $view->assertSee('Student A J111');
            $view->assertSee('Student A J112');
            $view->assertSee('Student A J113');
            $view->assertSee('Student B J111');
            $view->assertSee('Student B J112');
            $view->assertSee('Student B J113');
            return true;
        }
    );
});

test('bestGradeByClassReport - PDF check', function () {
    $grading_scheme = GradingScheme::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grades = Grade::all();

    foreach ($grades as $grade) {
        // Create Classes and Semester Classes (3 per grade, per semester, all Junior Grade)
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name . '11', 'name->en' => $grade->name . '11', 'grade_id' => $grade->id,],
            ['code' => $grade->name . '12', 'name->en' => $grade->name . '12', 'grade_id' => $grade->id],
            ['code' => $grade->name . '13', 'name->en' => $grade->name . '13', 'grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        // Creating posting headers, 3 per code, linked with one active report card
        $sem1_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ]);

        $sem2_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ]);

        $final_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ]);

        StudentReportCard::factory()->create(['results_posting_header_id' => $sem1_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $sem2_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $final_header->id]);

        // Create Result Posting Line Items, each class will have 4 students, 1 student pass
        foreach ($sem1_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 1,
                'total' => 90,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 2,
                'total' => 80,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 3,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student D ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 4,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 1,
                'total' => 90,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 2,
                'total' => 80,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 3,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student D ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 4,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 1,
                'total' => 90,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 2,
                'total' => 80,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 3,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student D ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 4,
                'total' => 50,
            ]);
        }
    }

    $header = ResultsPostingHeader::select('id')
        ->where([
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ])
        ->first();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_id' => $header->id,
        'grade_id' => $grade_1->id,
    ];

    $filename = 'best-grade-by-class-report';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "best-grade-by-class-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = ['No', 'Class', 'Position In Class', 'Net Average', 'Student No.', 'Student Name', 'Student Name'];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    // Expect to see grade name
    SnappyPDF::assertSee($grade_1->name);

    //Expect to see J111, J112, J113 classes
    SnappyPDF::assertSee('J111');
    SnappyPDF::assertSee('J112');
    SnappyPDF::assertSee('J113');

    // Expect to see Student A, B, C in J111/J112
    SnappyPDF::assertSee('Student A J111');
    SnappyPDF::assertSee('Student B J111');
    SnappyPDF::assertSee('Student C J111');

    SnappyPDF::assertSee('Student A J112');
    SnappyPDF::assertSee('Student B J112');
    SnappyPDF::assertSee('Student C J112');

    SnappyPDF::assertSee('Student A J113');
    SnappyPDF::assertSee('Student B J113');
    SnappyPDF::assertSee('Student C J113');

});

test('bestGradeByClassReport - Excel check', function () {
    $grading_scheme = GradingScheme::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grades = Grade::all();

    foreach ($grades as $grade) {
        // Create Classes and Semester Classes (3 per grade, per semester, all Junior Grade)
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name . '11', 'name->en' => $grade->name . '11', 'grade_id' => $grade->id,],
            ['code' => $grade->name . '12', 'name->en' => $grade->name . '12', 'grade_id' => $grade->id],
            ['code' => $grade->name . '13', 'name->en' => $grade->name . '13', 'grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        // Creating posting headers, 3 per code, linked with one active report card
        $sem1_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ]);

        $sem2_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ]);

        $final_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ]);

        StudentReportCard::factory()->create(['results_posting_header_id' => $sem1_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $sem2_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $final_header->id]);

        // Create Result Posting Line Items, each class will have 4 students, 1 student pass
        foreach ($sem1_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 1,
                'total' => 90,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 2,
                'total' => 80,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 3,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student D ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 4,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 1,
                'total' => 90,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 2,
                'total' => 80,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 3,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student D ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 4,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class) {
            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student A ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 1,
                'total' => 90,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student B ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 2,
                'total' => 80,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student C ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 3,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create(['name->en' => 'Student D ' . $sem_class->classModel->name])->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'class_rank' => 4,
                'total' => 50,
            ]);
        }
    }

    $header = ResultsPostingHeader::select('id')
        ->where([
            'grade_id' => $grade_1->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ])
        ->first();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_id' => $header->id,
        'grade_id' => $grade_1->id,
    ];

    $filename = 'best-grade-by-class-report';
    $extension = '.xslx';

    Excel::fake();

    $report_print_service_mock = $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "best-grade-by-class-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = ['No', 'Class', 'Position In Class', 'Net Average', 'Student No.', 'Student Name', 'Student Name'];
    $report_view_name = 'reports.academy.best-grade-by-class-report';

    $filename = $report_print_service_mock->getFileName();
    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $filename,
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $expected_headers, $grade_1) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($grade_1->name);
            $view->assertSee('J111');
            $view->assertSee('J112');
            $view->assertSee('J113');

            $view->assertSee('Student A J111');
            $view->assertSee('Student B J111');
            $view->assertSee('Student C J111');


            $view->assertSee('Student A J112');
            $view->assertSee('Student B J112');
            $view->assertSee('Student C J112');

            $view->assertSee('Student A J113');
            $view->assertSee('Student B J113');
            $view->assertSee('Student C J113');
            return true;
        }
    );
});

test('bestGradeBySubjectReport - PDF test', function () {

    $semester_setting_1 = SemesterSetting::factory()->create([
        'semester_year_setting_id' => SemesterYearSetting::factory()->create(['year' => 2025])->id,
        'name' => '2025 Sem 1'
    ]);

    $grade = Grade::factory()->create(['name->en' => 'J1']);
    $class = ClassModel::factory()->create(['code' => 'J111', 'name->en' => 'J111', 'grade_id' => $grade->id]);

    $semester_class = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class->id]);

    $subjects = Subject::factory(6)->state(new Sequence(
        [
            'type' => SubjectType::MAJOR->value,
            'code' => 'M01',
            'name->en' => 'Chinese',
            'name->zh' => 'Chinese',
            'sequence' => 99,
        ],
        [
            'type' => SubjectType::MAJOR->value,
            'code' => 'M02',
            'name->en' => 'English',
            'name->zh' => 'English',
            'sequence' => 98,
        ],
        [
            'type' => SubjectType::MAJOR->value,
            'code' => 'M03',
            'name->en' => 'Mathematics',
            'name->zh' => 'Mathematics',
            'sequence' => 97,
        ],
        [
            'type' => SubjectType::ELECTIVE->value,
            'code' => 'A-01',
            'name->en' => 'Art 1',
            'name->zh' => 'Art 1',
            'sequence' => 96,
        ],
        [
            'type' => SubjectType::ELECTIVE->value,
            'code' => 'A-02',
            'name->en' => 'Art 2',
            'name->zh' => 'Art 2',
            'sequence' => 95,
        ],
        [
            'type' => SubjectType::COCURRICULUM->value,
            'code' => 'COCURRICULUM',
            'name->en' => 'Cocurriculum',
            'name->zh' => 'Cocurriculum',
            'sequence' => 100,
        ]
    ))->create();

    // Dummy Report Card Output Components to link line items to final and non final outputs for testing purpose
    // In reality, each line item will have its own unique output component
    $normal_output = ReportCardOutput::factory()->create(['code' => 'SEM1RESULT', 'is_final' => false]);
    $final_output = ReportCardOutput::factory()->create(['code' => 'FINALRESULT', 'is_final' => true]);

    $normal_output_component = ReportCardOutputComponent::factory()->create(['report_card_output_id' => $normal_output->id]);
    $final_output_component = ReportCardOutputComponent::factory()->create(['report_card_output_id' => $final_output->id]);

    $sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting_1->id,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    $final_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting_1->id,
        'report_card_output_code' => 'FINALRESULT'
    ]);

    foreach ($subjects as $subject) {
        ResultsPostingLineItem::factory()->create([
            'header_id' => $sem1_header->id,
            'report_card_output_component_code' => $subject->code,
            'report_card_output_component_id' => $normal_output_component->id,
            'student_id' => Student::factory()->create(['name->en' => 'Final Student A ' . $subject->name])->id,
            'subject_id' => $subject->id,
            'grade_id' => $grade->id,
            'semester_class_id' => $semester_class->id,
            'grade_rank' => 1,
            'calculate_rank' => true,
            'total' => 90,
        ]);

        ResultsPostingLineItem::factory()->create([
            'header_id' => $final_header->id,
            'report_card_output_component_code' => $subject->code,
            'report_card_output_component_id' => $final_output_component->id,
            'student_id' => Student::factory()->create(['name->en' => 'Final Student A ' . $subject->name])->id,
            'subject_id' => $subject->id,
            'grade_id' => $grade->id,
            'semester_class_id' => $semester_class->id,
            'grade_rank' => 1,
            'calculate_rank' => true,
            'total' => 90,
        ]);

        ResultsPostingLineItem::factory()->create([
            'header_id' => $final_header->id,
            'report_card_output_component_code' => $subject->code,
            'report_card_output_component_id' => $final_output_component->id,
            'student_id' => Student::factory()->create(['name->en' => 'Final Student B ' . $subject->name])->id,
            'subject_id' => $subject->id,
            'grade_id' => $grade->id,
            'semester_class_id' => $semester_class->id,
            'grade_rank' => 2,
            'calculate_rank' => true,
            'total' => 90,
        ]);
    }

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_id' => $final_header->id,
        'grade_id' => $grade->id,
    ];

    $filename = 'best-grade-by-subject-report';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "best-grade-by-subject-report", $payload))->json();


    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        __('general.no'),
        __('exam.subject'),
        __('general.student_no'),
        __('general.student_name'),
        __('general.student_name'),
        __('general.class'),
        __('exam.marks')
    ];

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    // Expect to see grade name
    SnappyPDF::assertSee($grade->name);

    //Expect to see J111 Class
    SnappyPDF::assertSee('J111');

    // Expect to see Student A as top student for every subject
    SnappyPDF::assertSee('Final Student A English');
    SnappyPDF::assertSee('Final Student A Mathematics');
    SnappyPDF::assertSee('Final Student A Art 1');
    SnappyPDF::assertSee('Final Student A Art 2');
});

test('bestGradeBySubjectReport - Excel test', function () {

    $semester_setting_1 = SemesterSetting::factory()->create([
        'semester_year_setting_id' => SemesterYearSetting::factory()->create(['year' => 2025])->id,
        'name' => '2025 Sem 1'
    ]);

    $grade = Grade::factory()->create(['name->en' => 'J1']);
    $class = ClassModel::factory()->create(['code' => 'J111', 'name->en' => 'J111', 'grade_id' => $grade->id]);

    $semester_class = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class->id]);

    $subjects = Subject::factory(6)->state(new Sequence(
        [
            'type' => SubjectType::MAJOR->value,
            'code' => 'M01',
            'name->en' => 'Chinese',
            'name->zh' => 'Chinese',
            'sequence' => 99,
        ],
        [
            'type' => SubjectType::MAJOR->value,
            'code' => 'M02',
            'name->en' => 'English',
            'name->zh' => 'English',
            'sequence' => 98,
        ],
        [
            'type' => SubjectType::MAJOR->value,
            'code' => 'M03',
            'name->en' => 'Mathematics',
            'name->zh' => 'Mathematics',
            'sequence' => 97,
        ],
        [
            'type' => SubjectType::ELECTIVE->value,
            'code' => 'A-01',
            'name->en' => 'Art 1',
            'name->zh' => 'Art 1',
            'sequence' => 96,
        ],
        [
            'type' => SubjectType::ELECTIVE->value,
            'code' => 'A-02',
            'name->en' => 'Art 2',
            'name->zh' => 'Art 2',
            'sequence' => 95,
        ],
        [
            'type' => SubjectType::COCURRICULUM->value,
            'code' => 'COCURRICULUM',
            'name->en' => 'Cocurriculum',
            'name->zh' => 'Cocurriculum',
            'sequence' => 100,
        ]
    ))->create();

    // Dummy Report Card Output Components to link line items to final and non final outputs for testing purpose
    // In reality, each line item will have its own unique output component
    $normal_output = ReportCardOutput::factory()->create(['code' => 'SEM1RESULT', 'is_final' => false]);
    $final_output = ReportCardOutput::factory()->create(['code' => 'FINALRESULT', 'is_final' => true]);

    $normal_output_component = ReportCardOutputComponent::factory()->create(['report_card_output_id' => $normal_output->id]);
    $final_output_component = ReportCardOutputComponent::factory()->create(['report_card_output_id' => $final_output->id]);

    $sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting_1->id,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    $final_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade->id,
        'semester_setting_id' => $semester_setting_1->id,
        'report_card_output_code' => 'FINALRESULT'
    ]);

    foreach ($subjects as $subject) {
        ResultsPostingLineItem::factory()->create([
            'header_id' => $sem1_header->id,
            'report_card_output_component_code' => $subject->code,
            'report_card_output_component_id' => $normal_output_component->id,
            'student_id' => Student::factory()->create(['name->en' => 'Final Student A ' . $subject->name])->id,
            'subject_id' => $subject->id,
            'grade_id' => $grade->id,
            'semester_class_id' => $semester_class->id,
            'grade_rank' => 1,
            'calculate_rank' => true,
            'total' => 90,
        ]);

        ResultsPostingLineItem::factory()->create([
            'header_id' => $final_header->id,
            'report_card_output_component_code' => $subject->code,
            'report_card_output_component_id' => $final_output_component->id,
            'student_id' => Student::factory()->create(['name->en' => 'Final Student A ' . $subject->name])->id,
            'subject_id' => $subject->id,
            'grade_id' => $grade->id,
            'semester_class_id' => $semester_class->id,
            'grade_rank' => 1,
            'calculate_rank' => true,
            'total' => 90,
        ]);

        ResultsPostingLineItem::factory()->create([
            'header_id' => $final_header->id,
            'report_card_output_component_code' => $subject->code,
            'report_card_output_component_id' => $final_output_component->id,
            'student_id' => Student::factory()->create(['name->en' => 'Final Student B ' . $subject->name])->id,
            'subject_id' => $subject->id,
            'grade_id' => $grade->id,
            'semester_class_id' => $semester_class->id,
            'grade_rank' => 2,
            'calculate_rank' => true,
            'total' => 90,
        ]);
    }

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_id' => $final_header->id,
        'grade_id' => $grade->id,
    ];

    $filename = 'best-grade-by-class-report';
    $extension = '.xslx';

    Excel::fake();

    $report_print_service_mock = $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "best-grade-by-subject-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        __('general.no'),
        __('exam.subject'),
        __('general.student_no'),
        __('general.student_name'),
        __('general.student_name'),
        __('general.class'),
        __('exam.marks')
    ];

    $report_view_name = 'reports.academy.best-grade-by-subject-report';

    $filename = $report_print_service_mock->getFileName();
    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $filename,
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $expected_headers, $grade) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($grade->name);
            $view->assertSee('J111');
            $view->assertSee('Final Student A English');
            $view->assertSee('Final Student A Mathematics');
            $view->assertSee('Final Student A Art 1');
            $view->assertSee('Final Student A Art 2');
            return true;
        }
    );
});

test('bestGradeByGradeReport - PDF test', function () {
    $semester_setting_1 = SemesterSetting::factory()->create(['name->en' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name->en' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);

    // Create Header for SEM1RESULT, SEM2RESULT, FINALRESULT for Grade 1
    $grade1_sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    $grade1_sem2_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM2RESULT'
    ]);

    $grade1_final_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'FINALRESULT'
    ]);

    $grade1_sem1_inactive_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_sem1_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_sem2_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_final_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_sem1_inactive_header->id, 'is_active' => false]);

    // Create Header for SEM1RESULT, SEM2RESULT, FINALRESULT for Grade 2
    $grade2_sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    $grade2_sem2_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM2RESULT'
    ]);

    $grade2_final_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'FINALRESULT'
    ]);

    $grade2_sem1_inactive_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_sem1_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_sem2_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_final_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_sem1_inactive_header->id, 'is_active' => false]);


    // Create Students and Classes
    $class_J1 = ClassModel::factory()->create([
        'code' => 'J111',
        'name->en' => 'J111',
        'grade_id' => $grade_1->id
    ]);

    $class_J2 = ClassModel::factory()->create([
        'code' => 'J211',
        'name->en' => 'J211',
        'grade_id' => $grade_2->id
    ]);

    $sem1_class_J1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_J1->id]);
    $sem1_class_J2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_J2->id]);
    $sem2_class_J1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_2->id, 'class_id' => $class_J1->id]);
    $sem2_class_J2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_2->id, 'class_id' => $class_J2->id]);


    $student_A_J1 = Student::factory()->create(['name->en' => 'Student A J1']);
    $student_B_J1 = Student::factory()->create(['name->en' => 'Student B J1']);
    $student_C_J1 = Student::factory()->create(['name->en' => 'Student C J1']);
    $student_D_J1 = Student::factory()->create(['name->en' => 'Student D J1']);

    $student_A_J2 = Student::factory()->create(['name->en' => 'Student A J2']);
    $student_B_J2 = Student::factory()->create(['name->en' => 'Student B J2']);
    $student_C_J2 = Student::factory()->create(['name->en' => 'Student C J2']);
    $student_D_J2 = Student::factory()->create(['name->en' => 'Student D J2']);


    // Sem1 Header Line Items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    // SEM1 Inactive header line items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    // SEM2 header line items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    // FINAL header line items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'semester_setting_id' => $semester_setting_1->id,
        'report_card_output_code' => 'SEM1RESULT'
    ];

    $filename = 'best-grade-by-grade-report';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "best-grade-by-grade-report", $payload))->json();


    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        __('general.no'),
        __('general.grade'),
        __('general.position_in_standard'),
        __('general.net_average'),
        __('general.student_number'),
        __('general.class'),
        __('general.student_name'),
        __('general.student_name'),
    ];

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    //Expect to see J111 and J211 Class
    SnappyPDF::assertSee('J111');
    SnappyPDF::assertSee('J211');

    // Expect to see Student A as top student for every subject
    SnappyPDF::assertSee($student_A_J1->name);
    SnappyPDF::assertSee($student_B_J1->name);
    SnappyPDF::assertSee($student_C_J1->name);
    SnappyPDF::assertSee($student_A_J2->name);
    SnappyPDF::assertSee($student_B_J2->name);
    SnappyPDF::assertSee($student_C_J2->name);

    SnappyPDF::assertSee($student_A_J1->student_number);
    SnappyPDF::assertSee($student_B_J1->student_number);
    SnappyPDF::assertSee($student_C_J1->student_number);
    SnappyPDF::assertSee($student_A_J2->student_number);
    SnappyPDF::assertSee($student_B_J2->student_number);
    SnappyPDF::assertSee($student_C_J2->student_number);

    SnappyPDF::assertDontSee($student_D_J1->name);
    SnappyPDF::assertDontSee($student_D_J2->name);
});

test('bestGradeByGradeReport - Excel test', function () {
    $semester_setting_1 = SemesterSetting::factory()->create(['name->en' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name->en' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);

    // Create Header for SEM1RESULT, SEM2RESULT, FINALRESULT for Grade 1
    $grade1_sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    $grade1_sem2_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM2RESULT'
    ]);

    $grade1_final_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'FINALRESULT'
    ]);

    $grade1_sem1_inactive_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_sem1_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_sem2_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_final_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade1_sem1_inactive_header->id, 'is_active' => false]);

    // Create Header for SEM1RESULT, SEM2RESULT, FINALRESULT for Grade 2
    $grade2_sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    $grade2_sem2_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM2RESULT'
    ]);

    $grade2_final_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_2->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'FINALRESULT'
    ]);

    $grade2_sem1_inactive_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_1->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_sem1_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_sem2_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_final_header->id, 'is_active' => true]);
    StudentReportCard::factory()->create(['results_posting_header_id' => $grade2_sem1_inactive_header->id, 'is_active' => false]);


    // Create Students and Classes
    $class_J1 = ClassModel::factory()->create([
        'code' => 'J111',
        'name->en' => 'J111',
        'grade_id' => $grade_1->id
    ]);

    $class_J2 = ClassModel::factory()->create([
        'code' => 'J211',
        'name->en' => 'J211',
        'grade_id' => $grade_2->id
    ]);

    $sem1_class_J1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_J1->id]);
    $sem1_class_J2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_J2->id]);
    $sem2_class_J1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_2->id, 'class_id' => $class_J1->id]);
    $sem2_class_J2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_2->id, 'class_id' => $class_J2->id]);


    $student_A_J1 = Student::factory()->create(['name->en' => 'Student A J1']);
    $student_B_J1 = Student::factory()->create(['name->en' => 'Student B J1']);
    $student_C_J1 = Student::factory()->create(['name->en' => 'Student C J1']);
    $student_D_J1 = Student::factory()->create(['name->en' => 'Student D J1']);

    $student_A_J2 = Student::factory()->create(['name->en' => 'Student A J2']);
    $student_B_J2 = Student::factory()->create(['name->en' => 'Student B J2']);
    $student_C_J2 = Student::factory()->create(['name->en' => 'Student C J2']);
    $student_D_J2 = Student::factory()->create(['name->en' => 'Student D J2']);


    // Sem1 Header Line Items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    // SEM1 Inactive header line items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_inactive_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem1_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem1_inactive_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem1_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    // SEM2 header line items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem2_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_sem2_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    // FINAL header line items
    ResultsPostingLineItem::factory(8)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_A_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_B_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_C_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_final_header->id,
            'student_id' => $student_D_J1->id,
            'semester_class_id' => $sem2_class_J1->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_A_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_B_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_C_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_2->id,
            'header_id' => $grade2_final_header->id,
            'student_id' => $student_D_J2->id,
            'semester_class_id' => $sem2_class_J2->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 50,
        ]
    ))->create();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $semester_setting_1->id,
        'report_card_output_code' => 'SEM1RESULT'
    ];

    $filename = 'best-grade-by-grade-report';
    $extension = '.xslx';

    Excel::fake();

    $report_print_service_mock = $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "best-grade-by-grade-report", $payload))->json();


    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        __('general.no'),
        __('general.grade'),
        __('general.position_in_standard'),
        __('general.net_average'),
        __('general.student_number'),
        __('general.class'),
        __('general.student_name'),
        __('general.student_name'),
    ];


    $report_view_name = 'reports.academy.best-grade-by-grade-report';

    $students_J1 = [$student_A_J1, $student_B_J1, $student_C_J1, $student_D_J1];
    $students_J2 = [$student_A_J2, $student_B_J2, $student_C_J2, $student_D_J2];

    $filename = $report_print_service_mock->getFileName();
    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $filename,
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $expected_headers, $students_J1, $students_J2) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee('J111');
            $view->assertSee('J211');

            $view->assertSee($students_J1[0]->name);
            $view->assertSee($students_J1[1]->name);
            $view->assertSee($students_J1[2]->name);
            $view->assertSee($students_J2[0]->name);
            $view->assertSee($students_J2[1]->name);
            $view->assertSee($students_J2[2]->name);

            $view->assertSee($students_J1[0]->student_number);
            $view->assertSee($students_J1[1]->student_number);
            $view->assertSee($students_J1[2]->student_number);
            $view->assertSee($students_J2[0]->student_number);
            $view->assertSee($students_J2[1]->student_number);
            $view->assertSee($students_J2[2]->student_number);

            $view->assertDontSee($students_J1[3]->name);
            $view->assertDontSee($students_J2[3]->name);
            return true;
        }
    );
});

test('positionRankingByGradeReport - PDF test', function () {
    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $semester_setting = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);

    $class_J111 = ClassModel::factory()->create(['code' => 'J111', 'name->en' => 'J111', 'grade_id' => $grade_1->id,]);
    $class_J112 = ClassModel::factory()->create(['code' => 'J112', 'name->en' => 'J112', 'grade_id' => $grade_1->id,]);
    $class_J113 = ClassModel::factory()->create(['code' => 'J113', 'name->en' => 'J113', 'grade_id' => $grade_1->id,]);

    $sem_class_J111 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id, 'class_id' => $class_J111->id]);
    $sem_class_J112 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id, 'class_id' => $class_J112->id]);
    $sem_class_J113 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id, 'class_id' => $class_J113->id]);

    $student_A = Student::factory()->create(['name->en' => 'Student A']);
    $student_B = Student::factory()->create(['name->en' => 'Student B']);
    $student_C = Student::factory()->create(['name->en' => 'Student C']);
    $student_D = Student::factory()->create(['name->en' => 'Student D']);
    $student_E = Student::factory()->create(['name->en' => 'Student E']);
    $student_F = Student::factory()->create(['name->en' => 'Student F']);
    $student_G = Student::factory()->create(['name->en' => 'Student G']);
    $student_H = Student::factory()->create(['name->en' => 'Student H']);
    $student_I = Student::factory()->create(['name->en' => 'Student I']);

    $grade1_sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    // Sem 1 Header
    ResultsPostingLineItem::factory(9)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_A->id,
            'semester_class_id' => $sem_class_J111->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 95,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_B->id,
            'semester_class_id' => $sem_class_J112->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_C->id,
            'semester_class_id' => $sem_class_J113->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 85,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_D->id,
            'semester_class_id' => $sem_class_J111->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 80,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_E->id,
            'semester_class_id' => $sem_class_J112->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 5,
            'total' => 75,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_F->id,
            'semester_class_id' => $sem_class_J113->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 6,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_G->id,
            'semester_class_id' => $sem_class_J111->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 7,
            'total' => 65,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_H->id,
            'semester_class_id' => $sem_class_J112->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 8,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_I->id,
            'semester_class_id' => $sem_class_J113->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 9,
            'total' => 55,
        ]
    ))->create();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'semester_setting_id' => $semester_setting->id,
        'result_posting_header_id' => $grade1_sem1_header->id,
        'grade_id' => $grade_1->id
    ];

    $filename = 'position-ranking-by-grade-report';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "position-ranking-by-grade-report", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        __('general.position_in_standard'),
        __('general.net_average'),
        __('general.student_number'),
        __('general.class'),
        __('general.student_name'),
        __('general.student_name'),
    ];

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    //Expect to see J111, J112, J113
    SnappyPDF::assertSee('J111');
    SnappyPDF::assertSee('J112');
    SnappyPDF::assertSee('J113');

    // Expect to see all students
    SnappyPDF::assertSee($student_A->getTranslation('name', 'en'));
    SnappyPDF::assertSee($student_A->getTranslation('name', 'zh'));
    SnappyPDF::assertSee($student_A->student_number);
    SnappyPDF::assertSee(95.00);  // average mark
    SnappyPDF::assertSee(1);    // grade rank

    SnappyPDF::assertSee($student_B->getTranslation('name', 'en'));
    SnappyPDF::assertSee($student_B->getTranslation('name', 'zh'));
    SnappyPDF::assertSee($student_B->student_number);
    SnappyPDF::assertSee(90.00);  // average mark
    SnappyPDF::assertSee(2);    // grade rank

    SnappyPDF::assertSee($student_C->getTranslation('name', 'en'));
    SnappyPDF::assertSee($student_C->getTranslation('name', 'zh'));
    SnappyPDF::assertSee($student_C->student_number);
    SnappyPDF::assertSee(85.00);  // average mark
    SnappyPDF::assertSee(3);    // grade rank

});

test('positionRankingByGradeReport - Excel test', function () {
    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $semester_setting = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);

    $class_J111 = ClassModel::factory()->create(['code' => 'J111', 'name->en' => 'J111', 'grade_id' => $grade_1->id,]);
    $class_J112 = ClassModel::factory()->create(['code' => 'J112', 'name->en' => 'J112', 'grade_id' => $grade_1->id,]);
    $class_J113 = ClassModel::factory()->create(['code' => 'J113', 'name->en' => 'J113', 'grade_id' => $grade_1->id,]);

    $sem_class_J111 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id, 'class_id' => $class_J111->id]);
    $sem_class_J112 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id, 'class_id' => $class_J112->id]);
    $sem_class_J113 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id, 'class_id' => $class_J113->id]);

    $student_A = Student::factory()->create(['name->en' => 'Student A']);
    $student_B = Student::factory()->create(['name->en' => 'Student B']);
    $student_C = Student::factory()->create(['name->en' => 'Student C']);
    $student_D = Student::factory()->create(['name->en' => 'Student D']);
    $student_E = Student::factory()->create(['name->en' => 'Student E']);
    $student_F = Student::factory()->create(['name->en' => 'Student F']);
    $student_G = Student::factory()->create(['name->en' => 'Student G']);
    $student_H = Student::factory()->create(['name->en' => 'Student H']);
    $student_I = Student::factory()->create(['name->en' => 'Student I']);
    $students = [
        $student_A, $student_B, $student_C, $student_D, $student_E, $student_F, $student_G, $student_H, $student_I
    ];

    $grade1_sem1_header = ResultsPostingHeader::factory()->create([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting->id,
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'report_card_output_code' => 'SEM1RESULT'
    ]);

    // Sem 1 Header
    ResultsPostingLineItem::factory(9)->state(new Sequence(
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_A->id,
            'semester_class_id' => $sem_class_J111->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 1,
            'total' => 95,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_B->id,
            'semester_class_id' => $sem_class_J112->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 2,
            'total' => 90,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_C->id,
            'semester_class_id' => $sem_class_J113->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 3,
            'total' => 85,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_D->id,
            'semester_class_id' => $sem_class_J111->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 4,
            'total' => 80,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_E->id,
            'semester_class_id' => $sem_class_J112->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 5,
            'total' => 75,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_F->id,
            'semester_class_id' => $sem_class_J113->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 6,
            'total' => 70,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_G->id,
            'semester_class_id' => $sem_class_J111->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 7,
            'total' => 65,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_H->id,
            'semester_class_id' => $sem_class_J112->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 8,
            'total' => 60,
        ],
        [
            'grade_id' => $grade_1->id,
            'header_id' => $grade1_sem1_header->id,
            'student_id' => $student_I->id,
            'semester_class_id' => $sem_class_J113->id,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'grade_rank' => 9,
            'total' => 55,
        ]
    ))->create();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $semester_setting->id,
        'result_posting_header_id' => $grade1_sem1_header->id,
        'grade_id' => $grade_1->id
    ];

    Excel::fake();

    $filename = 'position-ranking-by-grade-report';
    $extension = '.pdf';

    $report_print_service_mock = $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(route($this->routeNamePrefix . "position-ranking-by-grade-report", $payload))->json();


    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    $expected_headers = [
        __('general.position_in_standard'),
        __('general.net_average'),
        __('general.student_number'),
        __('general.class'),
        __('general.student_name'),
        __('general.student_name'),
    ];

    $report_view_name = 'reports.academy.position-ranking-by-grade-report';
    $filename = $report_print_service_mock->getFileName();

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $filename,
        'local',
        function (GenericExcelExportViaView $export) use ($report_view_name, $expected_headers, $students) {
            expect($export->view()->name())->toBe($report_view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee('J111');
            $view->assertSee('J112');
            $view->assertSee('J113');

            $view->assertSee($students[0]->getTranslation('name', 'en'));
            $view->assertSee($students[0]->getTranslation('name', 'zh'));
            $view->assertSee($students[0]->student_number);
            $view->assertSee(1);  // grade rank
            $view->assertSee(95); // average marks

            $view->assertSee($students[1]->getTranslation('name', 'en'));
            $view->assertSee($students[1]->getTranslation('name', 'zh'));
            $view->assertSee($students[1]->student_number);
            $view->assertSee(2);  // grade rank
            $view->assertSee(90); // average marks

            $view->assertSee($students[2]->getTranslation('name', 'en'));
            $view->assertSee($students[2]->getTranslation('name', 'zh'));
            $view->assertSee($students[2]->student_number);
            $view->assertSee(3);  // grade rank
            $view->assertSee(85); // average marks

            $view->assertSee($students[3]->getTranslation('name', 'en'));
            $view->assertSee($students[3]->getTranslation('name', 'zh'));
            $view->assertSee($students[3]->student_number);
            $view->assertSee(4);  // grade rank
            $view->assertSee(80); // average marks

            $view->assertSee($students[4]->getTranslation('name', 'en'));
            $view->assertSee($students[4]->getTranslation('name', 'zh'));
            $view->assertSee($students[4]->student_number);
            $view->assertSee(5);  // grade rank
            $view->assertSee(75); // average marks

            $view->assertSee($students[5]->getTranslation('name', 'en'));
            $view->assertSee($students[5]->getTranslation('name', 'zh'));
            $view->assertSee($students[5]->student_number);
            $view->assertSee(6);  // grade rank
            $view->assertSee(70); // average marks

            $view->assertSee($students[6]->getTranslation('name', 'en'));
            $view->assertSee($students[6]->getTranslation('name', 'zh'));
            $view->assertSee($students[6]->student_number);
            $view->assertSee(7);  // grade rank
            $view->assertSee(65); // average marks

            $view->assertSee($students[7]->getTranslation('name', 'en'));
            $view->assertSee($students[7]->getTranslation('name', 'zh'));
            $view->assertSee($students[7]->student_number);
            $view->assertSee(8);  // grade rank
            $view->assertSee(60); // average marks

            $view->assertSee($students[8]->getTranslation('name', 'en'));
            $view->assertSee($students[8]->getTranslation('name', 'zh'));
            $view->assertSee($students[8]->student_number);
            $view->assertSee(9);  // grade rank
            $view->assertSee(55); // average marks

            return true;
        }
    );
});
