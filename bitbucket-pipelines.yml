#Still in progress
image: atlassian/default-image:3

pipelines:
  custom:
    deploy-script:
      - variables:
        - name: CLIENT
        - name: VERSION
        - name: ENV
      - step:
          script:
            - echo "Deploying Version $VERSION for Environment $ENV for Client $CLIENT"
            - echo "AWS ECR URL = $AWS_ECR_HOST/$AWS_ECR_IMAGE_NAME/$CLIENT:$VERSION"
            - docker-compose -f docker-compose.$ENV.yml build --no-cache
            - pipe: atlassian/aws-ecr-push-image:2.4.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                IMAGE_NAME: $AWS_ECR_IMAGE_NAME
                TAGS: $VERSION
            - pipe: atlassian/ssh-run:0.8.1
              variables:
                SSH_USER: ubuntu
                SERVER: $SERVER_IP
                SSH_KEY: $SERVER_SSH_KEY
                MODE: script
                COMMAND: deploy.sh $AWS_ECR_HOST/$AWS_ECR_IMAGE_NAME/$CLIENT:$VERSION
