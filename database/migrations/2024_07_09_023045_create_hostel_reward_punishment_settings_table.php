<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hostel_reward_punishment_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hostel_merit_demerit_setting_id');
            $table->string('code');
            $table->string('name');
            $table->decimal('points');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hostel_reward_punishment_settings');
    }
};
