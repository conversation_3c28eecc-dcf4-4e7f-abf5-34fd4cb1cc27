<?php

use App\Models\ConductSetting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     */
    public function up(): void
    {
        Schema::create('conduct_setting_teachers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conduct_setting_id'); // [ref: > conduct_settings.id]
            $table->foreignId('employee_id'); // [ref: > employees.id]
            $table->boolean('is_homeroom_teacher');
            $table->boolean('is_active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conduct_setting_teachers');
    }
};
