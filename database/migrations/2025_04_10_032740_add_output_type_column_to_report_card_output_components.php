<?php

use App\Models\ReportCardOutputComponent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('report_card_output_components', function (Blueprint $table) {
            $table->string('output_type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('report_card_output_components', function (Blueprint $table) {
            $table->dropColumn('output_type');
        });
    }
};
