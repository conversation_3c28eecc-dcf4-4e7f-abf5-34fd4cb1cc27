<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merit_demerit_settings', function (Blueprint $table) {
            $table->id();
            $table->jsonb('name'); // Translatable
            $table->string('type'); // MERIT, DEMERIT
            $table->decimal('average_exam_marks');
            $table->decimal('conduct_marks');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merit_demerit_settings');
    }
};
