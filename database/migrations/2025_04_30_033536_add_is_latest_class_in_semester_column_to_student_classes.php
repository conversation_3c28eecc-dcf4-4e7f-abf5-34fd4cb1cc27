<?php

use App\Enums\ClassType;
use App\Models\StudentClass;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('student_classes', function (Blueprint $table) {
            $table->boolean('is_latest_class_in_semester')->default(true);
        });

        $student_class_id_to_be_updated = StudentClass::query()->selectRaw('max(id) as id')->where('class_type', ClassType::PRIMARY->value)
            ->groupBy(['semester_setting_id', 'student_id'])
            ->pluck('id')
            ->toArray();

        StudentClass::query()->whereNotIn('id', $student_class_id_to_be_updated)->update(['is_latest_class_in_semester' => false]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_classes', function (Blueprint $table) {
            $table->dropColumn('is_latest_class_in_semester');
        });
    }
};
