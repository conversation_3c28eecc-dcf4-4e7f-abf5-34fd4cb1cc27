<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_special_settings', function (Blueprint $table) {
            $table->id();
            $table->string('module'); // HOSTEL, EXAM
            $table->string('submodule')->nullable(); // HOSTEL_PIC
            $table->foreignId('user_id');
            $table->json('custom_permissions')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_special_settings');
    }
};
