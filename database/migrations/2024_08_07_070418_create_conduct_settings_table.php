<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conduct_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('semester_setting_id')->index(); // ref > semester_settings
            $table->foreignId('semester_class_id')->index(); // ref > semester_classes
            $table->foreignId('grading_scheme_id')->index(); // ref > master_grading_schemes
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conduct_settings');
    }
};
