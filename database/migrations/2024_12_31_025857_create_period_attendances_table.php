<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('period_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->index();
            $table->date('date');
            $table->morphs('timeslot');
            $table->foreignId('updated_by_employee_id')->index()->nullable();
            $table->string('status');
            $table->foreignId('leave_application_id')->index()->nullable();
            $table->unsignedInteger('period');
            $table->boolean('has_mark_deduction')->default(true);
            $table->timestamps();

            $table->index(['student_id', 'status', 'date'], 'idx_pa_report_card_output_1');
            $table->index(['student_id', 'date', 'period'], 'idx_pa_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('period_attendances');
    }
};
