<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE MATERIALIZED VIEW latest_primary_class_by_semester_setting_views AS
                SELECT semester_class_id,
                    semester_setting_id,
                    class_type,
                    student_id,
                    seat_no,
                    class_enter_date,
                    class_leave_date
                FROM student_classes
                WHERE id in (
                    SELECT max(id)
                    FROM student_classes
                    WHERE class_type = 'PRIMARY'
                    GROUP BY semester_setting_id, student_id
                );
        ");

        DatabaseHelper::createIndex('latest_primary_class_by_semester_setting_views', 'idx_lpcbssv_1', ['semester_class_id', 'semester_setting_id', 'student_id', 'class_leave_date']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP MATERIALIZED VIEW IF EXISTS latest_primary_class_by_semester_setting_views;");
    }
};
