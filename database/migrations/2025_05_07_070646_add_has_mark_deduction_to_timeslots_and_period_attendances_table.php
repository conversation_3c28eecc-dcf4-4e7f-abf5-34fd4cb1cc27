<?php

use App\Models\Period;
use App\Models\Timeslot;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('timeslots', function (Blueprint $table) {
            if (!Schema::hasColumn('timeslots', 'has_mark_deduction')) {
                $table->boolean('has_mark_deduction')->default(true);
            }
        });

        // period 15 ids
        $period_ids = Period::query()
            ->where('period', 15)
            ->pluck('id')
            ->toArray();

        Timeslot::query()->whereIn('period_id', $period_ids)->update(['has_mark_deduction' => false]);

        Schema::table('period_attendances', function (Blueprint $table) {
            if (!Schema::hasColumn('period_attendances', 'has_mark_deduction')) {
                $table->boolean('has_mark_deduction')->default(true);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};
