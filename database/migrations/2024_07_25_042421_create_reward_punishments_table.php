<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reward_punishments', function (Blueprint $table) {
            $table->id();
            $table->jsonb('name');
            $table->foreignId('category_id')->index();
            $table->foreignId('sub_category_id')->index()->nullable();
            $table->decimal('average_exam_marks');
            $table->decimal('conduct_marks');
            $table->boolean('display_in_report_card');
            $table->boolean('is_active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reward_punishments');
    }
};
