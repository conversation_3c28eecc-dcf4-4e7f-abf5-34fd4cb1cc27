<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_period_override', function (Blueprint $table) {
            $table->id();
            $table->morphs('attendance_recordable');
            $table->date('period');
            $table->time('attendance_from');
            $table->time('attendance_to');
            $table->foreignId('updated_by_employee_id');
            $table->timestamps();

            $table->unique(['attendance_recordable_type', 'attendance_recordable_id', 'period'], 'unique_recordable_period');
            $table->index(['attendance_recordable_type', 'attendance_recordable_id', 'period'], 'idx_apo_1');
            $table->index(['period']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_period_override');
    }
};
