<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_input_error_logs', function (Blueprint $table) {
            $table->id();
            $table->string('card_number');
            $table->datetime('time');
            $table->text('error_message');
            $table->foreignId('terminal_id')->nullable();
            $table->timestamps();

            $table->index(['card_number', 'time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_input_error_logs');
    }
};
