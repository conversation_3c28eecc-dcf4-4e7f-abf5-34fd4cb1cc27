<?php

use App\Models\UnpaidItemAssignment;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unpaid_item_assignments', function (Blueprint $table) {
            $table->id();
            $table->string('name', 128);
            $table->unsignedInteger('product_id');
            $table->enum('status', [UnpaidItemAssignment::STATUS_NEW, UnpaidItemAssignment::STATUS_APPLYING, UnpaidItemAssignment::STATUS_COMPLETED]);
            $table->json('recurring_settings');
            $table->string('currency_code', 4);
            $table->decimal('unit_price', 12, 2);
            $table->decimal('quantity', 12, 2);
            $table->decimal('amount_before_tax', 12, 2);
            $table->dateTime('apply_at');
            $table->dateTime('applied_at')->nullable();
            $table->timestamps();

            $table->index(['product_id', 'status']);
            $table->index(['apply_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unpaid_item_assignments');
    }
};
