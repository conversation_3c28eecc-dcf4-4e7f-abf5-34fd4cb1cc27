<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateway_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('requested_by_id');
            $table->unsignedInteger('billing_document_id');
            $table->string('type', 50);
            $table->string('provider', 50);
            $table->unsignedInteger('payment_method_id');
            $table->unsignedInteger('bank_id')->nullable()->comment('Only applicable for FPX');
            $table->nullableMorphs('transaction_loggable');
            $table->string('order_id')->nullable();
            $table->foreignId('currency_id');
            $table->string('currency_code', 6);
            $table->string('currency_name');
            $table->decimal('amount', 12, 2);
            $table->string('status', 50);
            $table->string('description')->nullable();
            $table->string('payment_url')->nullable();
            $table->string('remark')->nullable();
            $table->json('request_data')->nullable();
            $table->json('response_data')->nullable();
            $table->json('callback_data')->nullable();
            $table->string('reference_id')->nullable();
            $table->dateTime('transaction_datetime')->nullable();
            $table->string('token');
            $table->timestamps();

            $table->index(['type', 'provider', 'order_id', 'status'], 'payment_gateway_logs_idx');
            $table->index(['billing_document_id'], 'idx_billing_document');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_gateway_logs');
    }
};
