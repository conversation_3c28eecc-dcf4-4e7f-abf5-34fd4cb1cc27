<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enrollment_login_otps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('enrollment_user_id');
            $table->string('otp');
            $table->dateTime('expired_at');
            $table->timestamps();

            $table->index(['enrollment_user_id', 'expired_at'], 'idx_otp_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enrollment_login_otps');
    }
};
