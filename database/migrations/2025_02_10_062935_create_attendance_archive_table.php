<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_archive', function (Blueprint $table) {
            $table->id();
            $table->dateTime('version');
            $table->morphs('attendance_recordable');
            $table->date('date');
            $table->dateTime('check_in_datetime')->nullable();
            $table->string('check_in_status')->nullable(); // ON_TIME/LATE
            $table->text('check_in_remarks')->nullable();
            $table->nullableMorphs('check_in_overridable');
            $table->dateTime('attendance_from')->nullable();
            $table->dateTime('check_out_datetime')->nullable();
            $table->string('check_out_status')->nullable(); // ON_TIME/LEFT_EARLY
            $table->text('check_out_remarks')->nullable();
            $table->nullableMorphs('check_out_overridable');
            $table->dateTime('attendance_to')->nullable();
            $table->string('status'); // ABSENT/PRESENT
            $table->foreignId('card_id')->nullable();
            $table->foreignId('leave_application_id')->nullable();
            $table->boolean('is_error')->default(false);
            $table->text('error_message')->nullable();
            $table->timestamps();

            $table->index(['attendance_recordable_id', 'attendance_recordable_type', 'date'], 'idx_att_archive_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_archive');
    }
};
