<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_semester_settings', function (Blueprint $table) {
            $table->id();
            $table->string('category');
            $table->date('from_date');
            $table->date('to_date');
            $table->foreignId('grade_id');
            $table->foreignId('semester_setting_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_semester_settings');
    }
};
