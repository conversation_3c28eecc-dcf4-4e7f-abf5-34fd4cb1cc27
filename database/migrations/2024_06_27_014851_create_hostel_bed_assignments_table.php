<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     */
    public function up(): void
    {
        Schema::create('hostel_bed_assignments', function (Blueprint $table) {
            $table->id();
            $table->morphs('assignable'); // refer to Student
            $table->foreignId('hostel_room_bed_id'); // refer to HostelRoomBed
            $table->foreignId('previous_hostel_room_bed_id')->nullable(); // refer to HostelRoomBed
            $table->foreignId('assigned_by'); // refer to User
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->text('remarks')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hostel_bed_assignments');
    }
};
