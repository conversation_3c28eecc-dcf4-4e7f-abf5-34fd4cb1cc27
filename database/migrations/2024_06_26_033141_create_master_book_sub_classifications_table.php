<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_book_sub_classifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('book_classification_id');
            $table->string('code')->unique()->nullable();
            $table->jsonb('name');
            $table->timestamps();
        });

        DB::statement("CREATE INDEX master_book_sub_classifications_code_gin_idx ON master_book_sub_classifications USING gin (code gin_trgm_ops)");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_book_sub_classifications');
    }
};
