<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ecommerce_product_group_assignments', function (Blueprint $table) {
            $table->unsignedBigInteger('product_group_id');
            $table->unsignedBigInteger('product_id');

            $table->index(['product_group_id', 'product_id'], 'ecommerce_product_group_assignments_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ecommerce_product_group_assignments');
    }
};
