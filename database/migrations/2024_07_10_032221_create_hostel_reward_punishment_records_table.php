<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hostel_reward_punishment_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('person_in_charge_id')->index(); // ref to Employees
            $table->foreignId('student_id')->index(); // ref to Students
            $table->date('date');
            $table->foreignId('hostel_reward_punishment_setting_id')->index(); // ref to HostelRewardPunishmentSetting
            $table->string('remark')->nullable();
            $table->foreignId('created_by')->index(); // ref to Users
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hostel_reward_punishment_records');
    }
};
