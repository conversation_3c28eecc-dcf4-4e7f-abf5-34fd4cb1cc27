<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enrollment_guardians', function (Blueprint $table) {
            $table->id();
            $table->jsonb('name');
            $table->string('email')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('nric')->nullable();
            $table->string('passport_number')->nullable();
            $table->foreignId('nationality_id')->nullable();
            $table->foreignId('race_id')->nullable();
            $table->foreignId('religion_id')->nullable();
            $table->foreignId('education_id')->nullable();
            $table->string('married_status')->nullable();
            $table->string('live_status')->nullable();
            $table->string('occupation')->nullable();
            $table->string('occupation_description')->nullable();
            $table->text('remarks')->nullable();
            $table->boolean('is_primary')->default(false);
            $table->string('guardian_type')->nullable();
            $table->foreignId('enrollment_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enrollment_guardians');
    }
};
