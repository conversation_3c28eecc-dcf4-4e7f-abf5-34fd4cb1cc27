<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_payment_methods', function (Blueprint $table) {
            $table->id();
            $table->string('code', 16);
            $table->string('name', 32);
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('settings')->nullable();
            $table->timestamps();

            $table->index(['is_active']);
        });

        DB::statement("CREATE INDEX master_payment_methods_code_gin_idx ON master_payment_methods USING gin (code gin_trgm_ops)");
        DB::statement("CREATE INDEX master_payment_methods_name_gin_idx ON master_payment_methods USING gin (name gin_trgm_ops)");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_payment_methods');
    }
};
