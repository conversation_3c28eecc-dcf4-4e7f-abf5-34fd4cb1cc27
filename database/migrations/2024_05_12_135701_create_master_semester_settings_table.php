<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_semester_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->index();
            $table->foreignId('semester_year_setting_id')->index()->nullable();
            $table->string('name');
            $table->date('from');
            $table->date('to');
            $table->boolean('is_current_semester');
            $table->timestamps();

            $table->index(['from', 'to']);
            $table->index(['is_current_semester']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_semester_settings');
    }
};
