<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_period_override_leave_application', function (Blueprint $table) {
            $table->foreignId('attendance_period_override_id');
            $table->foreignId('leave_application_id');

            $table->index(['attendance_period_override_id', 'leave_application_id'], 'attendance_period_override_leave_application_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_period_override_leave_applications');
    }
};
