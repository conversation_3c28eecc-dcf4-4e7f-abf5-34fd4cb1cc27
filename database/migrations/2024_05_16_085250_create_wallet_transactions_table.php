<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('card_id')->nullable();
            $table->foreignId('wallet_id');
            $table->nullableMorphs('userable');
            $table->string('reference_no')->unique()->nullable();
            $table->string('type');
            $table->string('status');
            $table->nullableMorphs('wallet_transactable');
            $table->decimal('total_amount', 14);
            $table->decimal('amount_before_tax', 14)->default(0);
            $table->decimal('amount_after_tax', 14)->default(0);
            $table->string('tax_type')->nullable();
            $table->decimal('tax_value', 14)->default(0);
            $table->decimal('balance_before', 14);
            $table->decimal('balance_after', 14);
            $table->string('description')->nullable();
            $table->string('remark')->nullable();
            $table->timestamps();

            $table->index(['card_id', 'wallet_id', 'type', 'status'], 'wallet_transactions_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
