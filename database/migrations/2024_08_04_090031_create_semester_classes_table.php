<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('semester_classes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('semester_setting_id')->index();
            $table->foreignId('class_id')->index();
            $table->foreignId('homeroom_teacher_id')->index()->nullable(); // foreign key to employees table
            $table->foreignId('default_grading_framework_id')->index()->nullable();
            $table->boolean('is_active');
            $table->timestamps();

            $table->index(['semester_setting_id', 'is_active']);
            $table->index(['class_id', 'is_active']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('semester_classes');
    }
};
