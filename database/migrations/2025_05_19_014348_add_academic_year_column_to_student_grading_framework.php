<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('student_grading_frameworks', function (Blueprint $table) {
            // TODO: Change to non-nullable before go-live, nullable here is only for testing purposes
            $table->year('academic_year')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_grading_framework', function (Blueprint $table) {
            $table->dropColumn('academic_year');
        });
    }
};
