<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ecommerce_products', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('merchant_id');
            $table->string('code')->unique();
            $table->string('name');
            $table->string('description')->nullable();
            $table->decimal('price_before_tax')->default(0);
            $table->unsignedInteger('tax_id');
            $table->string('currency_code', 10);
            $table->boolean('is_active')->default(true);
            $table->integer('sequence')->default(0);
            $table->timestamps();

            $table->index(['merchant_id', 'code', 'name', 'is_active'], 'ecommerce_products_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ecommerce_products');
    }
};
