<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('competition_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('competition_id')->index();
            $table->foreignId('student_id')->index();
            $table->foreignId('award_id')->index();
            $table->string('type_of_bonus'); // [PERFORMANCE, OFF_CAMPUS]
            $table->float('mark')->default(0);
            $table->foreignId('semester_class_id')->index();
            $table->timestamps();

            $table->index(['competition_id', 'student_id', 'award_id'], 'idx_cr_1');
            $table->index(['competition_id', 'student_id'], 'idx_cr_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('competition_records');
    }
};
