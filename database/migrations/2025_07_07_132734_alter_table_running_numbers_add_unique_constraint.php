<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('ALTER TABLE running_numbers ADD CONSTRAINT "running_number_unique" UNIQUE NULLS NOT DISTINCT (document_type, year, month, identifier1, identifier2)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('ALTER TABLE running_numbers DROP CONSTRAINT "running_number_unique"');
    }
};
