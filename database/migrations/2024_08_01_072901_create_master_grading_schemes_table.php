<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     *
     */
    public function up(): void
    {
        Schema::create('master_grading_schemes', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->string('code', 32)->unique();
            $table->string('name');
            $table->boolean('is_active');
            $table->timestamps();

            $table->index(['code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_grading_schemes');
    }
};
