<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calendar_targets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('calendar_id');
            $table->integer('priority');
            $table->morphs('calendar_targetable');
            $table->timestamps();

            $table->index(['calendar_id', 'priority', 'calendar_targetable_id', 'calendar_targetable_type'], 'calendar_targets_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('calendar_targets');
    }
};
