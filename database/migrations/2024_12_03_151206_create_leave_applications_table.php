<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leave_applications', function (Blueprint $table) {
            $table->id();
            $table->morphs('leave_applicable');
            $table->string('status');
            $table->foreignId('leave_application_type_id')->index();
            $table->text('reason');
            $table->text('remarks')->nullable();
            $table->boolean('is_present')->default(0);
            $table->boolean('is_full_day')->default(0);
            $table->decimal('average_point_deduction', 5, 2)->default(0);
            $table->decimal('conduct_point_deduction', 5, 2)->default(0);
            $table->timestamps();

            $table->index(['status', 'is_present', 'is_full_day'], 'idx_leave_applications_1');
            $table->index(['leave_applicable_type', 'leave_applicable_id', 'status'], 'idx_leave_applications_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leave_applications');
    }
};
