<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        DB::statement('
            CREATE MATERIALIZED VIEW student_timetable AS
                SELECT student_classes.student_id as student_id, timetables.id AS timetable_id,
                    timetables.name AS timetable_name, period_groups.name AS period_group_name,
                    timeslots.id AS timeslot_id, timeslots.day, periods.from_time, periods.to_time, periods.period,
                    timeslots.attendance_from, timeslots.attendance_to, timeslots.placeholder, timeslots.default_init_status, timeslots.has_mark_deduction, 
                    subjects.type AS subject_type, subjects.code AS subject_code, subjects.name AS subject_name, classes.code AS class_code,
                    classes.name AS class_name, classes.type AS class_type, period_labels.id AS period_label_id from "timeslots"
                INNER JOIN "periods" on "periods"."id" = "timeslots"."period_id"
                LEFT JOIN "class_subjects" on "class_subjects"."id" = "timeslots"."class_subject_id"
                LEFT JOIN "subjects" on "subjects"."id" = "class_subjects"."subject_id"
                INNER JOIN "timetables" on "timetables"."id" = "timeslots"."timetable_id"
                INNER JOIN "period_groups" on "period_groups"."id" = "timetables"."period_group_id"
                INNER JOIN "period_labels" on "period_labels"."period_group_id" = "period_groups"."id" and "period_labels"."period" = "periods"."period"
                INNER JOIN "semester_classes" on "semester_classes"."id" = "timetables"."semester_class_id"
                INNER JOIN "classes" on "classes"."id" = "semester_classes"."class_id"
                INNER JOIN "student_classes" on "student_classes"."semester_class_id" = "semester_classes"."id"
                WHERE
                    "student_classes"."is_active" = true AND
                    "semester_classes"."is_active" = true AND
                    "period_labels"."is_attendance_required" = true AND
                    timetables.is_active = true
        ');

        DatabaseHelper::createUniqueIndex('student_timetable', 'idx_student_timetable_unique_1', ['student_id', 'timeslot_id']);
        DatabaseHelper::createIndex('student_timetable', 'idx_student_timetable_1', ['student_id']);
        DatabaseHelper::createIndex('student_timetable', 'idx_student_timetable_2', ['student_id', 'day']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP MATERIALIZED VIEW IF EXISTS student_timetable;");
    }
};
