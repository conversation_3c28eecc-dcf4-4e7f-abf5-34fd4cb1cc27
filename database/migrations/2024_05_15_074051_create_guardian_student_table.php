<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('guardian_student', function (Blueprint $table) {
            $table->foreignId('guardian_id');
            $table->string('type');
            $table->morphs('studenable');
            $table->string('relation_to_student')->nullable();
            $table->boolean('is_primary')->default(false);
            //$table->boolean('is_direct_dependant');

            $table->index(['guardian_id', 'type', 'studenable_type', 'studenable_id'], 'idx_guardian_student_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('guardian_student');
    }
};
