<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_grading_frameworks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id');
            $table->foreignId('grading_framework_id');
            $table->date('effective_from');
            $table->date('effective_to');
            $table->boolean('is_active');
            $table->jsonb('configuration');     // json based config to provision all grading related data structure
            $table->timestamps();

            $table->index(['student_id', 'is_active']);
            $table->index(['grading_framework_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_grading_frameworks');
    }
};
