<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE MATERIALIZED VIEW historical_students_class_and_grade_view AS
                SELECT master_semester_year_settings.year AS year, master_semester_settings.id AS semester_setting_id, master_semester_settings.name AS semester_name,
                    students.id AS student_id, students.name AS student_name, students.student_number AS student_number, classes.id AS class_id,
                    classes.name AS class_name, classes.code AS class_code, master_grades.id AS grade_id, master_grades.name AS grade_name, class_type
                    FROM student_classes
                    INNER JOIN students ON students.id = student_classes.student_id
                    INNER JOIN semester_classes ON semester_classes.id = student_classes.semester_class_id
                    INNER JOIN classes ON classes.id = semester_classes.class_id
                    INNER JOIN master_semester_settings ON master_semester_settings.id = semester_classes.semester_setting_id
                    INNER JOIN master_semester_year_settings ON master_semester_year_settings.id = master_semester_settings.semester_year_setting_id
                    LEFT JOIN master_grades ON master_grades.id = classes.grade_id
                    WHERE student_classes.is_latest_class_in_semester = true
                    ORDER BY student_id ASC, year ASC, semester_name ASC
        ");

        DatabaseHelper::createUniqueIndex('historical_students_class_and_grade_view', 'idx_hscagv_unique_1', ['semester_setting_id', 'student_id', 'class_id']);
        DatabaseHelper::createIndex('historical_students_class_and_grade_view', 'idx_hscagv_2', ['semester_setting_id', 'class_id']);
        DatabaseHelper::createIndex('historical_students_class_and_grade_view', 'idx_hscagv_3', ['semester_setting_id', 'grade_id']);
        DatabaseHelper::createIndex('historical_students_class_and_grade_view', 'idx_hscagv_4', ['semester_setting_id', 'student_id', 'class_type']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP MATERIALIZED VIEW IF EXISTS historical_students_class_and_grade_view;");
    }
};
