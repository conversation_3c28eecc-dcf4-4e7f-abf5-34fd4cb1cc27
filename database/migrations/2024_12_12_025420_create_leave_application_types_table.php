<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leave_application_types', function (Blueprint $table) {
            $table->id();
            $table->jsonb('name');
            $table->boolean('is_present')->default(0);
            $table->decimal('average_point_deduction', 5, 2)->default(0);
            $table->decimal('conduct_point_deduction', 5, 2)->default(0);
            $table->timestamps();

            $table->index(['is_present']);

        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leave_application_types');
    }
};
