<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_bank_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('bank_id');
            $table->string('bankable_type');        // owner of the bank account e.g. legal entity, parents bank account, etc.
            $table->unsignedInteger('bankable_id');
            $table->string('currency_code', 4);
            $table->string('account_name', 128);
            $table->string('account_number', 32);
            $table->text('label');
            $table->text('address')->nullable();
            $table->boolean('is_active');
            $table->timestamps();

            $table->index(['bank_id', 'bankable_type', 'bankable_id', 'currency_code', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_bank_accounts');
    }
};
