<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_documents', function (Blueprint $table) {
            $table->id();
            $table->string('type', 16)->comment('invoice, advance_invoice, credit_note, debit_note');
            $table->string('sub_type', 32)->nullable()->comment('??');
            $table->string('classification', 4)->comment('AR / AP');
            $table->date('document_date');
            $table->date('posting_date')->nullable();
            $table->string('status')->comment('draft, confirmed, posted, voided');
            $table->string('payment_status')->comment('unpaid, partial, paid');
            $table->dateTime('paid_at')->nullable();
            $table->string('reference_no', 32);
            $table->unsignedInteger('legal_entity_id');
            $table->string('legal_entity_name', 128);
            $table->text('legal_entity_address')->nullable();
            $table->string('bill_to_type');     // student / merchant / etc.
            $table->unsignedInteger('bill_to_id');
            $table->string('bill_to_name', 128);
            $table->string('bill_to_reference_number')->nullable();
            $table->text('bill_to_address')->nullable();
            $table->string('counterparty_reference_no', 64)->nullable();
            $table->string('tax_code', 8);
            $table->string('tax_description', 64);
            $table->decimal('tax_percentage', 6, 2);
            $table->date('payment_due_date');
            $table->unsignedInteger('payment_term_id');
            $table->unsignedInteger('remit_to_id')->nullable();
            $table->string('remit_to_account_number', 32)->nullable();
            $table->string('remit_to_account_name', 128)->nullable();
            $table->string('remit_to_bank_name', 128)->nullable();
            $table->text('remit_to_bank_address')->nullable();
            $table->string('remit_to_swift_code', 16)->nullable();
            $table->string('receipt_url')->nullable();
            $table->string('currency_code', 4);
            $table->decimal('amount_before_tax', 12, 2);
            $table->decimal('amount_before_tax_after_less_advance', 12, 2);
            $table->decimal('tax_amount', 12, 2);
            $table->decimal('amount_after_tax', 12, 2);

            $table->string('einvoice_validation_status', 16)->nullable()->comment('pending, in_progress, success, failed, not_applicable');
            $table->dateTime('einvoice_validation_datetime')->nullable();
            $table->string('einvoice_reference_no', 64)->nullable();

            $table->timestamps();

            $table->index(['type', 'legal_entity_id', 'document_date', 'status', 'bill_to_id', 'bill_to_type'], 'idx_billing_document_1');
            $table->index(['reference_no'], 'idx_billing_document_2');
            $table->index(['bill_to_id', 'bill_to_type', 'bill_to_reference_number', 'payment_status', 'type'], 'idx_billing_document_3');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_documents');
    }
};
