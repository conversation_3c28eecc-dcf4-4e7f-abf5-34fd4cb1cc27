<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if ( !Schema::hasTable('master_health_concerns') ) {
            Schema::create('master_health_concerns', function (Blueprint $table) {
                $table->id();
                $table->jsonb('name');
                $table->integer('sequence')->default(0);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_health_concerns');
    }
};
