<?php

use App\Models\PaymentGatewayLog;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_gateway_logs', function (Blueprint $table) {
            $table->string('requested_by_type')->nullable();
        });

        PaymentGatewayLog::query()->update([
            'requested_by_type' => User::class,
        ]);

        Schema::table('payment_gateway_logs', function (Blueprint $table) {
            $table->string('requested_by_type')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_gateway_logs', function (Blueprint $table) {
            $table->dropColumn('requested_by_type');
        });
    }
};
