<?php

use App\Models\UnpaidItemAssignmentStudent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unpaid_item_assignment_students', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('unpaid_item_assignment_id');
            $table->unsignedInteger('student_id');
            $table->enum('status', [UnpaidItemAssignmentStudent::STATUS_NEW, UnpaidItemAssignmentStudent::STATUS_COMPLETED, UnpaidItemAssignmentStudent::STATUS_ERROR]);
            $table->dateTime('applied_at')->nullable();
            $table->text('remarks')->nullable();
            $table->timestamps();

            $table->index(['unpaid_item_assignment_id', 'student_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unpaid_item_assignment_students');
    }
};
