<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_society_positions', function (Blueprint $table) {
            $table->id();
            $table->jsonb('name');
            $table->string('code')->unique();
            $table->boolean('is_active'); // [ACTIVE / INACTIVE]
            $table->timestamps();

            $table->index(['code']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_society_positions');
    }
};
