<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_input', function (Blueprint $table) {
            $table->id();
            $table->morphs('attendance_recordable');
            $table->foreignId('card_id')->nullable();
            $table->text('remarks')->nullable();
            $table->date('date');
            $table->datetime('record_datetime');
            $table->boolean('is_manual')->default(false);
            $table->foreignId('updated_by_employee_id')->nullable();
            $table->foreignId('terminal_id')->nullable();

            $table->timestamps();

            $table->index(['date', 'attendance_recordable_type', 'attendance_recordable_id'], 'idx_attendance_input_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_input');
    }
};
