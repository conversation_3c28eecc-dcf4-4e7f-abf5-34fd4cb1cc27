<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leadership_position_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('semester_setting_id')->index();
            $table->foreignId('semester_class_id')->index();
            $table->foreignId('student_id')->index();
            $table->foreignId('leadership_position_id')->index();
            $table->foreignId('created_by')->index();
            $table->timestamps();

            $table->index(['semester_setting_id', 'semester_class_id'], 'leadership_position_records_idx');
            $table->index(['student_id', 'semester_setting_id'], 'leadership_position_records_idx_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leadership_position_records');
    }
};
