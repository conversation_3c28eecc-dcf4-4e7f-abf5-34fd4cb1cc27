<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ecommerce_product_sub_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_category_id');
            $table->string('name');
            $table->integer('sequence')->default(0);
            $table->timestamps();

            $table->index(['product_category_id', 'name'], 'ecommerce_product_sub_categories_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ecommerce_product_sub_categories');
    }
};
