<?php

use App\Models\EnrollmentSession;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('enrollment_sessions', 'admission_grade_id')) {
            return;
        }

        Schema::table('enrollment_sessions', function (Blueprint $table) {
            $table->foreignId('admission_grade_id')->nullable();
        });

        EnrollmentSession::query()->update(['admission_grade_id' => 1]);

        Schema::table('enrollment_sessions', function (Blueprint $table) {
            $table->foreignId('admission_grade_id')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('enrollment_sessions', function (Blueprint $table) {
            $table->dropColumn('admission_grade_id');
        });
    }
};
