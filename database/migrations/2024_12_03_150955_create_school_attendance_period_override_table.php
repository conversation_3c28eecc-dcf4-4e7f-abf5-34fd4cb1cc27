<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('school_attendance_period_override', function (Blueprint $table) {
            $table->id();
            $table->date('from');
            $table->date('to');
            $table->string('remarks')->nullable();
            $table->integer('priority');
            $table->time('attendance_from')->nullable();
            $table->time('attendance_to')->nullable();
            $table->timestamps();

            $table->index(['from', 'to'], 'idex_sapo_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_attendance_period_override');
    }
};
