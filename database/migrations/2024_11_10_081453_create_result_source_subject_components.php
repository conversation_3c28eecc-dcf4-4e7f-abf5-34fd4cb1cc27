<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('result_source_subject_components', function (Blueprint $table) {
            $table->id();
            $table->foreignId('result_source_subject_id');
            $table->string('code', 32);
            $table->jsonb('name');
            $table->decimal('weightage_percent', 5,2);
            $table->decimal('actual_score', 5, 2)->nullable();
            $table->foreignId('data_entry_employee_id')->nullable();
            $table->dateTime('data_entry_at')->nullable();
            $table->timestamps();

            $table->index(['result_source_subject_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('result_source_subject_components');
    }
};
