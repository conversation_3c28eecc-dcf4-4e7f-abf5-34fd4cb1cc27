<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('counselling_case_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id');
            $table->foreignId('created_by_employee_id'); // refer to Employee
            $table->datetime('visit_datetime');
            $table->text('note');
            $table->string('status'); // DRAFT, POSTED
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('counselling_case_records');
    }
};
