<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reward_punishment_records', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->foreignId('student_id')->index();
            $table->foreignId('reward_punishment_id')->index();
            $table->decimal('average_exam_marks');
            $table->decimal('conduct_marks');
            $table->boolean('display_in_report_card');
            $table->string('status');
            $table->datetime('notification_sent_at')->nullable();
            $table->timestamps();

            $table->index(['student_id', 'status', 'date', 'average_exam_marks'], 'idx_rpr_report_card_1');
            $table->index(['student_id', 'status', 'date', 'display_in_report_card'], 'idx_rpr_report_card_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reward_punishment_records');
    }
};
