<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('library_payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('member_id');
            $table->unsignedBigInteger('book_loan_id');
            $table->string('payment_method');
            $table->decimal('payment_amount');
            $table->date('payment_date');
            $table->string('description')->nullable();
            $table->unsignedBigInteger('wallet_transaction_id')->nullable();
            $table->timestamps();

            $table->index([
                'member_id',
                'book_loan_id',
                'payment_method',
                'payment_date',
            ], 'library_payment_transactions_index');

            $table->index(['wallet_transaction_id']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('library_payment_transactions');
    }
};
