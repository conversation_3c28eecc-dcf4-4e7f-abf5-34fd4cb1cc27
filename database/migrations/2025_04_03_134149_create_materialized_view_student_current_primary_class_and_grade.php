<?php

use App\Helpers\DatabaseHelper;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE MATERIALIZED VIEW current_students_class_and_grade_view AS
                SELECT students.id AS student_id, students.name AS student_name, students.student_number AS student_number, classes.id AS class_id,
                    classes.name AS class_name, classes.code AS class_code, master_grades.id AS grade_id, master_grades.name AS grade_name, class_type
                FROM
                    student_classes
                INNER JOIN students ON students.id = student_classes.student_id
                INNER JOIN semester_classes ON semester_classes.id = student_classes.semester_class_id
                INNER JOIN classes ON classes.id = semester_classes.class_id
                LEFT JOIN master_grades ON master_grades.id = classes.grade_id
                WHERE
                    student_classes.is_active = true
                AND
                    student_classes.semester_setting_id = (SELECT id FROM master_semester_settings WHERE is_current_semester = true)
                ORDER BY student_id ASC
        ");



        DatabaseHelper::createUniqueIndex('current_students_class_and_grade_view', 'idx_cscagv_unique_1', ['student_id', 'class_id']);
        //DatabaseHelper::createIndex('current_students_class_and_grade_view', 'idx_cscagv_1', ['student_id']);
        DatabaseHelper::createIndex('current_students_class_and_grade_view', 'idx_cscagv_2', ['class_id']);
        DatabaseHelper::createIndex('current_students_class_and_grade_view', 'idx_cscagv_3', ['grade_id']);
        DatabaseHelper::createIndex('current_students_class_and_grade_view', 'idx_cscagv_4', ['student_id', 'class_type']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP MATERIALIZED VIEW IF EXISTS current_students_class_and_grade_view;");
    }
};
