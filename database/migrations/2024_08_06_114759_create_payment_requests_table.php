<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_requests', function (Blueprint $table) {
            $table->id();
            $table->string('userable_type');
            $table->unsignedInteger('userable_id');
            $table->unsignedInteger('billing_document_id');
            $table->unsignedInteger('payment_method_id');
            $table->string('status', 16)->comment('pending, processing, approved, posted, voided');
            $table->decimal('amount', 12, 2);
            $table->unsignedInteger('bank_id')->nullable();
            $table->string('payment_reference_no', 64)->nullable();
            $table->text('proof_of_payment_url')->nullable();
            $table->dateTime('processed_at')->nullable();
            $table->foreignId('processed_by_employee_id')->nullable();
            $table->dateTime('approved_at')->nullable();
            $table->foreignId('approved_by_employee_id')->nullable();
            $table->dateTime('posted_at')->nullable();
            $table->foreignId('posted_by_employee_id')->nullable();
            $table->timestamps();

            $table->index(['userable_type', 'userable_id']);
            $table->index(['status', 'processed_by_employee_id', 'approved_by_employee_id', 'posted_by_employee_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_requests');
    }
};
