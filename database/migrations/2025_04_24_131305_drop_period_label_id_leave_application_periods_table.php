<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leave_application_periods', function (Blueprint $table) {
            if (Schema::hasColumn('leave_application_periods', 'period_label_id')) {
                $table->dropColumn('period_label_id');
            }
        });

        Schema::table('period_labels', function (Blueprint $table) {
            if (!Schema::hasColumn('period_labels', 'can_apply_leave')) {
                $table->boolean('can_apply_leave')->default(true)->index();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leave_application_periods', function (Blueprint $table) {
            //
        });
    }
};
