<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('classes', function (Blueprint $table) {
            if (!Schema::hasColumn('classes', 'stream')) {
                $table->string('stream', 16)->default(\App\Enums\ClassStream::NOT_APPLICABLE->value);
            }

            if (Schema::hasColumn('classes', 'stream')) {
                $table->string('stream', 16)->default(\App\Enums\ClassStream::NOT_APPLICABLE->value)->change();
            }
        });

        foreach (\App\Models\ClassModel::all() as $class) {
            if (str_contains($class->getTranslation('name', 'zh'), '理')) {
                $class->stream = \App\Enums\ClassStream::SCIENCE;
                $class->save();
            } else {
                if (str_contains($class->getTranslation('name', 'zh'), '文商')) {
                    $class->stream = \App\Enums\ClassStream::COMMERCE;
                    $class->save();
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('classes', function (Blueprint $table) {
            $table->dropColumn('stream');
        });
    }
};
