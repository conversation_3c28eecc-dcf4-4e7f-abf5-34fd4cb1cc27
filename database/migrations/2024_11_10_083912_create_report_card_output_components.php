<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_card_output_components', function (Blueprint $table) {
            $table->id();
            $table->foreignId('report_card_output_id');
            $table->string('code', 32);
            $table->jsonb('name');
            $table->foreignId('subject_id')->nullable();        // if this component is a subject's score.
            $table->foreignId('grading_scheme_id')->nullable();     // if a grading scheme is required to convert marks to grade.
            $table->text('total_formula')->nullable();
            $table->text('label_formula')->nullable();
            $table->integer('resolve_priority')->default(0);
            $table->boolean('calculate_rank')->default(false);
            $table->decimal('weightage_multiplier', 7, 4)->nullable();
            $table->timestamps();

            $table->index(['report_card_output_id', 'code']);
            $table->index(['subject_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_card_output_components');
    }
};
