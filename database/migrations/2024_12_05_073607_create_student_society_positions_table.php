<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_society_positions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id');
            $table->foreignId('semester_class_id');
            $table->foreignId('society_position_id');
            $table->timestamps();

            $table->index(['student_id']);
            $table->index(['semester_class_id']);
            $table->index(['society_position_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_society_positions');
    }
};
