<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE MATERIALIZED VIEW hostel_bed_by_year_views AS
            SELECT
                DISTINCT ON (year, assignable_type, assignable_id)
                EXTRACT('Year' FROM start_date) as year, 
                assignable_type,
                assignable_id,
                hostel_room_bed_id
            FROM hostel_bed_assignments
            ORDER BY year DESC, assignable_type DESC, assignable_id DESC, start_date DESC
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP MATERIALIZED VIEW hostel_bed_by_year_views');
    }
};
