<?php

use App\Enums\PeriodAttendanceStatus;
use App\Models\Timeslot;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('timeslots', function (Blueprint $table) {
            if (!Schema::hasColumn('timeslots', 'default_init_status')) {
                $table->string('default_init_status')->default(PeriodAttendanceStatus::ABSENT->value);
            }
        });

        Schema::table('leave_application_types', function (Blueprint $table) {
            if (!Schema::hasColumn('leave_application_types', 'code')) {
                $table->string('code', 16)->unique()->nullable();
            }
        });

        DB::statement("UPDATE leave_application_types SET code = id::text");

        $timeslots = Timeslot::with('period')->get();
        foreach ($timeslots as $timeslot) {
            if ($timeslot->period->period == 15) {
                $timeslot->default_init_status = 'PRESENT';
                $timeslot->save();
            }
        }

        // Schema::table('leave_application_types', function (Blueprint $table) {
        //     $table->string('code', 16)->nullable(false)->change();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('timeslots', function (Blueprint $table) {
            //
        });
    }
};
