<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leave_application_periods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('leave_application_id');
            $table->date('date');
            $table->foreignId('period_label_id');
            $table->unsignedInteger('period');
            $table->timestamps();

            $table->index(['leave_application_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leave_application_periods');
    }
};
