<?php

use App\Models\DiscountSetting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discount_settings', function (Blueprint $table) {
            $table->id();
            $table->enum('basis', [DiscountSetting::BASIS_PERCENT, DiscountSetting::BASIS_FIXED_AMOUNT]);
            $table->decimal('basis_amount', 12, 2);
            $table->decimal('max_amount', 12, 2)->nullable();
            $table->decimal('used_amount', 12, 2)->default(0)->comment('Amount of discount already used');
            $table->date('effective_from');
            $table->date('effective_to');
            $table->jsonb('gl_account_codes')->default(json_encode([]));
            $table->string('source_type')->nullable();
            $table->unsignedInteger('source_id')->nullable();
            $table->string('userable_type');
            $table->unsignedInteger('userable_id');
            $table->boolean('is_active');

            $table->timestamps();

            $table->index(['source_type', 'source_id']);
            $table->index(['userable_type', 'userable_id', 'is_active', 'effective_from', 'effective_to'], 'idx_get_applicable_discounts');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discount_settings');
    }
};
