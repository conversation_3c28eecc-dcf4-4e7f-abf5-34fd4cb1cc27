<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('results_posting_line_items_history', function (Blueprint $table) {
            $table->text('label')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('results_posting_line_items_history', function (Blueprint $table) {
            $table->string('label, 32')->nullable()->change();
        });
    }
};
