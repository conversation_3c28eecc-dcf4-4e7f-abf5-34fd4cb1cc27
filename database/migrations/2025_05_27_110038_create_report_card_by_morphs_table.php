<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_card_by_morphs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('results_posting_header_id');
            $table->morphs('report_card_morphable');
            $table->foreignId('semester_setting_id');
            $table->text('file_url');
            $table->boolean('is_active');
            $table->dateTime('file_generated_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_card_by_morphs');
    }
};
