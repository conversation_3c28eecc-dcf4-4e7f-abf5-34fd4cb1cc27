<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if ( !Schema::hasTable('pending_student_employee_status_changes') ) {
            Schema::create('pending_student_employee_status_changes', function (Blueprint $table) {
                $table->id();
                $table->string('type');
                $table->date('execution_date');
                $table->json('data');
                $table->string('status');
                $table->string('error_message')->nullable();
                $table->morphs('status_changeable');
                $table->timestamps();

                $table->index(['execution_date', 'status', 'type'], 'idx_psesc_1');
                $table->index(['status', 'type', 'status_changeable_id', 'status_changeable_type'], 'idx_psesc_2');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pending_student_employee_status_changes');
    }
};
