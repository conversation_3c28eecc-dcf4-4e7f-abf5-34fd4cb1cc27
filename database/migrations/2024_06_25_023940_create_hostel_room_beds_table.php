<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hostel_room_beds', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hostel_room_id');
            $table->string('name');
            $table->boolean('is_active');
            $table->string('status');
            $table->timestamps();

            $table->index(['hostel_room_id']);
            $table->index(['hostel_room_id', 'name'], 'idx_hostel_room_beds_validation_1');
            $table->index(['name', 'is_active', 'status'], 'idx_hostel_room_beds_1');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hostel_room_beds');
    }
};
