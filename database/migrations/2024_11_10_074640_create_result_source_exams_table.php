<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('result_source_exams', function (Blueprint $table) {
            $table->id();
            $table->foreignId('result_source_id');
            $table->foreignId('exam_id');
            $table->timestamps();

            $table->index(['result_source_id']);
            $table->index(['exam_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('result_source_exams');
    }
};
