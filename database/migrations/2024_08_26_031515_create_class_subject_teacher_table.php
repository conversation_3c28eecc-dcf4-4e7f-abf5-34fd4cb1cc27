<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     */
    public function up(): void
    {
        Schema::create('class_subject_teacher', function (Blueprint $table) {
            $table->foreignId('class_subject_id'); // [ref: > class_subjects.id]
            $table->foreignId('employee_id'); // [ref: > employees.id]
            $table->string('type'); // PRIMARY, SECONDARY

            $table->index(['class_subject_id']);
            $table->index(['employee_id', 'type']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('class_subject_teacher');
    }
};
