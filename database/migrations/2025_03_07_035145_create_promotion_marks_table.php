<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotion_marks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('semester_class_id')->index();
            $table->decimal('net_average_for_promotion', 7, 2);
            $table->decimal('conduct_mark_for_promotion', 7, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotion_marks');
    }
};
