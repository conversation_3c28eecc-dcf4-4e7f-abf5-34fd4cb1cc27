<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ecommerce_order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('merchant_id');
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('product_id');
            $table->unsignedInteger('quantity');
            $table->string('product_name');
            $table->string('product_description')->nullable();
            $table->decimal('product_unit_price');
            $table->string('currency_code');
            $table->decimal('amount_before_tax');       // no need amount after tax at line item level
            $table->decimal('tax_amount', 12, 2);
            $table->decimal('tax_percentage', 5, 2);
            $table->unsignedBigInteger('tax_id')->nullable();
            $table->date('product_delivery_date')->nullable();
            $table->timestamps();

            $table->index(['merchant_id'], 'ecommerce_order_items_idx_1');
            $table->index(['order_id'], 'ecommerce_order_items_idx_2');
            $table->index(['product_id', 'product_name'], 'ecommerce_order_items_idx_3');
            $table->index(['tax_id'], 'ecommerce_order_items_idx_4');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ecommerce_order_items');
    }
};
