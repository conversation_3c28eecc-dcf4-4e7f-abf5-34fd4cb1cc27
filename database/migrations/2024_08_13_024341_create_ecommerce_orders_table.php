<?php

use App\Enums\EcommerceOrderPaymentStatus;
use App\Enums\EcommerceOrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ecommerce_orders', function (Blueprint $table) {
            $table->id();
            $table->string('merchant_type');
            $table->string('order_reference_number');
            $table->string('buyer_userable_type');          // the recipient/profile that the purchase is for (e.g. parent buy for student)
            $table->unsignedBigInteger('buyer_userable_id');
            $table->unsignedBigInteger('recipient_student_class_id')->nullable();
            $table->dateTime('cancel_before_datetime');
            $table->string('status')->default(EcommerceOrderStatus::NEW);
            $table->string('payment_status')->default(EcommerceOrderPaymentStatus::PENDING);
            $table->string('currency_code');
            $table->decimal('amount_before_tax');
            $table->decimal('amount_after_tax');
            $table->decimal('tax_amount')->default(0);
            $table->jsonb('tax_breakdown')->nullable();
            $table->unsignedInteger('user_id');     // the actual user that did the purchase (e.g. parent buy for student)
            $table->timestamps();

            $table->index(['order_reference_number'], 'ecommerce_orders_idx_1');
            $table->index(['buyer_userable_type', 'buyer_userable_id', 'status', 'payment_status'], 'ecommerce_orders_idx_2');
            $table->index(['user_id'], 'ecommerce_orders_idx_3');
            $table->index(['merchant_type', 'status', 'cancel_before_datetime'], 'ecommerce_orders_idx_4');
        });

        $index_columns = ['order_reference_number'];

        foreach ($index_columns as $column) {
            DB::statement("CREATE INDEX ecommerce_order_{$column}_gin_idx ON ecommerce_orders USING gin ({$column} gin_trgm_ops)");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ecommerce_orders');
    }
};
