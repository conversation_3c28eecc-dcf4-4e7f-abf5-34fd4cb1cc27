<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('timeslot_override', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id');
            $table->date('date');
            $table->unsignedInteger('period');
            $table->string('placeholder')->nullable();
            $table->time('attendance_from')->nullable();
            $table->time('attendance_to')->nullable();
            $table->foreignId('employee_id')->nullable();
            $table->boolean('inherit_from_school_attendance');
            $table->boolean('class_attendance_required');
            $table->boolean('is_empty');
            $table->timestamps();

            $table->index(['student_id', 'date'], 'idx_timeslot_override_1');
            $table->index(['date', 'employee_id', 'class_attendance_required', 'is_empty'], 'idx_timeslot_override_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('timeslot_override');
    }
};
