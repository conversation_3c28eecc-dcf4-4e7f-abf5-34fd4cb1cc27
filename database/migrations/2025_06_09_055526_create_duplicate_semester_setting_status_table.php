<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('duplicate_semester_setting_status', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_semester_setting_id');
            $table->foreignId('to_semester_setting_id');
            $table->string('batch_id');
            $table->string('status');
            $table->string('error_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('duplicate_semester_setting_status');
    }
};
