<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('results_posting_headers', function (Blueprint $table) {
            $table->id();
            $table->string('code', 32);
            $table->string('report_card_output_code', 32);
            $table->string('report_card_template_service')->nullable();
            $table->foreignId('grade_id');      // posting is by grade (in a semester) and output code.
            $table->foreignId('semester_setting_id');
            $table->string('status', 32);
            $table->dateTime('processing_start')->nullable();
            $table->dateTime('processing_end')->nullable();
            $table->jsonb('student_ids');
            $table->foreignId('posted_by_employee_id');
            $table->dateTime('posted_at');
            $table->jsonb('metadata')->nullable();      // key value pair only
            $table->jsonb('errors')->nullable();
            $table->timestamps();

            $table->index(['code']);
            $table->index(['status']);
            $table->index(['posted_at']);
            $table->index(['grade_id', 'semester_setting_id', 'status'], 'idx_rph_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('results_posting_headers');
    }
};
