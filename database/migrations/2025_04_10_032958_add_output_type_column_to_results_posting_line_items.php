<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('results_posting_line_items', function (Blueprint $table) {
            $table->string('output_type')->nullable();
        });

        Schema::table('results_posting_line_items_history', function (Blueprint $table) {
            $table->string('output_type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('results_posting_line_items', function (Blueprint $table) {
            $table->dropColumn('output_type');
        });

        Schema::table('results_posting_line_items_history', function (Blueprint $table) {
            $table->dropColumn('output_type');
        });
    }
};
