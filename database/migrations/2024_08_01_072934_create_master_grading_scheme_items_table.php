<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     *
     */
    public function up(): void
    {
        Schema::create('master_grading_scheme_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('grading_scheme_id'); // ref : > master_grading_schemes.id
            $table->string('name');
            $table->string('display_as_name');
            $table->decimal('from', 6, 2);
            $table->decimal('to', 6, 2);
            $table->decimal('extra_marks')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_grading_scheme_items');
    }
};
