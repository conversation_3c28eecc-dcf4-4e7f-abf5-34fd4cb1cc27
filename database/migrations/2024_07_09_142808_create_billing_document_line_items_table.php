<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_document_line_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('billing_document_id');
            $table->text('description');
            $table->unsignedInteger('product_id')->nullable();
            $table->string('billable_item_type')->nullable()->comment("Refers to things that can be billed, e.g. fees");
            $table->unsignedInteger('billable_item_id')->nullable();
            $table->string('currency_code', 4);
            $table->string('uom_code', 8);
            $table->decimal('unit_price', 12, 2);
            $table->decimal('quantity', 12, 2);
            $table->decimal('amount_before_tax', 12, 2);
            $table->unsignedInteger('offset_billing_document_id')->nullable();
            $table->string('gl_account_code', 16);
            $table->boolean('is_discount')->default(false);
            $table->unsignedInteger('discount_id')->nullable();
            $table->unsignedInteger('discount_original_line_item_id')->nullable();

            $table->timestamps();

            $table->index(['billing_document_id']);
            $table->index(['product_id']);
            $table->index(['offset_billing_document_id']);
            $table->index(['billable_item_type', 'billable_item_id', 'gl_account_code']);
            $table->index(['discount_id']);
            $table->index(['is_discount', 'discount_original_line_item_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_document_line_items');
    }
};
