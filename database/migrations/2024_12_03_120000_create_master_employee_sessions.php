<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_employee_sessions', function (Blueprint $table) {
            $table->id();
            $table->jsonb('name');
            $table->boolean('is_active')->default(true);
            $table->boolean('determine_attendance_status')->default(false);
            $table->timestamps();

            $table->index(['is_active']);
            $table->index(['determine_attendance_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_employee_sessions');
    }
};
