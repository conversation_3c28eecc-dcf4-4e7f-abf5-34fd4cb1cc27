<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('result_source_subjects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('result_source_id');
            $table->foreignId('subject_id');
            $table->string('grading_type', 24);
            $table->decimal('weightage_multiplier', 7, 4)->nullable();
            $table->boolean('is_exempted')->default(false);

            $table->decimal('actual_score', 5, 2)->nullable();      // score e.g. 50%, 100%. this is computed from component level.
            $table->string('actual_score_grade', 32)->nullable();       // score in label form e.g. A, B, C, D+ (to be data entry by employee)

            $table->foreignId('data_entry_employee_id')->nullable();
            $table->dateTime('data_entry_at')->nullable();

            $table->string('data_entry_status');
            $table->dateTime('posted_at')->nullable();

            $table->timestamps();

            $table->index(['result_source_id', 'subject_id'], 'idx_results_compilation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('result_source_subjects');
    }
};
