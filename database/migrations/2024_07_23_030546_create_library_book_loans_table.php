<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('library_book_loans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('member_id');
            $table->foreignId('book_id');
            $table->date('loan_date');
            $table->date('due_date');
            $table->date('return_date')->nullable();
            $table->date('lost_date')->nullable();
            $table->decimal('penalty_overdue_amount')->default(0);
            $table->decimal('penalty_lost_amount')->default(0);
            $table->decimal('penalty_total_fine_amount')->default(0);
            $table->decimal('penalty_paid_amount')->default(0);
            $table->string('loan_status')->nullable();
            $table->string('penalty_payment_status');
            $table->string('remarks')->nullable();
            $table->timestamps();

            $table->index([
                'member_id',
                'book_id',
                'penalty_payment_status',
                'loan_status'
            ], 'library_book_loans_index');

            $table->index([
                'due_date',
                'loan_status',
            ], 'idx_overdue_book_loans');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('library_book_loans');
    }
};
