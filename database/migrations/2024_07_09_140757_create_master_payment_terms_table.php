<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_payment_terms', function (Blueprint $table) {
            $table->id();
            $table->string('code', 16);
            $table->jsonb('name');
            $table->boolean('is_active');
            $table->unsignedInteger('due_date_days');
            $table->timestamps();

            $table->index(['code']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_payment_terms');
    }
};
