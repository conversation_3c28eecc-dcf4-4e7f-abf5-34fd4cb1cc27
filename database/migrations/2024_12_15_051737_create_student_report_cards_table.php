<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_report_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('results_posting_header_id');
            $table->foreignId('student_id');
            $table->foreignId('grade_id');
            $table->foreignId('semester_setting_id');
            $table->foreignId('semester_class_id');
            $table->text('file_url');
            $table->dateTime('file_generated_at');
            $table->boolean('is_visible_to_student')->default(false);
            $table->timestamps();

            $table->index(['student_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_report_cards');
    }
};
