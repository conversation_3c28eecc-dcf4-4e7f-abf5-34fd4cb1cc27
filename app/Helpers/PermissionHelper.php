<?php

namespace App\Helpers;

use App\Interfaces\PermissionDependencies;
use App\Models\Merchant;

class PermissionHelper implements PermissionDependencies
{
    /** Put all the permissions that can be accessed by dependency permission
     *  Example: If updating student requires master-race-view and master-religion-view, then the permission will be
     *  master-race-view|master-religion-view|student-update
     */

    public static function getAvailablePermissionsFor(array $route_permission): array
    {
        if (empty($route_permission)) {
            return [];
        }

        // If $route_permission = 'master-race-view', it will return `master-race-view|student-update|student-create`
        // if $route_permission = 'master-race-view', get all the array that contain 'master-race-view', then return their keys
        return
            collect(PermissionDependencies::DEPENDENCIES)->filter(function ($permissions) use ($route_permission) {
                return (bool) array_intersect($route_permission, $permissions);
            })
                ->keys()
                ->merge($route_permission)
                ->unique()
                ->all();
    }

    public static function setMerchantIdByPermission(&$input): array
    {
        if (auth()->user()->cannot('view-all-merchant')) {
            $merchant = Merchant::where('user_id', auth()->user()->id)->first();

            // If no merchant, throw error instead
            if (!isset($merchant)) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 4008);
            }

            $input['merchant_id'] = $merchant->id;
        }

        return $input;
    }
}
