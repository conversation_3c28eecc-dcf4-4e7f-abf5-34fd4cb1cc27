<?php

namespace App\Services\Report;

use App\Enums\ClassType;
use App\Enums\LibraryMemberType;
use App\Enums\LibraryReportSchoolRateBorrowBookFilterBy;
use App\Enums\LibraryReportTopBorrowedBookFilterBy;
use App\Enums\LibraryReportTopBorrowerFilterBy;
use App\Helpers\ErrorCodeHelper;
use App\Models\Student;
use App\Repositories\BookLanguageRepository;
use App\Repositories\BookRepository;
use App\Repositories\LibraryBookLoanRepository;
use App\Repositories\SemesterClassRepository;
use App\Repositories\SemesterSettingRepository;
use App\Repositories\StudentRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;

class LibraryBookLoanReportService
{
    public function __construct(
        protected LibraryBookLoanRepository $bookLoanRepository,
        protected StudentRepository $studentRepository,
        protected BookLanguageRepository $bookLanguageRepository,
        protected BookRepository $bookRepository,
        protected SemesterClassRepository $semesterClassRepository,
        protected SemesterSettingRepository $semesterSettingRepository,
    ) {

    }

    public function getReportByBookLoanData(array $filters = []): array
    {
        $filters['includes'] = [
            'member.userable' => function (MorphTo $morph_to) use ($filters) {
                $morph_to->morphWith([
                    Student::class => [
                        'classes' => function ($query) use ($filters) {
                            $query->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                                $query->where('semester_class_id', $filters['semester_class_id'])
                                    ->where('is_active', true);
                            });
                        },
                        'primaryClass.semesterClass.classModel'
                    ]
                ]);
            }
        ];

        $book_loans = $this->bookLoanRepository->getAll($filters);

        $data = [];

        foreach ($book_loans as $book_loan) {
            $class_name = Arr::get($book_loan, 'member.userable.primaryClass.semesterClass.classModel.name');

            $data[] = [
                'member_number' => $book_loan->member->member_number,
                'name' => $book_loan->member->getFormattedTranslations('name'),
                'class_name' => $class_name,
                'call_no' => $book_loan->book->call_no,
                'title' => $book_loan->book->title,
                'loan_date' => $book_loan->loan_date->toDateString(),
                'due_date' => $book_loan->due_date->toDateString(),
                'return_date' => $book_loan->return_date?->toDateString(),
                'balance' => number_format($book_loan->penalty_total_fine_amount ?: 0, 2),
                'loan_status' => $book_loan->loan_status,
            ];
        }

        //sort data
        return collect($data)->sortBy($filters['order_by'])->values()->toArray();
    }

    public function getReportTopBorrowers(array $filters = []): array
    {
        if (isset($filters['period_loan_date_from'])) {
            $filters['library_book_loan_date_from'] = $filters['period_loan_date_from'];
        }

        if (isset($filters['period_loan_date_to'])) {
            $filters['library_book_loan_date_to'] = $filters['period_loan_date_to'];
        }

        $filters['includes'] = [
            'latestPrimaryClass.semesterClass.classModel',
            'libraryMember.bookLoans' => function ($query) use ($filters) {
                $query->when(isset($filters['period_loan_date_to']), function (Builder $query) use ($filters) {
                    $query->where('loan_date', '<=', $filters['period_loan_date_to']);
                })->when(isset($filters['period_loan_date_from']), function (Builder $query) use ($filters) {
                    $query->where('loan_date', '>=', $filters['period_loan_date_from']);
                });
            },
            'libraryMember.bookLoans.book',
        ];

        $students = $this->studentRepository->getAll($filters);

        $book_languages = $this->getBookLanguagesWithOthers();

        $data = [];

        foreach ($students as $student) {
            $class_name = Arr::get($student, 'latestPrimaryClass.semesterClass.classModel.name');

            if (!isset($data[$student->id])) {
                $data[$student->id] = [
                    'student_name' => $student->getFormattedTranslations('name'),
                    'student_number' => $student->student_number,
                    'class_name' => $class_name,
                    'total_book_loans' => 0,
                    'book_languages' => [],
                ];

                foreach ($book_languages as $book_language) {
                    $data[$student->id]['book_languages'][$book_language['id']] = [
                        'name' => $book_language['name'],
                        'total_book_loans' => 0
                    ];
                }
            }

            foreach ($student->libraryMember->bookLoans as $book_loan) {
                $book_language_id = Arr::get($book_loan, 'book.book_language_id', -1);
                $data[$student->id]['total_book_loans']++;
                $data[$student->id]['book_languages'][$book_language_id]['total_book_loans']++;
            }

            // Reindex book_languages
            $data[$student->id]['book_languages'] = array_values($data[$student->id]['book_languages']);
        }

        $data = collect($data)->sortByDesc('total_book_loans')->values()->toArray();

//        $order_by = Arr::get($filters, 'order_by');
//
//        if ($order_by) {
//            if (Arr::get($filters, 'order_direction') == 'DESC') {
//                $data = collect($data)->sortByDesc('total_book_loans')->values()->toArray();
//            } else {
//                $data = collect($data)->sortBy($order_by)->values()->toArray();
//            }
//        }

        $filter_by = Arr::get($filters, 'filter_by');

        if ($filter_by == LibraryReportTopBorrowerFilterBy::GRADE->value) {
            $data = collect($data)->skip(0)->take(10)->values()->toArray();
        }

        return [
            'students' => array_values($data),
            'book_languages' => $book_languages,
        ];
    }

    public function getReportTopBorrowedBooks(array $filters = []): Collection
    {
        if ($filters['filter_by'] == LibraryReportTopBorrowedBookFilterBy::MONTHLY->value) {
            $filters['period_loan_date_from'] = Carbon::createFromDate($filters['year'], $filters['month'], 1)->startOfMonth()->toDateString();
            $filters['period_loan_date_to'] = Carbon::createFromDate($filters['year'], $filters['month'], 1)->endOfMonth()->toDateString();
        }

        if ($filters['filter_by'] == LibraryReportTopBorrowedBookFilterBy::YEARLY->value) {
            $filters['period_loan_date_from'] = Carbon::createFromDate($filters['year'], 1, 1)->startOfYear()->toDateString();
            $filters['period_loan_date_to'] = Carbon::createFromDate($filters['year'], 1, 1)->endOfYear()->toDateString();
        }

        $filters['book_loan_exist'] = true;
        $filters['includes'] = [
            'bookClassification'
        ];

        return $this->bookRepository->orderByTotalBookLoan($filters);
    }

    public function getReportSchoolRateBorrowBook(array $filters = []): array
    {
        if ($filters['filter_type'] == LibraryReportSchoolRateBorrowBookFilterBy::MONTHLY->value) {
            $semester_year = $this->getSemesterYear($filters['semester_setting_id'], $filters['month']);
            $filters['period_loan_date_from'] = Carbon::createFromDate($semester_year, $filters['month'], 1)->startOfMonth()->toDateString();
            $filters['period_loan_date_to'] = Carbon::createFromDate($semester_year, $filters['month'], 1)->endOfMonth()->toDateString();
        }

        $filters['member_type'] = LibraryMemberType::STUDENT->value;

        $filters['includes'] = [
            'member' => function ($query) use ($filters) {
                $query->select(['id', 'userable_id', 'userable_type']);
                $query->with([
                    'userable' => function ($query) use ($filters) {
                        $query->select(['id']);
                        $query->with([
                            'latestPrimaryClass' => function ($query) use ($filters) {
                                $query->select('id', 'student_id', 'class_type', 'is_active', 'semester_class_id')
                                    ->where('is_latest_class_in_semester', true)
                                    ->where('semester_setting_id', $filters['semester_setting_id'])
                                    ->with([
                                        'semesterClass' => function ($query) {
                                            $query->select('id');
                                        },
                                    ]);
                            },
                        ]);
                    },
                ]);
            },
        ];

        $book_loans = $this->bookLoanRepository->getAll($filters);

        $book_languages = $this->getBookLanguagesWithOthers();

        // active primary semester classes
        $semester_class_filters = [
            'is_active' => true,
            'includes' => ['classModel.grade'],
            'semester_setting_id' => $filters['semester_setting_id'],
            'class_type' => ClassType::PRIMARY,
        ];

        $semester_classes = $this->semesterClassRepository->getAll($semester_class_filters);

        $data = [];

        foreach ($semester_classes as $semester_class) {
            $semester_class_id = $semester_class->id;
            $class_name = $semester_class->classModel->name;

            $data[$semester_class_id] = [
                'class_name' => $class_name,
                'grade_sequence' => $semester_class->classModel->grade?->sequence,
                'book_languages' => [],
                'total_book_loans' => 0
            ];
        }

        foreach ($data as $semester_class_id => $d) {
            foreach ($book_languages as $book_language) {
                $data[$semester_class_id]['book_languages'][$book_language['id']] = [
                    'name' => $book_language['name'],
                    'total_book_loans' => 0
                ];
            }
        }

        foreach ($book_loans as $book_loan) {
            $book_language_id = Arr::get($book_loan, 'book.book_language_id', -1);
            $book_loan_semester_class_id = Arr::get($book_loan, 'member.userable.latestPrimaryClass.semesterClass.id');
            // invalid semester class, skip
            if (!isset($book_loan_semester_class_id) || !isset($data[$book_loan_semester_class_id])) {
                continue;
            }
            $data[$book_loan_semester_class_id]['total_book_loans']++;
            $data[$book_loan_semester_class_id]['book_languages'][$book_language_id]['total_book_loans']++;
        }

        // Reindex book_languages for each semester class
        foreach ($data as $semester_class_id => $semester_class_data) {
            $data[$semester_class_id]['book_languages'] = array_values($data[$semester_class_id]['book_languages']);
        }

        $data = collect($data)
            ->sortBy([
                ['grade_sequence', 'desc'],
                ['class_name', 'asc'],
            ])
            ->values()
            ->toArray();

        return [
            'classes' => $data,
            'book_languages' => $book_languages,
        ];
    }

    public function getSemesterYear($semester_setting_id, $month): string
    {
        $semester = $this->semesterSettingRepository->find($semester_setting_id);

        $start_date = Carbon::parse($semester->from)->startOfMonth()->toDateString();
        $end_date = Carbon::parse($semester->to)->endOfMonth()->toDateString();

        $carbon_period = CarbonPeriod::create(Carbon::parse($start_date), '1 month', Carbon::parse($end_date));

        foreach ($carbon_period as $period) {
            if ($period->format('n') == $month) {
                return $period->format('Y');
            }
        }

        ErrorCodeHelper::throwError(ErrorCodeHelper::REPORT_ERROR, 30001);
    }

    public function getReportBookBorrowRecord(array $filters = []): array
    {
        $filters['period_loan_date_from'] = Carbon::parse($filters['year'] . '-01-01')->startOfYear()->toDateString();
        $filters['period_loan_date_to'] = Carbon::parse($filters['year'] . '-01-01')->endOfYear()->toDateString();

        $filters['member_type'] = LibraryMemberType::STUDENT->value;

        $filters['includes'] = [
            'member.userable.primaryClass.semesterClass.classModel',
        ];

        $book = $this->bookRepository->find($filters['book_id']);

        $book_loans = $this->bookLoanRepository->getAll($filters);

        $data = [
            'book_title' => $book->title,
            'book_number' => $book->book_no,
            'book_call_number' => $book->call_no,
            'book_loans' => []
        ];

        foreach ($book_loans as $book_loan) {
            $class_name = Arr::get($book_loan->member->userable, 'primaryClass.semesterClass.classModel.name');

            $data['book_loans'][] = [
                'member_number' => $book_loan->member->member_number,
                'member_name' => $book_loan->member->getFormattedTranslations('name'),
                'class_name' => $class_name,
                'loan_date' => $book_loan->loan_date,
                'due_date' => $book_loan->due_date,
                'return_date' => $book_loan->return_date,
                'balance' => $book_loan->penalty_total_fine_amount
            ];
        }

        return $data;
    }

    protected function getBookLanguagesWithOthers(): array
    {
        $book_languages = $this->bookLanguageRepository->getAll()->select('id', 'name')->toArray();

        $book_languages[] = [
            'id' => -1,
            'name' => __('general.others')
        ];

        return $book_languages;
    }
}
