<?php

namespace App\Services\Billing;

use App\Enums\PaymentStatus;
use App\Exceptions\BillingLogicRestrictionException;
use App\Models\BillingDocument;
use App\Repositories\AdvanceTransactionRepository;
use App\Repositories\BillingDocumentRepository;

class InvoiceService extends BillingDocumentService
{
    protected AdvanceTransactionRepository $advanceTransactionRepository;

    public function __construct(BillingDocumentRepository $billingDocumentRepository, AdvancePaymentService $advancePaymentService, AdvanceTransactionRepository $advanceTransactionRepository)
    {
        parent::__construct($billingDocumentRepository, $advancePaymentService);
        $this->advanceTransactionRepository = $advanceTransactionRepository;
    }

    public function changeStatusTo($new_status)
    {

        \DB::transaction(function () use (&$new_status) {

            parent::changeStatusTo($new_status);

            if ($new_status === BillingDocument::STATUS_VOIDED) {

                // get all used advance transactions, reverse.
                $transactions = $this->advanceTransactionRepository->getAll([
                    'used_in_invoice_id' => $this->billingDocument->id,
                    'order_by' => ['id' => 'desc'],
                ]);

                foreach ($transactions as $transaction) {

                    $this->advanceTransactionRepository->create([
                        'billable_type' => $transaction->billable_type,
                        'billable_id' => $transaction->billable_id,
                        'advance_invoice_id' => $transaction->advance_invoice_id,
                        'amount_before_tax' => abs($transaction->amount_before_tax),
                        'currency_code' => $transaction->currency_code,
                        'gl_account_code' => $transaction->gl_account_code,
                        'used_in_invoice_id' => $transaction->used_in_invoice_id,
                    ]);

                }

                // Reverse discount used_amount
                $this->refundDiscountedAmount();

            }

        });


    }

    public function validateStatusChange($new_status)
    {
        parent::validateStatusChange($new_status);

        if ($new_status === BillingDocument::STATUS_VOIDED && $this->billingDocument->payment_status === BillingDocument::PAYMENT_STATUS_PAID) {
            throw new BillingLogicRestrictionException('Unable to cancel a paid billing document.');
        }

        if ($this->billingDocument->paymentGatewayLogs()->where('status', PaymentStatus::PENDING)->exists()) {
            throw new BillingLogicRestrictionException(__('system_error.36011'));
        }

        return true;
    }


    public function refundDiscountedAmount()
    {

        $line_items_with_discount = $this->billingDocument->lineItems->where('is_discount', true)->values();

        foreach ($line_items_with_discount as $line_item) {

            $discount_setting = $line_item->discount;
            $discount_setting->decreaseUsedAmount(abs($line_item->amount_before_tax));

        }

        return $this;
    }


}
