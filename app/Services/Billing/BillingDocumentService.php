<?php

namespace App\Services\Billing;

use App\Enums\PaymentStatus;
use App\Events\InvoicePaidEvent;
use App\Events\InvoiceVoidedEvent;
use App\Exceptions\BillingLogicRestrictionException;
use App\Helpers\ErrorCodeHelper;
use App\Helpers\SystemHelper;
use App\Interfaces\Billable;
use App\Models\BankAccount;
use App\Models\BillingDocument;
use App\Models\BillingDocumentAdvanceTransaction;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\LegalEntity;
use App\Models\PaymentTerm;
use App\Models\Tax;
use App\Models\Uom;
use App\Models\User;
use App\Repositories\BillingDocumentRepository;
use App\Services\DocumentRunningNumberService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class BillingDocumentService
{

    const UNPAID_ITEM_LOCK_RELEASE_IN_SECONDS = 60;

    protected BillingDocumentRepository $billingDocumentRepository;
    protected Collection $lineItems;
    protected Collection $advanceOffsetTransactions;
    protected ?Billable $billToParty;
    protected float $amountBeforeTax;
    protected float $taxAmount;
    protected float $amountAfterTax;
    protected float $lessAdvanceAmount;
    protected float $amountBeforeTaxLessAdvance;

    protected ?Tax $tax;
    protected ?LegalEntity $legalEntity;
    protected ?PaymentTerm $paymentTerm;
    protected ?BankAccount $remitToBankAccount;
    protected ?BillingDocument $billingDocument;

    protected ?string $type;
    protected ?string $subType;
    protected Carbon $documentDate;
    protected string $status;
    protected ?string $referenceNo;
    protected Carbon $paymentDueDate;
    protected ?string $currency;
    protected AdvancePaymentService $advancePaymentService;
    protected array $eligibleDiscounts;

    protected ?User $user;
    protected Collection $preGeneratedReferenceNumbers;

    public function __construct(
        BillingDocumentRepository $billingDocumentRepository,
        AdvancePaymentService $advancePaymentService,
    ) {
        $this->billingDocumentRepository = $billingDocumentRepository;
        $this->advancePaymentService = $advancePaymentService;

        $this->lineItems = collect([]);
        $this->advanceOffsetTransactions = collect([]);
        $this->isSystemAction = false;
        $this->preGeneratedReferenceNumbers = collect([]);
    }

    public function init(): BillingDocumentService
    {
        $this->status = BillingDocument::STATUS_DRAFT;
        $this->documentDate = now(config('school.timezone'))->startOfDay();
        $this->referenceNo = null;
        $this->type = null;
        $this->subType = null;
        $this->legalEntity = null;
        $this->billToParty = null;
        $this->tax = null;
        $this->paymentDueDate = now(config('school.timezone'))->startOfDay();
        $this->paymentTerm = null;
        $this->currency = null;
        $this->remitToBankAccount = null;
        $this->amountBeforeTax = 0;
        $this->amountAfterTax = 0;
        $this->taxAmount = 0;
        $this->amountBeforeTaxLessAdvance = 0;
        $this->lessAdvanceAmount = 0;
        $this->billingDocument = null;
        $this->eligibleDiscounts = [];
        $this->user = null;
        return $this;
    }

    public function getAllPaginatedBillingDocuments($filters = []): LengthAwarePaginator
    {
        return $this->billingDocumentRepository->getAllPaginated($filters);
    }

    public function getAllBillingDocuments($filters = []): Collection
    {
        return $this->billingDocumentRepository->getAll($filters);
    }

    public function setDefaultValuesForWalletTopup()
    {

        $this->setDocumentDate(Carbon::now(config('school.timezone'))->startOfDay());
        $this->setLegalEntity(SystemHelper::getDefaultLegalEntity());
        $this->setPaymentTerm(SystemHelper::getDefaultPaymentTerm());
        $this->setPaymentDueDate(Carbon::now(config('school.timezone'))->addDays($this->getPaymentTerm()->due_date_days));
        return $this;

    }

    public function setDefaultValuesForPayingUnpaidItems()
    {

        $this->setDocumentDate(Carbon::now(config('school.timezone'))->startOfDay());
        $this->setLegalEntity(SystemHelper::getDefaultLegalEntity());
        $this->setPaymentTerm(SystemHelper::getDefaultPaymentTerm());
        $this->setPaymentDueDate(Carbon::now(config('school.timezone'))->addDays($this->getPaymentTerm()->due_date_days));
        return $this;
    }

    public function setDefaultValuesForManualPayment()
    {
        $this->setDocumentDate(Carbon::now(config('school.timezone'))->startOfDay());
        $this->setLegalEntity(SystemHelper::getDefaultLegalEntity());
        $this->setPaymentTerm(SystemHelper::getDefaultPaymentTerm());
        $this->setPaymentDueDate(Carbon::now(config('school.timezone'))->addDays($this->getPaymentTerm()->due_date_days));
        return $this;
    }

    public function setDefaultValuesForEnrollmentExamFee()
    {

        $this->setDocumentDate(Carbon::now(config('school.timezone'))->startOfDay());
        $this->setLegalEntity(SystemHelper::getDefaultLegalEntity());
        $this->setPaymentTerm(SystemHelper::getDefaultPaymentTerm());
        $this->setPaymentDueDate(Carbon::now(config('school.timezone'))->addDays($this->getPaymentTerm()->due_date_days));
        return $this;

    }

    public function setDefaultValuesForECommerceOrders()
    {
        $this->setDocumentDate(Carbon::now(config('school.timezone'))->startOfDay());
        $this->setLegalEntity(SystemHelper::getDefaultLegalEntity());
        $this->setPaymentTerm(SystemHelper::getDefaultPaymentTerm());
        $this->setPaymentDueDate(Carbon::now(config('school.timezone')));
        return $this;
    }

    public function addLineItem(BillingDocumentLineItem $line_item): BillingDocumentService
    {
        $this->lineItems->push($line_item);
        return $this;
    }

    public function addAdvanceOffsetTransaction(BillingDocumentAdvanceTransaction $transaction): BillingDocumentService
    {
        $this->advanceOffsetTransactions->push($transaction);
        return $this;
    }

    public function calculateAmountBeforeTax(): BillingDocumentService
    {

        $this->amountBeforeTax = 0;

        foreach ($this->lineItems as $line_item) {
            if ($line_item->offset_billing_document_id === null) {
                $this->amountBeforeTax = bcadd($this->amountBeforeTax, $line_item->amount_before_tax, 2);
            }
        }

        // less advance amount cannot be more than amount before tax
        if (bccomp($this->lessAdvanceAmount, $this->amountBeforeTax, 2) === 1) {
            throw new BillingLogicRestrictionException('Less advance amount cannot be more than amount before tax.');
        }

        $this->amountBeforeTaxLessAdvance = bcsub($this->amountBeforeTax, $this->lessAdvanceAmount, 2);

        // in the event amountBeforeTaxLessAdvance < 0 (shouldn't happen), set to 0
        if ($this->amountBeforeTaxLessAdvance < 0) {
            $this->amountBeforeTaxLessAdvance = 0;
        }

        $this->taxAmount = 0;
        $this->amountAfterTax = $this->amountBeforeTaxLessAdvance;

        return $this;

    }

    public function calculateLessAdvanceAmount(): BillingDocumentService
    {

        $total = 0;

        foreach ($this->advanceOffsetTransactions as $transaction) {
            $total = bcadd($total, abs($transaction->amount_before_tax), 2);
        }

        $this->lessAdvanceAmount = $total;
        return $this;

    }

    public function applyTax(Tax $tax)
    {

        $this->tax = $tax;
        $this->taxAmount = round($this->amountBeforeTaxLessAdvance * $tax->percentage / 100, 2);
        $this->amountAfterTax = bcadd($this->amountBeforeTaxLessAdvance, $this->taxAmount, 2);

        return $this;

    }

    public function preGenerateReferenceNumbers($how_many)
    {

        for ($i = 0; $i < $how_many; $i++) {
            $this->preGeneratedReferenceNumbers->push($this->actuallyGenerateReferenceNumber());
        }

        return $this;

    }

    public function generateReferenceNumber()
    {
        // if there are any pre-generated reference numbers, use them first FIFO.
        if ($this->preGeneratedReferenceNumbers->count() > 0) {
            $this->referenceNo = $this->preGeneratedReferenceNumbers->shift();
            return $this;
        }

        // else, generate a new reference number.
        $this->referenceNo = $this->actuallyGenerateReferenceNumber();
        return $this;

    }

    private function actuallyGenerateReferenceNumber()
    {

        if (!isset(BillingDocument::DOCUMENT_NUMBER_PREFIX[$this->type])) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36007);
        }

        return app(DocumentRunningNumberService::class)
            ->setDocumentType(BillingDocument::class)
            ->setYear($this->documentDate->year)
            ->setIdentifier1(BillingDocument::DOCUMENT_NUMBER_PREFIX[$this->type])
            ->addCustomComponent(BillingDocument::DOCUMENT_NUMBER_PREFIX[$this->type])
            ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_YEAR)
            ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
            ->generate();
    }

    public function calculatePaymentDueDate()
    {
        $this->paymentDueDate = $this->documentDate->clone()->addDays($this->paymentTerm->due_date_days);
        return $this;
    }

    public function recalculateAndUpdateAfterDiscount()
    {

        $this->calculateAmountBeforeTax();

        DB::transaction(function () {
            $this->billingDocument->amount_before_tax = $this->amountBeforeTax;
            $this->billingDocument->amount_before_tax_after_less_advance = $this->amountBeforeTaxLessAdvance;
            $this->applyTax($this->tax);
            $this->billingDocument->tax_amount = $this->taxAmount;
            $this->billingDocument->amount_after_tax = $this->amountAfterTax;

            $this->billingDocument->save();

            // apply billing_document_id to line items & create line items
            foreach ($this->lineItems->whereNull('id')->values() as $line_item) {
                $line_item->billing_document_id = $this->billingDocument->id;
                $line_item->save();
            }

            // save all discount settings
            $this->createEligibleDiscounts();

        });


        return $this;

    }

    public function createEligibleDiscounts()
    {

        // save all discount settings
        foreach ($this->eligibleDiscounts as $eligible_discount) {
            $discount_setting = $eligible_discount['discount_setting'];
            $discount_setting->save();
        }
        return $this;

    }

    public function create()
    {

        DB::transaction(function () {

            $this->billingDocument = $this->billingDocumentRepository->create([
                'type' => $this->type,
                'sub_type' => $this->subType,
                'classification' => BillingDocument::CLASSIFICATION_MAPPING[$this->type],
                'document_date' => $this->documentDate->toDateString(),
                'status' => $this->status,
                'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
                'paid_at' => null,
                'reference_no' => $this->referenceNo,
                'legal_entity_id' => $this->legalEntity->id,
                'legal_entity_name' => $this->legalEntity->name,
                'legal_entity_address' => $this->legalEntity->address,
                'bill_to_name' => $this->billToParty->getBillToName(),
                'bill_to_address' => $this->billToParty->getBillToAddress(),
                'bill_to_type' => $this->billToParty->getBillToType(),
                'bill_to_id' => $this->billToParty->getBillToId(),
                'bill_to_reference_number' => $this->billToParty->getBillToReferenceNumber(),
                'tax_code' => $this->tax->code,
                'tax_description' => $this->tax->name,
                'tax_percentage' => $this->tax->percentage,
                'payment_due_date' => $this->paymentDueDate->toDateString(),
                'payment_term_id' => $this->paymentTerm->id,
                'remit_to_id' => isset($this->remitToBankAccount) ? $this->remitToBankAccount->id : null,
                'remit_to_account_number' => isset($this->remitToBankAccount) ? $this->remitToBankAccount->account_number : null,
                'remit_to_account_name' => isset($this->remitToBankAccount) ? $this->remitToBankAccount->account_name : null,
                'remit_to_bank_address' => isset($this->remitToBankAccount) ? $this->remitToBankAccount->address : null,
                'remit_to_bank_name' => isset($this->remitToBankAccount) ? $this->remitToBankAccount->bank->name : null,
                'remit_to_swift_code' => isset($this->remitToBankAccount) ? $this->remitToBankAccount->bank->swift_code : null,
                'currency_code' => $this->currency,
                'amount_before_tax' => $this->amountBeforeTax,
                'amount_before_tax_after_less_advance' => $this->amountBeforeTaxLessAdvance,
                'tax_amount' => $this->taxAmount,
                'amount_after_tax' => $this->amountAfterTax,
            ]);

            $this->createLineItems();
            $this->createAdvanceOffsetTransactions();

        }, 3);

        return $this;

    }

    public function createAdvanceOffsetTransactions()
    {

        // apply billing_document_id to advance transactions
        if ($this->advanceOffsetTransactions->count() > 0) {
            $this->advancePaymentService
                ->setApplyToInvoice($this->billingDocument)
                ->create();
        }

        return $this;

    }

    public function updateBillingDocument()
    {
        $this->billingDocument->save();
        return $this;
    }

    public function createLineItems()
    {

        // apply billing_document_id to line items & create line items
        foreach ($this->lineItems as $line_item) {

            // discard useless fields
            unset($line_item->balance_discountable_amount_before_tax);

            $line_item->billing_document_id = $this->billingDocument->id;
            $line_item->save();
        }

        return $this;

    }

    public function applyAdvanceOffset()
    {

        if (!$this->canApplyAdvance()) {
            return $this;
        }

        // less advance amount cannot be more than amount before tax
        if (!isset($this->billToParty)) {
            throw new BillingLogicRestrictionException('Please specify Bill to party before apply advance offset.');
        }

        $this->advancePaymentService
            ->init()
            ->setBillable($this->billToParty)
            ->getEligibleAdvancesForCurrency($this->currency);

        foreach ($this->lineItems->groupBy('gl_account_code') as $gl_account_code => $line_items) {
            $amount_before_tax = bcadd($line_items->sum('amount_before_tax'), 0, 2);
            $this->advancePaymentService->useAdvanceUptoAmount($amount_before_tax, $gl_account_code);
        }

        $transactions = $this->advancePaymentService
            ->generateTransactionsForAdvanceUsage()
            ->getAdvanceTransactions();

        foreach ($transactions as $transaction) {

            $this->advanceOffsetTransactions->push($transaction);

            $line_item_service = app()->make(BillingDocumentLineItemService::class);
            $new_line_item = $line_item_service->init()
                ->setDescription(__('billing.advance_offset_line_item', ['gl_account_label' => $transaction['target_gl_account_label'], 'advance_invoice_number' => $transaction['advance_invoice']->reference_no, 'advance_invoice_date' => $transaction['advance_invoice']->document_date]))
                ->setCurrency($transaction['currency_code'])
                ->setUom(Uom::CODE_DEFAULT)
                ->setUnitPrice(0)
                ->setQuantity(0)
                ->setAmountBeforeTax($transaction['amount_before_tax'])
                ->setOffsetBillingDocumentId($transaction['advance_invoice_id'])
                ->setGlAccountCode($transaction['target_gl_account_code'])
                ->make();

            $this->lineItems->push($new_line_item);
        }

        $this->lessAdvanceAmount = $this->advancePaymentService->getAdvanceToBeUsedAmount();

        // recalculate amount before tax with less advance
        $this->calculateAmountBeforeTax();

        return $this;

    }

    public function canApplyAdvance()
    {
        return $this->type === BillingDocument::TYPE_INVOICE;
    }

    public function changeStatusTo($new_status)
    {

        if ($new_status === $this->billingDocument->status) {
            return $this;
        }

        $this->validateStatusChange($new_status);

        $this->billingDocument->status = $new_status;

        if ($new_status === BillingDocument::STATUS_POSTED) {
            $this->billingDocument->posting_date = now(config('school.timezone'))->toDateString();
        } else {
            if ($new_status === BillingDocument::STATUS_VOIDED) {
                InvoiceVoidedEvent::dispatch($this->billingDocument);
            }
        }

        $this->billingDocument->save();

        return $this;

    }

    public function validateStatusChange($new_status)
    {
        if (!isset($this->billingDocument)) {
            throw new BillingLogicRestrictionException('Please specify a billing document to change status.');
        }
        if ($this->billingDocument->status === BillingDocument::STATUS_VOIDED) {
            throw new BillingLogicRestrictionException('Unable to change status for a voided billing document.');
        }
        if ($new_status === BillingDocument::STATUS_DRAFT && $this->billingDocument->status === BillingDocument::STATUS_POSTED) {
            throw new BillingLogicRestrictionException('Unable to change from Posted to Draft status.');
        }
        if ($new_status === BillingDocument::STATUS_POSTED && $this->billingDocument->status === BillingDocument::STATUS_DRAFT) {
            throw new BillingLogicRestrictionException('Unable to change from Draft to Posted status.');
        }

        return true;

    }

    public function changePaymentStatusTo(string $new_payment_status, Carbon $payment_date)
    {

        if (!isset($this->billingDocument)) {
            throw new BillingLogicRestrictionException('Please specify a billing document to change status.');
        }

        if ($new_payment_status === $this->billingDocument->payment_status) {
            return $this;
        }

        $this->billingDocument->payment_status = $new_payment_status;
        $this->billingDocument->paid_at = $payment_date->tz('UTC');
        $this->billingDocument->save();

        if ($this->billingDocument->payment_status === BillingDocument::PAYMENT_STATUS_PAID) {

            if ($this->billingDocument->isRequirePayment() && !$this->billingDocument->hasPayment()) {
                throw new BillingLogicRestrictionException('Billing document requires at least 1 payment to be marked as paid.');
            }

            InvoicePaidEvent::dispatch($this->billingDocument);
        }

        return $this;

    }

    public function calculateEligibleDiscounts(DiscountSetting $specific_discount = null)
    {

        if (!isset($this->billToParty)) {
            throw new BillingLogicRestrictionException('Please specify Bill to party before applying discount.');
        }

        $apply_dates = $this->determineApplyDates();

        if ($specific_discount !== null) {
            $discounts = app()->make(DiscountSettingService::class)->getAvailableDiscountsForUserable($this->billToParty, $apply_dates->toArray(), [$specific_discount->id]);
        } else {
            // get all eligible discounts grouped by apply date and gl accounts
            $discounts = app()->make(DiscountSettingService::class)->getAvailableDiscountsForUserable($this->billToParty, $apply_dates->toArray());
        }

        // foreach line item, determine can apply discount or not
        // if can, add a new line item to minus the discounted value
        $default_date = $this->documentDate->toDateString();

        $this->eligibleDiscounts = [];

        foreach ($this->getToBePaidLineItems() as $line_item) {

            $apply_date = $line_item->getApplyDateWithDefaultValue($default_date);
            $gl_account_code = $line_item->gl_account_code;
            $balance_amount_to_be_deducted = $line_item->balance_discountable_amount_before_tax;

            if (isset($discounts[$apply_date][$gl_account_code])) {

                foreach ($discounts[$apply_date][$gl_account_code] as $discount_setting) {

                    $discount_amount = $discount_setting->calculateDiscountAmount($balance_amount_to_be_deducted);
                    $final_discount_amount = 0;

                    if (bccomp($discount_amount, $balance_amount_to_be_deducted, 2) === 1) {
                        $final_discount_amount = $balance_amount_to_be_deducted;
                        $balance_amount_to_be_deducted = 0;
                    } else {
                        $final_discount_amount = $discount_amount;
                        $balance_amount_to_be_deducted = bcsub($balance_amount_to_be_deducted, $discount_amount, 2);
                    }

                    if (bccomp($final_discount_amount, 0, 2) === 1) {
                        $this->eligibleDiscounts[] = [
                            'original_line_item' => $line_item,
                            'discount_setting' => $discount_setting,
                            'amount_before_tax' => $final_discount_amount,
                        ];
                    }

                }
            }
        }

        return $this;
    }

    public function addDiscountLineItems()
    {

        foreach ($this->eligibleDiscounts as $eligible_discount) {

            $original_line_item = $eligible_discount['original_line_item'];
            $amount_before_tax = $eligible_discount['amount_before_tax'];
            $discount_setting = $eligible_discount['discount_setting'];

            $line_item_service = app()->make(BillingDocumentLineItemService::class);
            $new_line_item = $line_item_service->init()
                ->setDescription(__('billing.discount_line_item', ['description' => $original_line_item->description, 'discount_source' => $discount_setting->getSourceDescription()]))
                ->setCurrency($original_line_item->currency_code)
                ->setUom(Uom::CODE_DEFAULT)
                ->setUnitPrice(0)
                ->setQuantity(0)
                ->setAmountBeforeTax(-abs($amount_before_tax))
                ->setGlAccountCode($original_line_item->gl_account_code)
                ->setIsDiscount(true)
                ->setDiscount($discount_setting)
                ->setDiscountOriginalLineItem($original_line_item)
                ->make();

            $this->lineItems->push($new_line_item);

        }

        return $this;

    }

    public function determineApplyDates(): Collection
    {

        $apply_dates = collect([]);

        // determine apply dates from line item billableitem
        // else, use document date as apply date
        // exclude less adv and discount line items
        foreach ($this->getToBePaidLineItems() as $line_item) {
            $default_date = $this->documentDate->toDateString();
            $apply_dates->push($line_item->getApplyDateWithDefaultValue($default_date));
        }

        return $apply_dates->unique()->sort()->values();

    }

    public function getToBePaidLineItems()
    {
        // existing discounts
        $existing_discounts = [];

        foreach ($this->lineItems as $line_item) {
            if ($line_item->is_discount && $line_item->discount_original_line_item_id !== null) {
                if (!isset($existing_discounts[$line_item->discount_original_line_item_id])) {
                    $existing_discounts[$line_item->discount_original_line_item_id] = 0;
                }

                $existing_discounts[$line_item->discount_original_line_item_id] += abs($line_item->amount_before_tax);
            }
        }

        // exclude advance line items and discounts.
        $to_be_paid_line_items = $this->lineItems->filter(function ($val) {
            return $val->amount_before_tax > 0 && $val->offset_billing_document_id === null && !$val->is_discount;
        })->sortBy('id')->values();

        foreach ($to_be_paid_line_items as &$line_item) {
            if (isset($existing_discounts[$line_item->id])) {
                $line_item->balance_discountable_amount_before_tax = bcsub($line_item->amount_before_tax, $existing_discounts[$line_item->id], 2);
            } else {
                $line_item->balance_discountable_amount_before_tax = $line_item->amount_before_tax;
            }
        }

        return $to_be_paid_line_items;

    }

    public function requestPaymentLink(array $payload = []): Model
    {
        if (!isset($this->billingDocument) && !isset($this->user)) {
            throw new Exception('Billing document or user is not set.');
        }

        $this->validateRequestPaymentLink();

        /** @var AccountingService $accounting_service */
        $accounting_service = app()->make(AccountingService::class);

        return $accounting_service
            ->setUser($this->getUser())
            ->setBillingDocument($this->getBillingDocument())
            ->setReturnUrl($payload['return_url'] ?? null)
            ->generatePaymentGatewayLogWithPaymentLink();
    }

    public function validateRequestPaymentLink(): void
    {
        /** @var BillingDocument $billing_document */
        $billing_document = $this->getBillingDocument();

        /**
         * throw error if $billing_document->status !== CONFIRMED && $billing_document->payment_status !== UNPAID
         */
        if (!$billing_document->isValidStatusForPayment()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::BILLING_DOCUMENT_ERROR, 37001);
        }

        /**
         * throw error if $billing_document->amount_after_tax is between RM0.01 and RM0.99
         */

        if (bccomp($billing_document->amount_after_tax, '0.01', 2) >= 0 && bccomp($billing_document->amount_after_tax, 1, 2) === -1) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36010);
        }

        /**
         * throw error if $billing_document has PaymentGatewayLog with status PENDING
         */

        if ($billing_document->paymentGatewayLogs()->where('status', PaymentStatus::PENDING)->exists()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ACCOUNTING_ERROR, 36011);
        }
    }

    public function getBillToParty(): Billable
    {
        return $this->billToParty;
    }

    public function setBillToParty(Billable $billToParty): BillingDocumentService
    {
        $this->billToParty = $billToParty;
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): BillingDocumentService
    {
        $this->user = $user;
        return $this;
    }

    public function getTax(): Tax
    {
        return $this->tax;
    }

    public function setTax(Tax $tax): BillingDocumentService
    {
        $this->tax = $tax;
        return $this;
    }

    public function getPaymentTerm(): PaymentTerm
    {
        return $this->paymentTerm;
    }

    public function setPaymentTerm(PaymentTerm $paymentTerm): BillingDocumentService
    {
        $this->paymentTerm = $paymentTerm;
        return $this;
    }

    public function getBillingDocument(): ?BillingDocument
    {
        return $this->billingDocument;
    }

    public function setBillingDocument(?BillingDocument $billingDocument): BillingDocumentService
    {
        $this->billingDocument = $billingDocument;
        $this->documentDate = Carbon::parse($this->billingDocument->document_date);
        $this->tax = $this->billingDocument->tax;
        $this->billToParty = $this->billingDocument->billTo;
        $this->lineItems = $this->billingDocument->lineItems;
        $this->currency = $this->billingDocument->currency_code;
        $this->amountBeforeTax = $this->billingDocument->amount_before_tax;
        $this->amountBeforeTaxLessAdvance = $this->billingDocument->amount_before_tax_after_less_advance;
        $this->taxAmount = $this->billingDocument->tax_amount;
        $this->amountAfterTax = $this->billingDocument->amount_after_tax;
        $this->lessAdvanceAmount = abs($this->amountBeforeTax - $this->amountBeforeTaxLessAdvance);

        return $this;
    }

    public function getRemitToBankAccount(): ?BankAccount
    {
        return $this->remitToBankAccount;
    }

    public function setRemitToBankAccount(?BankAccount $remitToBankAccount): BillingDocumentService
    {
        $this->remitToBankAccount = $remitToBankAccount;
        return $this;
    }

    public function getAmountBeforeTaxLessAdvance(): float
    {
        return $this->amountBeforeTaxLessAdvance;
    }

    public function setAmountBeforeTaxLessAdvance(float $amountBeforeTaxLessAdvance): BillingDocumentService
    {
        $this->amountBeforeTaxLessAdvance = $amountBeforeTaxLessAdvance;
        return $this;
    }

    public function getLegalEntity(): LegalEntity
    {
        return $this->legalEntity;
    }

    public function setLegalEntity(LegalEntity $legalEntity): BillingDocumentService
    {
        $this->legalEntity = $legalEntity;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): BillingDocumentService
    {
        $this->type = $type;
        return $this;
    }

    public function getSubType(): string
    {
        return $this->subType;
    }

    public function setSubType(string $subType): BillingDocumentService
    {
        $this->subType = $subType;
        return $this;
    }

    public function getDocumentDate(): Carbon
    {
        return $this->documentDate;
    }

    public function setDocumentDate(Carbon $documentDate): BillingDocumentService
    {
        $this->documentDate = $documentDate;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): BillingDocumentService
    {
        $this->status = $status;
        return $this;
    }

    public function getReferenceNo(): ?string
    {
        return $this->referenceNo;
    }

    public function setReferenceNo(string $referenceNo): BillingDocumentService
    {
        $this->referenceNo = $referenceNo;
        return $this;
    }

    public function getPaymentDueDate(): Carbon
    {
        return $this->paymentDueDate;
    }

    public function setPaymentDueDate(Carbon $paymentDueDate): BillingDocumentService
    {
        $this->paymentDueDate = $paymentDueDate;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): BillingDocumentService
    {
        $this->currency = $currency;
        return $this;
    }

    public function getLineItems(): Collection
    {
        return $this->lineItems;
    }

    public function setLineItems(Collection $lineItems): BillingDocumentService
    {
        $this->lineItems = $lineItems;
        return $this;
    }


    public function getAdvanceOffsetTransactions(): Collection
    {
        return $this->advanceOffsetTransactions;
    }

    public function getAmountBeforeTax(): float
    {
        return $this->amountBeforeTax;
    }

    public function getTaxAmount(): float
    {
        return $this->taxAmount;
    }

    public function getAmountAfterTax(): float
    {
        return $this->amountAfterTax;
    }

    public function getLessAdvanceAmount(): float
    {
        return $this->lessAdvanceAmount;
    }

    public function getEligibleDiscounts(): array
    {
        return $this->eligibleDiscounts;
    }

    public function getPreGeneratedReferenceNumbers(): Collection
    {
        return $this->preGeneratedReferenceNumbers;
    }

    public function setPreGeneratedReferenceNumbers(Collection $pre_generated_reference_numbers): BillingDocumentService
    {
        $this->preGeneratedReferenceNumbers = $pre_generated_reference_numbers;
        return $this;
    }


}
