<?php

namespace App\Services\Reports;

use App\Enums\EnrollmentPaymentStatus;
use App\Enums\Gender;
use App\Exports\EnrollmentsByDailyCollectionExport;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\ConfigHelper;
use App\Models\Enrollment;
use App\Repositories\BillingDocumentRepository;
use App\Repositories\EnrollmentRepository;
use App\Services\BaseReportService;
use App\Services\ReportPrintService;
use DateInterval;
use DateTime;

class EnrollmentReportService extends BaseReportService
{
    public function __construct(
        protected BillingDocumentRepository $billingDocumentRepository,
        protected BillingDocumentReportService $billingDocumentReportService,
        protected EnrollmentRepository $enrollmentRepository,
        protected ReportPrintService $reportPrintService,
    ) {}

    const array EXAM_BANDS = [
        'Under-50' => [
            'from' => 0,
            'to' => 49.99
        ],
        '50-54.99' => [
            'from' => 50,
            'to' => 54.99
        ],
        '55-59.99' => [
            'from' => 55,
            'to' => 59.99
        ],
        '60-64.99' => [
            'from' => 60,
            'to' => 64.99
        ],
        '65-69.99' => [
            'from' => 65,
            'to' => 69.99
        ],
        '70-74.99' => [
            'from' => 70,
            'to' => 74.99
        ],
        '75-79.99' => [
            'from' => 75,
            'to' => 79.99
        ],
        '80-Above' => [
            'from' => 80,
            'to' => 100,
        ]
    ];


    public function getRegisteredStudentReport(array $filters): array
    {
        $data = Enrollment::select('id', 'gender', 'is_hostel', 'payment_date')
            ->withAvg('enrollmentExams', 'total_average')
            ->whereBetween('payment_date', [$filters['from_date'], $filters['to_date']])
            ->where('payment_status', EnrollmentPaymentStatus::PAID->value)
            ->get();

        $data = $data->groupBy('payment_date');

        $total = [
            'total_students' => 0,
            'male_hostel_students' => 0,
            'female_hostel_students' => 0
        ];

        foreach ($this::EXAM_BANDS as $exam_key => $exam_band){
            $total['mark_counts'][$exam_key] = 0;
        }

        $data->transform(function ($data, $date) use (&$total) {
            $total_students = $data->count();
            $male_hostel_students = $data->where('gender', Gender::MALE)->where('is_hostel', true)->count();
            $female_hostel_students = $data->where('gender', Gender::FEMALE)->where('is_hostel', true)->count();

            $mark_counts = [];
            foreach ($this::EXAM_BANDS as $exam_key => $exam_band) {
                $mark_count = $data->whereNotNull('enrollment_exams_avg_total_average')
                    ->whereBetween('enrollment_exams_avg_total_average', [$exam_band['from'], $exam_band['to']])->count();
                $mark_counts[$exam_key] = $mark_count;
                $total['mark_counts'][$exam_key] += $mark_count;
            }

            $total['total_students'] += $total_students;
            $total['male_hostel_students'] += $male_hostel_students;
            $total['female_hostel_students'] += $female_hostel_students;

            return [
                'date' => $date,
                'total_students' => $total_students,
                'male_hostel_students' => $male_hostel_students,
                'female_hostel_students' => $female_hostel_students,
                'mark_counts' => $mark_counts
            ];
        });

        $data = $data->sortBy('date');

        $report_data = [
            'year' => DateTime::createFromFormat("Y-m-d", $filters['from_date'])->add(new DateInterval("P1Y"))->format('Y'),
            'data' => $data,
            'locales' => ConfigHelper::getAvailableLocales(),
            'exam_bands' => $this::EXAM_BANDS,
            'total' => $total
        ];

        if (!$this->getExportType()) {
            return $report_data;
        }

        $report_view = view($this->getReportViewName(), $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($this->getReportViewName())
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    public function getDailyCollectionReportData(array $filters = []): mixed
    {
        $data = $this->billingDocumentRepository->getPaidEnrollmentInvoiceReportData($filters);

        $mapped_data = $this->billingDocumentReportService->mapForDailyCollectionReport($data, []); // always empty product_ids

        $report_data = [
            'data' => $mapped_data['data'],
            'products' => $mapped_data['products'],
            'payment_methods' => $mapped_data['payment_methods'],
            'total_for_products' => $mapped_data['total_for_products'],
            'summary_amount_for_products' => $mapped_data['summary_amount_for_products'],
            'summary_amount_for_bank_charges' => $mapped_data['summary_amount_for_bank_charges'],
            'payment_date_from' => $filters['payment_date_from'],
            'payment_date_to' => $filters['payment_date_to'],
        ];

        // only for EXCEL
        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new EnrollmentsByDailyCollectionExport($report_data));

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }
}
