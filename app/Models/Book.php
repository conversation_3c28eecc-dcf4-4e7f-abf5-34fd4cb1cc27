<?php

namespace App\Models;

use App\Enums\BookBinding;
use App\Enums\BookCondition;
use App\Enums\BookStatus;
use App\Enums\LibraryBookLoanStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use OwenIt\Auditing\Contracts\Auditable;

class Book extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    protected $casts = [
        'published_date' => 'date',
        'binding' => BookBinding::class,
        'cdrom' => 'boolean',
        'status' => BookStatus::class,
        'condition' => BookCondition::class,
    ];

    public function authors(): BelongsToMany
    {
        return $this->belongsToMany(Author::class);
    }

    public function bookClassification(): BelongsTo
    {
        return $this->belongsTo(BookClassification::class);
    }

    public function bookSubClassification(): BelongsTo
    {
        return $this->belongsTo(BookSubClassification::class, 'book_sub_classification_id');
    }

    public function bookCategory(): BelongsTo
    {
        return $this->belongsTo(BookCategory::class);
    }

    public function bookSource(): BelongsTo
    {
        return $this->belongsTo(BookSource::class);
    }

    public function loanSettings(): HasMany
    {
        return $this->hasMany(BookLoanSetting::class);
    }

    public function bookLoans(): HasMany
    {
        return $this->hasMany(LibraryBookLoan::class);
    }

    public function activeBookLoan(): HasOne
    {
        return $this->hasOne(LibraryBookLoan::class)->where('loan_status', LibraryBookLoanStatus::BORROWED);
    }

    public function bookLanguage(): BelongsTo
    {
        return $this->belongsTo(BookLanguage::class);
    }

    public function isLost(): bool
    {
        return $this->status === BookStatus::LOST;
    }

    public function isBorrowed(): bool
    {
        return $this->status === BookStatus::BORROWED;
    }
}
