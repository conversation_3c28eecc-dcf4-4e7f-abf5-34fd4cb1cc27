<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class DiscountSetting extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $fillable = ['basis', 'basis_amount', 'max_amount', 'used_amount', 'effective_from', 'effective_to',
        'description', 'gl_account_codes', 'source_type', 'source_id', 'userable_id', 'userable_type', 'is_active', 'created_by_employee_id'];

    const BASIS_PERCENT = 'PERCENT';
    const BASIS_FIXED_AMOUNT = 'FIXED';

    const ALL_BASIS = [
        self::BASIS_PERCENT,
        self::BASIS_FIXED_AMOUNT,
    ];

    protected $table = 'discount_settings';


    public function source() {
        return $this->morphTo();
    }

    public function userable() {
        return $this->morphTo();
    }

    public function createdByEmployee()
    {
        return $this->belongsTo(Employee::class, 'created_by_employee_id');
    }

    public function getGlAccountCodes() {
        return json_decode($this->gl_account_codes, true);
    }

    public function getSourceDescription() {
        if ( $this->source === null ){
            return '';
        }else{
            return $this->source->getDiscountDescription();
        }
    }

    public function canBeUsedForDate($apply_date) {

        $has_balance = true;
        $date_match = false;

        if ( $this->max_amount !== null && bccomp($this->used_amount, $this->max_amount, 2) >= 0 ){
            $has_balance = false;
        }
        if ( Carbon::parse($this->effective_from)->lte(Carbon::parse($apply_date)) && Carbon::parse($this->effective_to)->gte(Carbon::parse($apply_date)) ){
            $date_match = true;
        }

        return $this->is_active && $has_balance && $date_match;

    }

    public function calculateDiscountAmount($original_amount) {

        $discount_amount = 0;

        if ( $this->basis === DiscountSetting::BASIS_FIXED_AMOUNT ) {
            $discount_amount = $this->basis_amount;
        }
        else if ( $this->basis === DiscountSetting::BASIS_PERCENT ) {
            $discount_amount = round($original_amount * $this->basis_amount / 100, 2);
        }

        // max can discount up to line item value
        if ( $discount_amount > $original_amount ) {
            $discount_amount = $original_amount;
        }

        // if discount has max amount configured, need to limit discount to that amount
        if ( $this->max_amount !== null ) {
            $balance_applicable_discount = bcsub($this->max_amount, $this->used_amount, 2);

            if ( $balance_applicable_discount < 0 ) {
                $balance_applicable_discount = 0;
            }

            if ( $discount_amount > $balance_applicable_discount ) {
                $discount_amount = $balance_applicable_discount;
            }
        }

        $this->used_amount = bcadd($this->used_amount, $discount_amount, 2);

        return $discount_amount;

    }

    public function decreaseUsedAmount($amount) {
        return DiscountSetting::where('id', $this->id)->decrement('used_amount', $amount);
    }
}
