<?php

namespace App\Imports;

use App\Models\EnrollmentSession;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStartRow;

class EnrollmentPostPaymentImport implements WithMapping, WithStartRow, SkipsEmptyRows
{
    public function __construct(
        protected EnrollmentSession $enrollmentSession,
    ) {
    }

    public function map($row): array
    {
        // trim whitespaces from all cells
        $row = array_map(function($value) {
            if (is_bool($value)) {
                // dont trim boolean values from excel
                return $value ? 'TRUE' : 'FALSE';
            } else {
                return trim($value);
            }
        }, $row);

        $current_index = 0;

        $adjusted = [
            'number' => (string) mb_strtoupper($row[$current_index++]),
            'exam_slip_number' => (string) mb_strtoupper($row[$current_index++]),
            'student_name_en' => (string) mb_strtoupper($row[$current_index++]),
            'nric' => (string) mb_strtoupper($row[$current_index++]),
            'passport_number' => (string) mb_strtoupper($row[$current_index++]),
            'hostel' => $this->convertToBoolean($row[$current_index++]),
            'total_average' => (string) $row[$current_index++],
        ];

        $enrollment_session = $this->enrollmentSession->loadMissing(['examSubjects']);

        // Add subject fields
        foreach ($enrollment_session->getExamSubjectsSortedBySubjectCode() as $subject) {
            $adjusted[$subject->code] = (string) $row[$current_index++];
        }

        $adjusted['status'] = (string) $row[$current_index++];

        // if ($current_index++ !== count($row)) {
        //     throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::ENROLLMENT_ERROR, 5017), 5017);
        // }

        return $adjusted;
    }

    public function startRow(): int
    {
        return 2;
    }

    /**
     * Convert Excel value to boolean.
     */
    private function convertToBoolean($value): mixed
    {
        $truthy['1'] = true;
        $truthy['true'] = true;
        $truthy['TRUE'] = true;
        $truthy['yes'] = true;
        $truthy['on'] = true;

        $falsy['0'] = true;
        $falsy['false'] = true;
        $falsy['FALSE'] = true;
        $falsy['no'] = true;
        $falsy['off'] = true;

        $value = strtolower(trim((string) $value));

        if (isset($truthy[$value])) {
            return true;
        } elseif (isset($falsy[$value])) {
            return false;
        } else {
            return $value;
        }
    }
}
