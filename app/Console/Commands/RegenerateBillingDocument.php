<?php

namespace App\Console\Commands;

use App\Models\BillingDocument;
use App\Services\DocumentPrintService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RegenerateBillingDocument extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billing:regenerate-document {ids* : The IDs of the billing documents to regenerate (space-separated)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate billing document receipt PDFs for one or more documents';

    /**
     * Create a new command instance.
     */
    public function __construct(
        protected DocumentPrintService $documentPrintService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $ids = $this->argument('ids');

        if (empty($ids)) {
            $this->error('No billing document IDs provided.');
            return 1;
        }

        $successful_count = 0;
        $failed_count = 0;
        $not_found_count = 0;

        $this->info("Processing " . count($ids) . " billing document(s)...");
        $this->newLine();

        foreach ($ids as $id) {
            $billing_document = BillingDocument::find($id);

            if (!$billing_document) {
                $this->error("Billing document with ID {$id} not found.");
                $not_found_count++;
                continue;
            }

            $success = $this->regenerateBillingDocument($billing_document);

            if ($success) {
                $this->info("✓ Successfully regenerated billing document with ID {$id}.");
                $successful_count++;
            } else {
                $this->error("✗ Failed to regenerate billing document with ID {$id}.");
                $failed_count++;
            }

            $this->newLine();
        }

        // Summary
        $this->info("=== Summary ===");
        $this->info("Successful: {$successful_count}");
        if ($failed_count > 0) {
            $this->error("Failed: {$failed_count}");
        }
        if ($not_found_count > 0) {
            $this->error("Not found: {$not_found_count}");
        }

        return ($failed_count > 0 || $not_found_count > 0) ? 1 : 0;
    }

    /**
     * Regenerate the billing document receipt PDF.
     * This reuses the functionality from App\Listeners\GenerateAndSendReceipt.
     *
     * @param BillingDocument $billingDocument
     * @return bool Returns true if successful, false if failed
     */
    protected function regenerateBillingDocument(BillingDocument $billingDocument): bool
    {
        $this->info("Regenerating billing document: {$billingDocument->reference_no}");
        $this->info("Old receipt URL: {$billingDocument->receipt_url}");

        try {
            $receipt_pdf_url = $this->documentPrintService->setPrintable($billingDocument)
                ->generate()
                ->upload()
                ->getFileUrl();

            $billingDocument->receipt_url = $receipt_pdf_url;
            $billingDocument->save();

            $this->info("New receipt URL: {$receipt_pdf_url}");
            return true;
        } catch (\Exception $e) {
            $this->error("Error regenerating billing document: {$e->getMessage()}");
            Log::error("Error regenerating billing document: {$e->getMessage()}", [
                'billing_document_id' => $billingDocument->id,
                'exception' => $e,
            ]);
            return false;
        }
    }
}
