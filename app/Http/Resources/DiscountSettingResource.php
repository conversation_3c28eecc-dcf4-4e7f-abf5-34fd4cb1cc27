<?php

namespace App\Http\Resources;

use App\Models\ScholarshipAward;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class DiscountSettingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'basis' => $this->basis,
            'basis_amount' => (float) $this->basis_amount,
            'max_amount' => (float) $this->max_amount,
            'used_amount' => (float) $this->used_amount,
            'effective_from' => $this->effective_from,
            'effective_to' => $this->effective_to,
            'description' => $this->description,
            'gl_account_codes' => json_decode($this->gl_account_codes, true),
            'is_active' => (bool) $this->is_active,
            'created_at' => Carbon::parse($this->created_at, 'UTC')->tz(config('school.timezone'))->toIso8601String(),
            'source' => $this->whenLoaded('source', function () {
                if ($this->source_type === ScholarshipAward::class) {
                    return new ScholarshipAwardResource($this->source);
                }
                return null;
            }),
            'userable' => $this->whenLoaded('userable', function () {
                return new UserableResource($this->userable);
            }),
            'created_by' => $this->whenLoaded('createdByEmployee', function () {
                return new SimpleEmployeeResource($this->createdByEmployee);
            }),
        ];
    }
}
