<?php

namespace App\Http\Requests\Api\Enrollment;

use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class EnrollmentAdminIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name' => ['nullable', 'string'],
            'admission_grade_id' => ['nullable', 'exists:master_grades,id'],
            'nationality_id' => ['nullable', 'exists:master_countries,id'],
            'race_id' => ['nullable', 'exists:master_races,id'],
            'religion_id' => ['nullable', 'exists:master_religions,id'],
            'birth_cert_number' => ['nullable'],
            'nric' => ['nullable'],
            'passport_number' => ['nullable'],
            'gender' => ['nullable'],
            'enrollment_status' => ['nullable', Rule::in(EnrollmentStatus::values())], // APPROVED, REJECTED, SHORTLISTED;
            'payment_status' => ['nullable', Rule::in(EnrollmentPaymentStatus::values())], // PAID, UNPAID, PENDING;
            'is_hostel' => ['nullable', 'boolean'],
            'is_foreigner' => ['nullable', 'boolean'],
            'registration_date' => ['nullable', 'date'],
            'expiry_date' => ['nullable', 'date'],
            'enrollment_user_id' => ['nullable', 'exists:enrollment_users,id'],
            'guardian_name' => ['nullable', 'string'],
            'guardian_phone_number' => ['nullable', 'string'],
            'common_search' => ['nullable'],
        ]);
    }
}
