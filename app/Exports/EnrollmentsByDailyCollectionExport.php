<?php

namespace App\Exports;

use App\Helpers\ReportHelper;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EnrollmentsByDailyCollectionExport implements
    FromCollection,
    WithHeadings,
    WithMapping,
    WithStyles,
    WithColumnWidths,
    WithEvents
{
    use Exportable, RegistersEventListeners;

    protected $data;
    public $products;
    public $paymentMethods;
    public $totalForProducts;
    public $summaryAmountForProducts;
    public $summaryAmountForBankCharges;
    public $paymentDateFrom;
    public $paymentDateTo;

    public function __construct($report_data)
    {
        $this->data = $report_data['data'];
        $this->products = $report_data['products'];
        $this->paymentMethods = $report_data['payment_methods'];
        $this->totalForProducts = $report_data['total_for_products'];
        $this->summaryAmountForProducts = $report_data['summary_amount_for_products'];
        $this->summaryAmountForBankCharges = $report_data['summary_amount_for_bank_charges'];
        $this->paymentDateFrom = $report_data['payment_date_from'];
        $this->paymentDateTo = $report_data['payment_date_to'];
    }

    public function collection(): \Illuminate\Support\Collection
    {
        $export_data = collect();
        $invoice_number = 1;

        foreach ($this->data as $invoice) {
            $row_count = count($invoice['payments']);

            for ($i = 0; $i < $row_count; $i++) {
                $row = [];

                // First row of each invoice gets the main invoice data
                if ($i === 0) {
                    $row['no'] = $invoice_number;
                    $row['payment_date'] = $invoice['payment_date'];
                    $row['invoice_date'] = $invoice['document_date'];
                    $row['invoice_no'] = $invoice['reference_no'];
                    $row['bill_to_name'] = $invoice['bill_to_name'];
                    $row['bill_to_reference_no'] = $invoice['bill_to_reference_number'];


                    foreach ($this->products as $product_name => $total) {
                        if (isset($invoice['products'][$product_name])) {
                            $row[$product_name] = $invoice['products'][$product_name];
                        } else {
                            $row[$product_name] = '';
                        }
                    }

                    $row['total_amount'] = $invoice['total_amount'] == 0 ? '0' : $invoice['total_amount'];
                    $row['bank_charges'] = $invoice['bank_charges'] == 0 ? '0' : $invoice['bank_charges'];
                } else {
                    // Subsequent rows are empty for merged cells
                    $row['no'] = '';
                    $row['payment_date'] = '';
                    $row['invoice_date'] = '';
                    $row['invoice_no'] = '';
                    $row['bill_to_name'] = '';
                    $row['bill_to_reference_no'] = '';

                    foreach ($this->products as $product_name => $total) {
                        $row[$product_name] = '';
                    }

                    $row['total_amount'] = '';
                    $row['bank_charges'] = '';
                }

                // HANDLE PAYMENT DETAILS
                $payment = $invoice['payments'][$i];

                $row['payment'] = $payment['payment_method'];
                $row['reference_no'] = $payment['payment_reference_no'];

                $row['bank'] = '';
                if (!empty($payment['bank']) && !empty($payment['bank_swift_code'])) {
                    $row['bank'] = $payment['bank'] . ' (' . $payment['bank_swift_code'] . ')';
                }

                $export_data->push($row);
            }

            $invoice_number++;
        }

        // summary row at the end
        $summary_row = [];
        $summary_row['no'] = '';
        $summary_row['payment_date'] = '';
        $summary_row['invoice_date'] = '';
        $summary_row['invoice_no'] = '';
        $summary_row['bill_to_name'] = '';
        $summary_row['bill_to_reference_no'] = __('general.total');

        foreach ($this->products as $product_name => $total) {
            $summary_row[$product_name] = $total;
        }

        $summary_row['total_amount'] = $this->totalForProducts;
        $summary_row['bank_charges'] = $this->summaryAmountForBankCharges;

        $export_data->push($summary_row);

        return $export_data;
    }

    public function headings(): array
    {
        $headings = [];

        $headings[] = __('general.no');
        $headings[] = __('general.payment_date');
        $headings[] = __('general.invoice_date');
        $headings[] = __('general.invoice_no');
        $headings[] = __('general.bill_to_name');
        $headings[] = __('general.bill_to_reference_no');

        foreach ($this->products as $product_name => $total) {
            $headings[] = $product_name;
        }

        $headings[] = __('general.total_amount');
        $headings[] = __('general.bank_charges');
        $headings[] = __('general.payment');
        $headings[] = __('general.reference_no');
        $headings[] = __('general.bank');

        return $headings;
    }

    public function map($row): array
    {
        $mapped = [];

        $mapped['no'] = $row['no'] ?? '';
        $mapped['payment_date'] = $row['payment_date'] ?? '';
        $mapped['invoice_date'] = $row['invoice_date'] ?? '';
        $mapped['invoice_no'] = $row['invoice_no'] ?? '';
        $mapped['bill_to_name'] = $row['bill_to_name'] ?? '';
        $mapped['bill_to_reference_no'] = $row['bill_to_reference_no'] ?? '';

        foreach ($this->products as $product_name => $total) {
            if (isset($row[$product_name])) {
                $mapped[$product_name] = $row[$product_name];
            } else {
                $mapped[$product_name] = '';
            }
        }

        $mapped['total_amount'] = $row['total_amount'] ?? '';
        $mapped['bank_charges'] = $row['bank_charges'] ?? '';
        $mapped['payment'] = $row['payment'] ?? '';
        $mapped['reference_no'] = $row['reference_no'] ?? '';
        $mapped['bank'] = $row['bank'] ?? '';

        return $mapped;
    }

    public function styles(Worksheet $sheet)
    {
        $last_column = $sheet->getHighestColumn();
        $last_row = $sheet->getHighestRow();

        // DEFAULT LEFT ALIGNMENT FOR ALL COLUMNS
        $sheet->getStyle('A1:' . $last_column . $last_row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_LEFT)
            ->setVertical(Alignment::VERTICAL_CENTER)
            ->setWrapText(true);

        // ADD BORDERS TO ALL DATA CELLS
        $data_range = 'A1:' . $last_column . $last_row;
        $sheet->getStyle($data_range)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);

        return [];
    }

    public function columnWidths(): array
    {
        $column_widths = [
            'A' => 5,    // No
            'B' => 13,   // Payment Date
            'C' => 13,   // Invoice Date
            'D' => 20,   // Invoice No
            'E' => 25,   // Bill To Name
            'F' => 20,   // Bill To Reference No
        ];

        $current_column = 'G';

        foreach ($this->products as $product_name => $total) {
            $column_widths[$current_column++] = 23; // Product columns
        }
        $column_widths[$current_column++] = 15; // Total Amount
        $column_widths[$current_column++] = 15; // Bank Charges
        $column_widths[$current_column++] = 20; // Payment
        $column_widths[$current_column++] = 30; // Reference No
        $column_widths[$current_column++] = 30; // Bank

        return $column_widths;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $worksheet = $event->sheet->getDelegate();
                $last_column = $worksheet->getHighestColumn();

                // INSERT TITLE AND PRINT_DATE ROWS
                $this->insertTitleRow($event, $worksheet, $last_column);

                // STYLE HEADER ROW TO BE BOLD
                $event->sheet->getStyle('A3:' . $last_column . '3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                // MERGE CELLS FOR INVOICES WITH MULTIPLE PAYMENTS
                $this->mergeCellsForMultiplePayments($worksheet);

                // DEFAULT CENTER ALIGNMENT FOR PRODUCTS + TOTAL_AMOUNT + BANK_CHARGES COLUMNS
                $last_row = $worksheet->getHighestRow();

                $product_columns = [];
                $current_column = 'G';

                foreach ($this->products as $product_name => $total) {
                    $product_columns[] = $current_column;

                    $event->sheet->getStyle($current_column . '3:' . $current_column . $last_row) // EACH PRODUCT COLUMN
                        ->getAlignment()
                        ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                    $current_column++;
                }

                $event->sheet->getStyle($current_column . '3:' . $current_column . $last_row) // TOTAL_AMOUNT COLUMN
                    ->getAlignment()
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);
                $current_column++;

                $event->sheet->getStyle($current_column . '3:' . $current_column . $last_row) // BANK_CHARGES COLUMN
                    ->getAlignment()
                    ->setHorizontal(Alignment::HORIZONTAL_CENTER);

                // STYLE PRODUCTS HEADER TO BE GREY
                if (!empty($product_columns)) {
                    $first_product_column = $product_columns[0];
                    $last_product_column = $product_columns[array_key_last($product_columns)];
                    $product_header_range = "{$first_product_column}3:{$last_product_column}3";

                    $event->sheet->getStyle($product_header_range)->applyFromArray([
                        'fill' => [
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'startColor' => [
                                'rgb' => 'DDDDDD',
                            ],
                        ],
                    ]);
                }

                // STYLE LAST ROW (SUMMARY/TOTAL ROW) TO BE BOLD
                $event->sheet->getStyle('A' . $last_row . ':' . $last_column . $last_row)->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                // INSERT PAYMENT METHODS SUMMARY
                $this->addPaymentMethodSummary($event, $worksheet, $last_row, $last_column);
            },
        ];
    }

    public function startCell(): string
    {
        return 'A4';
    }

    private function insertTitleRow(AfterSheet $event, Worksheet $worksheet, $last_column)
    {
        // INSERT EXTRA ROWS FOR TITLE AND PRINT DATE
        $worksheet->insertNewRowBefore(1, 2);

        // MERGE CELLS FOR TITLE
        $worksheet->mergeCells('A1:' . $last_column . '1');
        $worksheet->setCellValue('A1', __('billing.daily_collection_report', [
            'payment_date_from' => $this->paymentDateFrom,
            'payment_date_to' => $this->paymentDateTo
        ]));

        // MERGE CELLS FOR PRINT DATE
        $worksheet->mergeCells('A2:' . $last_column . '2');
        $worksheet->setCellValue('A2', __('general.print_date') . ': ' . ReportHelper::getPrintDate());

        // STYLE TITLE & PRINT_DATE TO BE BOLD & CENTRE
        $titleStyle = [
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ];

        $event->sheet->getStyle('A1:' . $last_column . '1')->applyFromArray($titleStyle);
        $event->sheet->getStyle('A2:' . $last_column . '2')->applyFromArray($titleStyle);
    }

    private function mergeCellsForMultiplePayments(Worksheet $worksheet)
    {
        $row_counter = 4; // Start from row 4 (after headers)

        foreach ($this->data as $invoice) {
            $payment_count = count($invoice['payments']);

            if ($payment_count > 1) { // only for invoices with multiple payments
                $first_row = $row_counter;
                $last_row = $row_counter + $payment_count - 1;

                // MERGE INVOICE DETAIL COLUMNS (A-F) (No, Payment Date, Invoice Date, Invoice No, Bill To Name, Bill To Reference No)
                for ($col = 'A'; $col <= 'F'; $col++) {
                    $worksheet->mergeCells($col . $first_row . ':' . $col . $last_row);
                }

                // MERGE PRODUCT COLUMNS (G onwards - dynamic)
                $current_col = 'G';
                foreach ($this->products as $product_name => $total) {
                    $worksheet->mergeCells($current_col . $first_row . ':' . $current_col . $last_row);
                    $current_col++;
                }

                // MERGE TOTAL_AMOUNT AND BANK_CHARGES COLUMNS
                $worksheet->mergeCells($current_col . $first_row . ':' . $current_col . $last_row); // Total Amount
                $current_col++;
                $worksheet->mergeCells($current_col . $first_row . ':' . $current_col . $last_row); // Bank Charges
            }

            // Move row counter to the next invoice
            $row_counter += $payment_count;
        }
    }

    private function addPaymentMethodSummary(AfterSheet $event, Worksheet $worksheet, $last_row, $last_column)
    {
        $base_style = [
            'font' => ['bold' => true],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];
        $left_aligned_style = array_merge($base_style, [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT],
        ]);

        $right_aligned_style = array_merge($base_style, [
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT],
        ]);

        $current_row = $last_row + 2; // Skip one row after total

        // GET MIDDLE COLUMN FOR PAYMENT METHODS TO PUT SUMMARY CENTERED
        $total_columns = Coordinate::columnIndexFromString($last_column);
        $mid_column = intval($total_columns / 2);
        $method_column = Coordinate::stringFromColumnIndex($mid_column);
        $amount_column = Coordinate::stringFromColumnIndex($mid_column + 1);

        foreach ($this->paymentMethods as $method => $amount) {
            // ADD PAYMENT METHOD SUMMARY ROW + STYLING
            $worksheet->setCellValue($method_column . $current_row, $method);
            $worksheet->setCellValue($amount_column . $current_row, $amount);
            $event->sheet->getStyle($method_column . $current_row)->applyFromArray($left_aligned_style);
            $event->sheet->getStyle($amount_column . $current_row)->applyFromArray($right_aligned_style);

            $current_row++;
        }

        // ADD TOTAL AMOUNT SUMMARY ROW + STYLING
        $worksheet->setCellValue($method_column . $current_row, __('general.total_amount'));
        $worksheet->setCellValue($amount_column . $current_row, $this->summaryAmountForProducts);
        $event->sheet->getStyle($method_column . $current_row)->applyFromArray($left_aligned_style);
        $event->sheet->getStyle($amount_column . $current_row)->applyFromArray($right_aligned_style);

        $current_row++;

        // ADD BANK CHARGES SUMMARY ROW + STYLING
        $worksheet->setCellValue($method_column . $current_row, __('general.bank_charges'));
        $worksheet->setCellValue($amount_column . $current_row, $this->summaryAmountForBankCharges);
        $event->sheet->getStyle($method_column . $current_row)->applyFromArray($left_aligned_style);
        $event->sheet->getStyle($amount_column . $current_row)->applyFromArray($right_aligned_style);
    }
}
